# Created by https://www.toptal.com/developers/gitignore/api/go,dotenv
# Edit at https://www.toptal.com/developers/gitignore?templates=go,dotenv

### dotenv ###
.env

### Go ###
# If you prefer the allow list template instead of the deny list, see community template:
# https://github.com/github/gitignore/blob/main/community/Golang/Go.AllowList.gitignore
#
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Air (hot reloading) temporary files
tmp/
build-errors.log

# Build artifacts
bin/

# End of https://www.toptal.com/developers/gitignore/api/go,dotenv