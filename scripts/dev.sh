#!/bin/bash

# Development script for hot reloading

echo "🚀 Starting development environment with hot reloading..."

# Check if Air is installed
if ! command -v air &> /dev/null; then
    echo "📦 Installing Air..."
    go install github.com/air-verse/air@latest
fi

# Check if frontend dependencies are installed
if [ ! -d "web/node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    cd web
    npm ci
    cd ..
fi

# Build frontend first
echo "🏗️  Building frontend..."
cd web
npm run build
cd ..

# Create tmp directory if it doesn't exist
mkdir -p tmp

# Start Air for hot reloading
echo "🔥 Starting Air hot reloading..."
echo "📝 Backend will be available at: http://localhost:8080"
echo "🔍 Health check: http://localhost:8080/health"
echo "⚡ Hot reloading is enabled - edit Go files to see changes"
echo ""
air -c .air.toml
