#!/bin/bash

# Test script for hot reloading functionality

echo "🧪 Testing hot reloading functionality..."

# Check if server is running
if ! curl -s http://localhost:8080/health > /dev/null; then
    echo "❌ Server is not running. Please start it with 'make dev' first."
    exit 1
fi

echo "✅ Server is running"

# Get initial response
echo "📡 Getting initial health check response..."
INITIAL_RESPONSE=$(curl -s http://localhost:8080/health | jq -r '.timestamp')
echo "Initial timestamp: $INITIAL_RESPONSE"

echo ""
echo "🔥 Now modify the health check message in main.go and watch the server restart!"
echo "💡 You can change the 'message' field in the health check endpoint"
echo "🔍 Monitor the terminal where 'make dev' is running to see the restart"
echo ""
echo "📝 To test, try changing this line in main.go:"
echo '   "message":   "Admin system is running with hot reloading! 🔥",'
echo "to:"
echo '   "message":   "Hot reload test - server restarted! ⚡",'
echo ""
echo "🌐 Then check: http://localhost:8080/health"
echo "📊 Or run: curl http://localhost:8080/health | jq"
