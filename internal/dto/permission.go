package dto

// Permission management DTOs

type CreatePermissionRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	Resource    string `json:"resource" validate:"required,min=2,max=50"`
	Action      string `json:"action" validate:"required,min=2,max=50"`
	Description string `json:"description" validate:"max=500"`
}

type UpdatePermissionRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	Resource    string `json:"resource" validate:"required,min=2,max=50"`
	Action      string `json:"action" validate:"required,min=2,max=50"`
	Description string `json:"description" validate:"max=500"`
}
