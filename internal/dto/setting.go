package dto

import "xiaoxingcloud.com/admin/internal/services"

// Setting management DTOs

// CreateSettingRequest represents setting creation request
type CreateSettingRequest struct {
	Key         string `json:"key" validate:"required,min=2,max=100"`
	Value       string `json:"value" validate:"required"`
	Type        string `json:"type" validate:"required,oneof=string number boolean json"`
	Category    string `json:"category" validate:"required,min=2,max=50"`
	Description string `json:"description" validate:"max=500"`
	IsPublic    bool   `json:"is_public"`
	IsEditable  bool   `json:"is_editable"`
}

// UpdateSettingRequest represents setting update request
type UpdateSettingRequest struct {
	Value       string `json:"value" validate:"required"`
	Type        string `json:"type" validate:"required,oneof=string number boolean json"`
	Category    string `json:"category" validate:"required,min=2,max=50"`
	Description string `json:"description" validate:"max=500"`
	IsPublic    bool   `json:"is_public"`
	IsEditable  bool   `json:"is_editable"`
}

// BulkUpdateSettingsRequest represents bulk settings update request
type BulkUpdateSettingsRequest struct {
	Settings map[string]services.SettingInput `json:"settings" validate:"required"`
}
