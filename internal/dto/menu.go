package dto

// Menu management DTOs

// CreateMenuRequest represents menu creation request
type CreateMenuRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	Title       string `json:"title" validate:"required,min=2,max=100"`
	Icon        string `json:"icon" validate:"max=50"`
	Path        string `json:"path" validate:"max=200"`
	Component   string `json:"component" validate:"max=200"`
	ParentID    *uint  `json:"parent_id"`
	Sort        int    `json:"sort"`
	IsVisible   bool   `json:"is_visible"`
	IsEnabled   bool   `json:"is_enabled"`
	Permission  string `json:"permission" validate:"max=100"`
	MenuType    string `json:"menu_type" validate:"required,oneof=menu button link"`
	ExternalURL string `json:"external_url" validate:"max=500"`
	Target      string `json:"target" validate:"oneof=_self _blank _parent _top"`
}

// UpdateMenuRequest represents menu update request
type UpdateMenuRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	Title       string `json:"title" validate:"required,min=2,max=100"`
	Icon        string `json:"icon" validate:"max=50"`
	Path        string `json:"path" validate:"max=200"`
	Component   string `json:"component" validate:"max=200"`
	ParentID    *uint  `json:"parent_id"`
	Sort        int    `json:"sort"`
	IsVisible   bool   `json:"is_visible"`
	IsEnabled   bool   `json:"is_enabled"`
	Permission  string `json:"permission" validate:"max=100"`
	MenuType    string `json:"menu_type" validate:"required,oneof=menu button link"`
	ExternalURL string `json:"external_url" validate:"max=500"`
	Target      string `json:"target" validate:"oneof=_self _blank _parent _top"`
}
