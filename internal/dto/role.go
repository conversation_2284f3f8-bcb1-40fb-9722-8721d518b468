package dto

// Role management DTOs

type AssignPermissionRequest struct {
	PermissionID uint `json:"permission_id" validate:"required,min=1"`
}

// CreateRoleRequest represents role creation request
type CreateRoleRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	Description string `json:"description" validate:"max=500"`
	IsActive    bool   `json:"is_active"` // Pointer to distinguish between false and nil
}

// UpdateRoleRequest represents role update request
type UpdateRoleRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	Description string `json:"description" validate:"max=500"`
	IsActive    bool   `json:"is_active"` // Pointer to distinguish between false and nil
}

// RoleListResponse represents paginated role list
type RoleListResponse struct {
	Roles      []RoleResponse `json:"roles"`
	Pagination Pagination     `json:"pagination"`
}

// RoleSearchRequest represents role search parameters
type RoleSearchRequest struct {
	Query    string `json:"query" form:"query"`
	IsActive *bool  `json:"is_active" form:"is_active"`
	Page     int    `json:"page" form:"page" validate:"min=1"`
	Limit    int    `json:"limit" form:"limit" validate:"min=1,max=100"`
}
