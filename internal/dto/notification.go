package dto

import "time"

// Notification DTOs

// CreateNotificationRequest represents notification creation request
type CreateNotificationRequest struct {
	Title      string     `json:"title" validate:"required,min=1,max=200"`
	Content    string     `json:"content" validate:"required,min=1"`
	Type       string     `json:"type" validate:"oneof=info success warning error"`
	Category   string     `json:"category" validate:"required,min=1,max=50"`
	Priority   int        `json:"priority" validate:"min=0,max=3"`
	IsGlobal   bool       `json:"is_global"`
	ReceiverID *uint      `json:"receiver_id"`
	ActionURL  string     `json:"action_url" validate:"max=500"`
	ExpiresAt  *time.Time `json:"expires_at"`
}

// BroadcastNotificationRequest represents broadcast notification request
type BroadcastNotificationRequest struct {
	Title     string     `json:"title" validate:"required,min=1,max=200"`
	Content   string     `json:"content" validate:"required,min=1"`
	Type      string     `json:"type" validate:"oneof=info success warning error"`
	Category  string     `json:"category" validate:"required,min=1,max=50"`
	Priority  int        `json:"priority" validate:"min=0,max=3"`
	ActionURL string     `json:"action_url" validate:"max=500"`
	ExpiresAt *time.Time `json:"expires_at"`
}

// NotificationResponse represents notification data in responses
type NotificationResponse struct {
	ID        uint       `json:"id"`
	Title     string     `json:"title"`
	Content   string     `json:"content"`
	Type      string     `json:"type"`
	Category  string     `json:"category"`
	Priority  int        `json:"priority"`
	IsRead    bool       `json:"is_read"`
	IsGlobal  bool       `json:"is_global"`
	ActionURL string     `json:"action_url,omitempty"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	Sender    *UserInfo  `json:"sender,omitempty"`
	Receiver  *UserInfo  `json:"receiver,omitempty"`
}

// NotificationListResponse represents paginated notification list
type NotificationListResponse struct {
	Notifications []NotificationResponse `json:"notifications"`
	Pagination    Pagination             `json:"pagination"`
	UnreadOnly    bool                   `json:"unread_only"`
}

// NotificationStatsResponse represents notification statistics
type NotificationStatsResponse struct {
	TotalNotifications int64            `json:"total_notifications"`
	UnreadCount        int64            `json:"unread_count"`
	ReadCount          int64            `json:"read_count"`
	ByType             map[string]int64 `json:"by_type"`
	ByCategory         map[string]int64 `json:"by_category"`
	ByPriority         map[int]int64    `json:"by_priority"`
}

// NotificationSearchRequest represents notification search parameters
type NotificationSearchRequest struct {
	Type       string `json:"type" form:"type"`
	Category   string `json:"category" form:"category"`
	IsRead     *bool  `json:"is_read" form:"is_read"`
	UnreadOnly bool   `json:"unread_only" form:"unread_only"`
	Page       int    `json:"page" form:"page" validate:"min=1"`
	Limit      int    `json:"limit" form:"limit" validate:"min=1,max=100"`
}
