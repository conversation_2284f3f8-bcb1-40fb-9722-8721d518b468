package dto

import (
	"math"

	"xiaoxingcloud.com/admin/internal/models"
)

// Converter functions between models and DTOs

// ToUserResponse converts a User model to UserResponse DTO
func ToUserResponse(user *models.User) *UserResponse {
	if user == nil {
		return nil
	}

	response := &UserResponse{
		ID:        user.ID,
		Username:  user.Username,
		Email:     user.Email,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		IsActive:  user.IsActive,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
	}

	// Convert roles if present
	if len(user.Roles) > 0 {
		response.Roles = make([]RoleResponse, len(user.Roles))
		for i, role := range user.Roles {
			response.Roles[i] = *ToRoleResponse(&role)
		}
	}

	// Convert attributes if present
	if len(user.Attributes) > 0 {
		response.Attributes = make([]AttributeResponse, len(user.Attributes))
		for i, attr := range user.Attributes {
			response.Attributes[i] = *ToAttributeResponse(&attr)
		}
	}

	return response
}

// ToUserInfo converts a User model to UserInfo DTO (safe user info)
func ToUserInfo(user *models.User) *UserInfo {
	if user == nil {
		return nil
	}

	return &UserInfo{
		ID:        user.ID,
		Username:  user.Username,
		Email:     user.Email,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		IsActive:  user.IsActive,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
	}
}

// ToRoleResponse converts a Role model to RoleResponse DTO
func ToRoleResponse(role *models.Role) *RoleResponse {
	if role == nil {
		return nil
	}

	response := &RoleResponse{
		ID:          role.ID,
		Name:        role.Name,
		Description: role.Description,
		IsActive:    role.IsActive,
		CreatedAt:   role.CreatedAt,
		UpdatedAt:   role.UpdatedAt,
	}

	// Convert permissions if present
	if len(role.Permissions) > 0 {
		response.Permissions = make([]PermissionResponse, len(role.Permissions))
		for i, perm := range role.Permissions {
			response.Permissions[i] = *ToPermissionResponse(&perm)
		}
	}

	return response
}

// ToPermissionResponse converts a Permission model to PermissionResponse DTO
func ToPermissionResponse(permission *models.Permission) *PermissionResponse {
	if permission == nil {
		return nil
	}

	return &PermissionResponse{
		ID:          permission.ID,
		Name:        permission.Name,
		Resource:    permission.Resource,
		Action:      permission.Action,
		Description: permission.Description,
		CreatedAt:   permission.CreatedAt,
		UpdatedAt:   permission.UpdatedAt,
	}
}

// ToAttributeResponse converts an Attribute model to AttributeResponse DTO
func ToAttributeResponse(attribute *models.Attribute) *AttributeResponse {
	if attribute == nil {
		return nil
	}

	return &AttributeResponse{
		ID:        attribute.ID,
		Name:      attribute.Name,
		Value:     attribute.Value,
		Type:      attribute.Type,
		CreatedAt: attribute.CreatedAt,
		UpdatedAt: attribute.UpdatedAt,
	}
}

// ToNotificationResponse converts a Notification model to NotificationResponse DTO
func ToNotificationResponse(notification *models.Notification) *NotificationResponse {
	if notification == nil {
		return nil
	}

	response := &NotificationResponse{
		ID        : notification.ID,
		Title     : notification.Title,
		Content   : notification.Content,
		Type      : notification.Type,
		Category  : notification.Category,
		Priority  : notification.Priority,
		IsRead    : notification.IsRead,
		IsGlobal  : notification.IsGlobal,
		ActionURL : notification.ActionURL,
		ExpiresAt : notification.ExpiresAt,
		CreatedAt : notification.CreatedAt,
		UpdatedAt : notification.UpdatedAt,
	}

	// Convert sender and receiver if present
	if notification.Sender != nil {
		response.Sender = ToUserInfo(notification.Sender)
	}
	if notification.Receiver != nil {
		response.Receiver = ToUserInfo(notification.Receiver)
	}

	return response
}

// ToPagination creates a Pagination DTO
func ToPagination(page, limit int, total int64) Pagination {
	totalPages := int(math.Ceil(float64(total) / float64(limit)))
	if totalPages < 1 {
		totalPages = 1
	}

	return Pagination{
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: totalPages,
	}
}

// ToUserListResponse creates a UserListResponse DTO
func ToUserListResponse(users []models.User, page, limit int, total int64) *UserListResponse {
	userResponses := make([]UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = *ToUserResponse(&user)
	}

	return &UserListResponse{
		Users:      userResponses,
		Pagination: ToPagination(page, limit, total),
	}
}

// ToRoleListResponse creates a RoleListResponse DTO
func ToRoleListResponse(roles []models.Role, page, limit int, total int64) *RoleListResponse {
	roleResponses := make([]RoleResponse, len(roles))
	for i, role := range roles {
		roleResponses[i] = *ToRoleResponse(&role)
	}

	return &RoleListResponse{
		Roles:      roleResponses,
		Pagination: ToPagination(page, limit, total),
	}
}

// ToNotificationListResponse creates a NotificationListResponse DTO
func ToNotificationListResponse(notifications []models.Notification, page, limit int, total int64, unreadOnly bool) *NotificationListResponse {
	notificationResponses := make([]NotificationResponse, len(notifications))
	for i, notification := range notifications {
		notificationResponses[i] = *ToNotificationResponse(&notification)
	}

	return &NotificationListResponse{
		Notifications: notificationResponses,
		Pagination:    ToPagination(page, limit, total),
		UnreadOnly:    unreadOnly,
	}
}

// ToErrorResponse creates an ErrorResponse DTO
func ToErrorResponse(message string, code int, details map[string]string) *ErrorResponse {
	return &ErrorResponse{
		Error:   "Request failed",
		Message: message,
		Code:    code,
		Details: details,
	}
}

// ToSuccessResponse creates a SuccessResponse DTO
func ToSuccessResponse(message string, data interface{}) *SuccessResponse {
	return &SuccessResponse{
		Message: message,
		Data:    data,
	}
}

// ToMessageResponse creates a MessageResponse DTO
func ToMessageResponse(message string) *MessageResponse {
	return &MessageResponse{
		Message: message,
	}
}

// ToIDResponse creates an IDResponse DTO
func ToIDResponse(id uint) *IDResponse {
	return &IDResponse{
		ID: id,
	}
}
