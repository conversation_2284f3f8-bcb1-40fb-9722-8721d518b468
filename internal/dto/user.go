package dto

import "time"

// User management DTOs

// CreateUserRequest represents user creation request
type CreateUserRequest struct {
	Username  string `json:"username" validate:"required,min=3,max=50,alphanum"`
	Email     string `json:"email" validate:"required,email"`
	Password  string `json:"password" validate:"required,min=8"`
	FirstName string `json:"first_name" validate:"required,min=1,max=50"`
	LastName  string `json:"last_name" validate:"required,min=1,max=50"`
	IsActive  *bool  `json:"is_active"` // Pointer to distinguish between false and nil
}

// UpdateUserRequest represents user update request
type UpdateUserRequest struct {
	Username  string `json:"username,omitempty" validate:"omitempty,min=3,max=50"`
	Email     string `json:"email" validate:"omitempty,email"`
	FirstName string `json:"first_name" validate:"omitempty,min=1,max=50"`
	LastName  string `json:"last_name" validate:"omitempty,min=1,max=50"`
	IsActive  *bool  `json:"is_active"` // Pointer to distinguish between false and nil
}

// UserResponse represents user data in responses
type UserResponse struct {
	ID         uint                `json:"id"`
	Username   string              `json:"username"`
	Email      string              `json:"email"`
	FirstName  string              `json:"first_name"`
	LastName   string              `json:"last_name"`
	IsActive   bool                `json:"is_active"`
	CreatedAt  time.Time           `json:"created_at"`
	UpdatedAt  time.Time           `json:"updated_at"`
	Roles      []RoleResponse      `json:"roles,omitempty"`
	Attributes []AttributeResponse `json:"attributes,omitempty"`
}

// UserListResponse represents paginated user list
type UserListResponse struct {
	Users      []UserResponse `json:"users"`
	Pagination Pagination     `json:"pagination"`
}

// AssignRoleRequest represents role assignment request
type AssignRoleRequest struct {
	RoleIDs []uint `json:"role_ids" validate:"required,min=1"`
}

// AssignSingleRoleRequest represents single role assignment request
type AssignSingleRoleRequest struct {
	RoleID uint `json:"role_id" validate:"required"`
}

// UserSearchRequest represents user search parameters
type UserSearchRequest struct {
	Query    string `json:"query" form:"query"`
	IsActive *bool  `json:"is_active" form:"is_active"`
	Page     int    `json:"page" form:"page" validate:"min=1"`
	Limit    int    `json:"limit" form:"limit" validate:"min=1,max=100"`
}
