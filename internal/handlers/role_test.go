package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"xiaoxingcloud.com/admin/internal/dto"
	"xiaoxingcloud.com/admin/internal/models"
	"xiaoxingcloud.com/admin/pkg"
)

// MockRoleService for testing
type MockRoleService struct {
	mock.Mock
}

func (m *MockRoleService) CreateRole(name, description string) (*models.Role, error) {
	args := m.Called(name, description)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Role), args.Error(1)
}

func (m *MockRoleService) GetRoleByID(id uint) (*models.Role, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Role), args.Error(1)
}

func (m *MockRoleService) GetAllRoles(page, limit int) ([]models.Role, int64, error) {
	args := m.Called(page, limit)
	return args.Get(0).([]models.Role), args.Get(1).(int64), args.Error(2)
}

func (m *MockRoleService) UpdateRole(id uint, name, description string, isActive bool) (*models.Role, error) {
	args := m.Called(id, name, description, isActive)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Role), args.Error(1)
}

func (m *MockRoleService) DeleteRole(id uint) error {
	args := m.Called(id)
	return args.Error(0)
}

func (m *MockRoleService) AssignPermissionToRole(roleID, permissionID uint) error {
	args := m.Called(roleID, permissionID)
	return args.Error(0)
}

func (m *MockRoleService) RemovePermissionFromRole(roleID, permissionID uint) error {
	args := m.Called(roleID, permissionID)
	return args.Error(0)
}

func (m *MockRoleService) GetRolePermissions(roleID uint) ([]models.Permission, error) {
	args := m.Called(roleID)
	return args.Get(0).([]models.Permission), args.Error(1)
}

type RoleHandlerTestSuite struct {
	suite.Suite
	echo        *echo.Echo
	mockService *MockRoleService
	handler     *RoleHandler
}

func (suite *RoleHandlerTestSuite) SetupTest() {
	suite.echo = echo.New()
	suite.echo.Validator = pkg.NewValidator()
	suite.mockService = new(MockRoleService)
	suite.handler = NewRoleHandler(suite.mockService)
}

func (suite *RoleHandlerTestSuite) TestCreateRole() {
	// Test successful role creation
	reqBody := dto.CreateRoleRequest{
		Name:        "test_role",
		Description: "Test Role Description",
	}
	bodyBytes, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/roles", bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	expectedRole := &models.Role{
		ID:          1,
		Name:        "test_role",
		Description: "Test Role Description",
		IsActive:    true,
	}

	suite.mockService.On("CreateRole", "test_role", "Test Role Description").Return(expectedRole, nil)

	err := suite.handler.CreateRole(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusCreated, rec.Code)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "test_role", response["name"])
	assert.Equal(suite.T(), "Test Role Description", response["description"])

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *RoleHandlerTestSuite) TestGetRoles() {
	req := httptest.NewRequest(http.MethodGet, "/roles?page=1&limit=10", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	expectedRoles := []models.Role{
		{ID: 1, Name: "role1", Description: "Role 1", IsActive: true},
		{ID: 2, Name: "role2", Description: "Role 2", IsActive: true},
	}

	suite.mockService.On("GetAllRoles", 1, 10).Return(expectedRoles, int64(2), nil)

	err := suite.handler.GetRoles(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	roles := response["roles"].([]interface{})
	assert.Len(suite.T(), roles, 2)

	pagination := response["pagination"].(map[string]interface{})
	assert.Equal(suite.T(), float64(1), pagination["page"])
	assert.Equal(suite.T(), float64(10), pagination["limit"])
	assert.Equal(suite.T(), float64(2), pagination["total"])

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *RoleHandlerTestSuite) TestGetRole() {
	req := httptest.NewRequest(http.MethodGet, "/roles/1", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("1")

	expectedRole := &models.Role{
		ID:          1,
		Name:        "test_role",
		Description: "Test Role",
		IsActive:    true,
		Permissions: []models.Permission{
			{ID: 1, Name: "test.permission", Resource: "test", Action: "permission"},
		},
	}

	suite.mockService.On("GetRoleByID", uint(1)).Return(expectedRole, nil)

	err := suite.handler.GetRole(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response models.Role
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), uint(1), response.ID)
	assert.Equal(suite.T(), "test_role", response.Name)
	assert.Len(suite.T(), response.Permissions, 1)

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *RoleHandlerTestSuite) TestUpdateRole() {
	reqBody := dto.UpdateRoleRequest{
		Name:        "updated_role",
		Description: "Updated Description",
		IsActive:    false,
	}
	bodyBytes, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPut, "/roles/1", bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("1")

	expectedRole := &models.Role{
		ID:          1,
		Name:        "updated_role",
		Description: "Updated Description",
		IsActive:    false,
	}

	suite.mockService.On("UpdateRole", uint(1), "updated_role", "Updated Description", false).Return(expectedRole, nil)

	err := suite.handler.UpdateRole(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response models.Role
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "updated_role", response.Name)
	assert.Equal(suite.T(), "Updated Description", response.Description)
	assert.False(suite.T(), response.IsActive)

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *RoleHandlerTestSuite) TestDeleteRole() {
	req := httptest.NewRequest(http.MethodDelete, "/roles/1", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("1")

	suite.mockService.On("DeleteRole", uint(1)).Return(nil)

	err := suite.handler.DeleteRole(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "role deleted successfully", response["message"])

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *RoleHandlerTestSuite) TestAssignPermission() {
	reqBody := dto.AssignPermissionRequest{
		PermissionID: 1,
	}
	bodyBytes, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/roles/1/permissions", bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("1")

	suite.mockService.On("AssignPermissionToRole", uint(1), uint(1)).Return(nil)

	err := suite.handler.AssignPermission(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "permission assigned successfully", response["message"])

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *RoleHandlerTestSuite) TestRemovePermission() {
	req := httptest.NewRequest(http.MethodDelete, "/roles/1/permissions/1", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id", "permissionId")
	c.SetParamValues("1", "1")

	suite.mockService.On("RemovePermissionFromRole", uint(1), uint(1)).Return(nil)

	err := suite.handler.RemovePermission(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "permission removed successfully", response["message"])

	suite.mockService.AssertExpectations(suite.T())
}

func TestRoleHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(RoleHandlerTestSuite))
}
