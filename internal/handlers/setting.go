package handlers

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"xiaoxingcloud.com/admin/internal/dto"
	"xiaoxingcloud.com/admin/internal/models"
	"xiaoxingcloud.com/admin/internal/services"
)

type SettingHandler struct {
	settingService services.SettingServiceInterface
}

// NewSettingHandler creates a new setting handler
func NewSettingHandler(settingService services.SettingServiceInterface) *SettingHandler {
	return &SettingHandler{
		settingService: settingService,
	}
}

// CreateSetting creates a new system setting
// @Summary Create new setting
// @Description Create a new system setting with the provided information
// @Tags Settings
// @Accept json
// @Produce json
// @Param request body dto.CreateSettingRequest true "Setting creation data"
// @Success 201 {object} models.SystemSetting "Setting created successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid request"
// @Failure 409 {object} dto.ErrorResponse "Setting is not editable"
// @Security BearerAuth
// @Router /settings [post]
func (h *SettingHandler) CreateSetting(c echo.Context) error {
	var req dto.CreateSettingRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	err := h.settingService.SetSetting(req.Key, req.Value, req.Type, req.Category, req.Description, req.IsPublic, req.IsEditable)
	if err != nil {
		if err.Error() == "setting is not editable" {
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	// Return the created setting
	setting, err := h.settingService.GetSetting(req.Key)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusCreated, setting)
}

// GetSettings returns all system settings
// @Summary Get all settings
// @Description Get all system settings (admin only) or public settings
// @Tags Settings
// @Accept json
// @Produce json
// @Success 200 {object} object "Settings retrieved successfully"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /settings [get]
func (h *SettingHandler) GetSettings(c echo.Context) error {
	// Check if user is admin (this should be set by middleware)
	isAdmin := c.Get("is_admin")
	isAdminBool := false
	if isAdmin != nil {
		isAdminBool = isAdmin.(bool)
	}

	category := c.QueryParam("category")
	if category != "" {
		settings, err := h.settingService.GetSettingsByCategory(category)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
		}

		// Filter non-public settings for non-admin users
		if !isAdminBool {
			var publicSettings []models.SystemSetting
			for _, setting := range settings {
				if setting.IsPublic {
					publicSettings = append(publicSettings, setting)
				}
			}
			settings = publicSettings
		}

		return c.JSON(http.StatusOK, map[string]any{
			"settings": settings,
			"category": category,
		})
	}

	settings, err := h.settingService.GetAllSettings(isAdminBool)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"settings": settings,
	})
}

// GetSetting returns a specific system setting
// @Summary Get setting by key
// @Description Get a specific system setting by key
// @Tags Settings
// @Accept json
// @Produce json
// @Param key path string true "Setting key"
// @Success 200 {object} models.SystemSetting "Setting retrieved successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid setting key"
// @Failure 403 {object} dto.ErrorResponse "Access denied to private setting"
// @Failure 404 {object} dto.ErrorResponse "Setting not found"
// @Security BearerAuth
// @Router /settings/{key} [get]
func (h *SettingHandler) GetSetting(c echo.Context) error {
	key := c.Param("key")
	if key == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "setting key is required")
	}

	setting, err := h.settingService.GetSetting(key)
	if err != nil {
		if err.Error() == "setting not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	// Check if user can access this setting
	isAdmin := c.Get("is_admin")
	isAdminBool := false
	if isAdmin != nil {
		isAdminBool = isAdmin.(bool)
	}

	if !isAdminBool && !setting.IsPublic {
		return echo.NewHTTPError(http.StatusForbidden, "access denied to private setting")
	}

	return c.JSON(http.StatusOK, setting)
}

// UpdateSetting updates a system setting
// @Summary Update setting
// @Description Update a system setting by key
// @Tags Settings
// @Accept json
// @Produce json
// @Param key path string true "Setting key"
// @Param request body dto.UpdateSettingRequest true "Setting update data"
// @Success 200 {object} models.SystemSetting "Setting updated successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid request"
// @Failure 404 {object} dto.ErrorResponse "Setting not found"
// @Failure 409 {object} dto.ErrorResponse "Setting is not editable"
// @Security BearerAuth
// @Router /settings/{key} [put]
func (h *SettingHandler) UpdateSetting(c echo.Context) error {
	key := c.Param("key")
	if key == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "setting key is required")
	}

	var req dto.UpdateSettingRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	err := h.settingService.SetSetting(key, req.Value, req.Type, req.Category, req.Description, req.IsPublic, req.IsEditable)
	if err != nil {
		if err.Error() == "setting not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if err.Error() == "setting is not editable" {
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	// Return the updated setting
	setting, err := h.settingService.GetSetting(key)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, setting)
}

// DeleteSetting deletes a system setting
// @Summary Delete setting
// @Description Delete a system setting by key
// @Tags Settings
// @Accept json
// @Produce json
// @Param key path string true "Setting key"
// @Success 200 {object} dto.MessageResponse "Setting deleted successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid setting key"
// @Failure 404 {object} dto.ErrorResponse "Setting not found"
// @Failure 409 {object} dto.ErrorResponse "Setting is not editable"
// @Security BearerAuth
// @Router /settings/{key} [delete]
func (h *SettingHandler) DeleteSetting(c echo.Context) error {
	key := c.Param("key")
	if key == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "setting key is required")
	}

	err := h.settingService.DeleteSetting(key)
	if err != nil {
		if err.Error() == "setting not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if err.Error() == "setting is not editable" {
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "setting deleted successfully",
	})
}

// GetCategories returns all setting categories
// @Summary Get setting categories
// @Description Get all available setting categories
// @Tags Settings
// @Accept json
// @Produce json
// @Success 200 {object} object "Categories retrieved successfully"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /settings/categories [get]
func (h *SettingHandler) GetCategories(c echo.Context) error {
	categories, err := h.settingService.GetCategories()
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"categories": categories,
	})
}

// BulkUpdateSettings updates multiple settings at once
func (h *SettingHandler) BulkUpdateSettings(c echo.Context) error {
	var req dto.BulkUpdateSettingsRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	err := h.settingService.BulkSetSettings(req.Settings)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "settings updated successfully",
	})
}

// InitializeDefaults initializes default system settings
func (h *SettingHandler) InitializeDefaults(c echo.Context) error {
	err := h.settingService.InitializeDefaultSettings()
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "default settings initialized successfully",
	})
}

// GetPublicSettings returns only public settings (for non-authenticated users)
func (h *SettingHandler) GetPublicSettings(c echo.Context) error {
	settings, err := h.settingService.GetAllSettings(false) // false = non-admin
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"settings": settings,
	})
}
