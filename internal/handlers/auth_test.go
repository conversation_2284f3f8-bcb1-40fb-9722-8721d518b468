package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/auth"
	"xiaoxingcloud.com/admin/internal/config"
	"xiaoxingcloud.com/admin/internal/dto"
	"xiaoxingcloud.com/admin/internal/models"
	"xiaoxingcloud.com/admin/internal/services"
	"xiaoxingcloud.com/admin/pkg"
)

type AuthHandlerTestSuite struct {
	suite.Suite
	db          *gorm.DB
	echo        *echo.Echo
	handler     *AuthHandler
	userService *services.UserService
	jwtService  *auth.JWTService
	testUser    *models.User
}

func (suite *AuthHandlerTestSuite) SetupSuite() {
	// Setup in-memory database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Auto migrate
	err = db.AutoMigrate(
		&models.User{},
		&models.Role{},
		&models.Permission{},
		&models.AuditLog{},
	)
	suite.Require().NoError(err)

	suite.db = db

	// Setup services
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:      "test-secret-key-for-auth-testing",
			ExpireHours: 24,
		},
	}

	suite.userService = services.NewUserService(db)
	suite.jwtService = auth.NewJWTService(cfg)
	suite.handler = NewAuthHandler(suite.userService, suite.jwtService)

	// Setup Echo
	suite.echo = echo.New()
	suite.echo.Validator = pkg.NewValidator()
}

func (suite *AuthHandlerTestSuite) SetupTest() {
	// Clean up database
	suite.db.Exec("DELETE FROM users")

	// Create test user
	user, err := suite.userService.CreateUser("testuser", "<EMAIL>", "Password123!", "Test", "User")
	suite.Require().NoError(err)
	suite.testUser = user
}

func (suite *AuthHandlerTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *AuthHandlerTestSuite) TestLogin_Success() {
	loginReq := dto.LoginRequest{
		Identifier: "testuser",
		Password:   "Password123!",
	}

	reqBody, _ := json.Marshal(loginReq)
	req := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.Login(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response dto.LoginResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), response.Token)
	assert.Equal(suite.T(), "testuser", response.User.Username)
	assert.Equal(suite.T(), "<EMAIL>", response.User.Email)
}

func (suite *AuthHandlerTestSuite) TestLogin_WithEmail() {
	loginReq := dto.LoginRequest{
		Identifier: "<EMAIL>",
		Password:   "Password123!",
	}

	reqBody, _ := json.Marshal(loginReq)
	req := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.Login(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response dto.LoginResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), response.Token)
	assert.Equal(suite.T(), "testuser", response.User.Username)
}

func (suite *AuthHandlerTestSuite) TestLogin_InvalidCredentials() {
	loginReq := dto.LoginRequest{
		Identifier: "testuser",
		Password:   "wrongpassword",
	}

	reqBody, _ := json.Marshal(loginReq)
	req := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.Login(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusUnauthorized, httpErr.Code)
}

func (suite *AuthHandlerTestSuite) TestLogin_InvalidJSON() {
	req := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewBuffer([]byte("invalid json")))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.Login(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *AuthHandlerTestSuite) TestLogin_MissingFields() {
	loginReq := dto.LoginRequest{
		Identifier: "testuser",
		// Missing password
	}

	reqBody, _ := json.Marshal(loginReq)
	req := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.Login(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *AuthHandlerTestSuite) TestRegister_Success() {
	registerReq := dto.RegisterRequest{
		Username:  "newuser",
		Email:     "<EMAIL>",
		Password:  "Password123!",
		FirstName: "New",
		LastName:  "User",
	}

	reqBody, _ := json.Marshal(registerReq)
	req := httptest.NewRequest(http.MethodPost, "/auth/register", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.Register(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusCreated, rec.Code)

	var response dto.LoginResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), response.Token)
	assert.Equal(suite.T(), "newuser", response.User.Username)
	assert.Equal(suite.T(), "<EMAIL>", response.User.Email)
}

func (suite *AuthHandlerTestSuite) TestRegister_DuplicateUsername() {
	registerReq := dto.RegisterRequest{
		Username:  "testuser", // Already exists
		Email:     "<EMAIL>",
		Password:  "Password123!",
		FirstName: "Another",
		LastName:  "User",
	}

	reqBody, _ := json.Marshal(registerReq)
	req := httptest.NewRequest(http.MethodPost, "/auth/register", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.Register(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *AuthHandlerTestSuite) TestRegister_DuplicateEmail() {
	registerReq := dto.RegisterRequest{
		Username:  "anotheruser",
		Email:     "<EMAIL>", // Already exists
		Password:  "Password123!",
		FirstName: "Another",
		LastName:  "User",
	}

	reqBody, _ := json.Marshal(registerReq)
	req := httptest.NewRequest(http.MethodPost, "/auth/register", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.Register(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *AuthHandlerTestSuite) TestRegister_WeakPassword() {
	registerReq := dto.RegisterRequest{
		Username:  "weakuser",
		Email:     "<EMAIL>",
		Password:  "123", // Too weak
		FirstName: "Weak",
		LastName:  "User",
	}

	reqBody, _ := json.Marshal(registerReq)
	req := httptest.NewRequest(http.MethodPost, "/auth/register", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.Register(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *AuthHandlerTestSuite) TestGetProfile_Success() {
	req := httptest.NewRequest(http.MethodGet, "/auth/profile", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.Set("user_id", suite.testUser.ID)

	err := suite.handler.GetProfile(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "testuser", response["username"])
	assert.Equal(suite.T(), "<EMAIL>", response["email"])
	assert.Equal(suite.T(), "Test", response["first_name"])
	assert.Equal(suite.T(), "User", response["last_name"])
}

func (suite *AuthHandlerTestSuite) TestGetProfile_UserNotFound() {
	req := httptest.NewRequest(http.MethodGet, "/auth/profile", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.Set("user_id", uint(999)) // Non-existent user

	err := suite.handler.GetProfile(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusNotFound, httpErr.Code)
}

func (suite *AuthHandlerTestSuite) TestGetProfile_NoUserID() {
	req := httptest.NewRequest(http.MethodGet, "/auth/profile", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	// No user_id set in context

	err := suite.handler.GetProfile(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusUnauthorized, httpErr.Code)
}

func (suite *AuthHandlerTestSuite) TestLogout_Success() {
	req := httptest.NewRequest(http.MethodPost, "/auth/logout", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.Logout(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "logged out successfully", response["message"])
}

func (suite *AuthHandlerTestSuite) TestRefreshToken_Success() {
	// First generate a token
	token, err := suite.jwtService.GenerateToken(suite.testUser.ID, suite.testUser.Username, suite.testUser.Email)
	suite.Require().NoError(err)

	// Create a token that's close to expiry by manipulating time
	// For testing purposes, we'll test the endpoint directly
	req := httptest.NewRequest(http.MethodPost, "/auth/refresh", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err = suite.handler.RefreshToken(c)
	// This might fail due to token not being close to expiry, but we test the flow
	if err != nil {
		httpErr, ok := err.(*echo.HTTPError)
		assert.True(suite.T(), ok)
		// Could be 401 (invalid token) or 400 (not close to expiry)
		assert.Contains(suite.T(), []int{http.StatusUnauthorized, http.StatusBadRequest}, httpErr.Code)
	} else {
		assert.Equal(suite.T(), http.StatusOK, rec.Code)
	}
}

func (suite *AuthHandlerTestSuite) TestRefreshToken_NoToken() {
	req := httptest.NewRequest(http.MethodPost, "/auth/refresh", nil)
	// No Authorization header
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.RefreshToken(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusUnauthorized, httpErr.Code)
}

func (suite *AuthHandlerTestSuite) TestRefreshToken_InvalidToken() {
	req := httptest.NewRequest(http.MethodPost, "/auth/refresh", nil)
	req.Header.Set("Authorization", "Bearer invalid-token")
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.RefreshToken(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusUnauthorized, httpErr.Code)
}

func TestAuthHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(AuthHandlerTestSuite))
}
