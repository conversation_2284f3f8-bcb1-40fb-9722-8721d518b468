package handlers

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
	"xiaoxingcloud.com/admin/internal/dto"
	"xiaoxingcloud.com/admin/internal/services"
)

type <PERSON><PERSON><PERSON><PERSON><PERSON> struct {
	menuService services.MenuServiceInterface
}

// NewMenuHandler creates a new menu handler
func NewMenuHandler(menuService services.MenuServiceInterface) *MenuHandler {
	return &MenuHandler{
		menuService: menuService,
	}
}

// CreateMenu creates a new menu item
// @Summary Create new menu
// @Description Create a new menu item with the provided information
// @Tags Menus
// @Accept json
// @Produce json
// @Param request body dto.CreateMenuRequest true "Menu creation data"
// @Success 201 {object} models.Menu "Menu created successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid request"
// @Failure 409 {object} dto.ErrorResponse "Menu name already exists or parent not found"
// @Security BearerAuth
// @Router /menus [post]
func (h *<PERSON>uHandler) CreateMenu(c echo.Context) error {
	var req dto.CreateMenuRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	menu, err := h.menuService.CreateMenu(services.CreateMenuInput{
		Name:        req.Name,
		Title:       req.Title,
		Icon:        req.Icon,
		Path:        req.Path,
		Component:   req.Component,
		ParentID:    req.ParentID,
		Sort:        req.Sort,
		IsVisible:   req.IsVisible,
		IsEnabled:   req.IsEnabled,
		Permission:  req.Permission,
		MenuType:    req.MenuType,
		ExternalURL: req.ExternalURL,
		Target:      req.Target,
	})

	if err != nil {
		if err.Error() == "menu name already exists" || err.Error() == "parent menu not found" {
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusCreated, menu)
}

// GetMenus returns all menus
// @Summary Get all menus
// @Description Get menus in list or tree format, optionally filtered by visibility
// @Tags Menus
// @Accept json
// @Produce json
// @Param tree query bool false "Return as tree structure"
// @Param visible query bool false "Return only visible menus (when tree=true)"
// @Success 200 {object} object "Menus retrieved successfully"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /menus [get]
func (h *MenuHandler) GetMenus(c echo.Context) error {
	treeParam := c.QueryParam("tree")
	visibleParam := c.QueryParam("visible")

	if treeParam == "true" {
		if visibleParam == "true" {
			menus, err := h.menuService.GetVisibleMenuTree()
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
			}
			return c.JSON(http.StatusOK, map[string]any{
				"menus": menus,
				"type":  "visible_tree",
			})
		} else {
			menus, err := h.menuService.GetMenuTree()
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
			}
			return c.JSON(http.StatusOK, map[string]any{
				"menus": menus,
				"type":  "tree",
			})
		}
	}

	menus, err := h.menuService.GetAllMenus()
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"menus": menus,
		"type":  "list",
	})
}

// GetMenu returns a specific menu by ID
// @Summary Get menu by ID
// @Description Get detailed information about a specific menu item
// @Tags Menus
// @Accept json
// @Produce json
// @Param id path int true "Menu ID"
// @Success 200 {object} models.Menu "Menu retrieved successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid menu ID"
// @Failure 404 {object} dto.ErrorResponse "Menu not found"
// @Security BearerAuth
// @Router /menus/{id} [get]
func (h *MenuHandler) GetMenu(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid menu ID")
	}

	menu, err := h.menuService.GetMenuByID(uint(id))
	if err != nil {
		if err.Error() == "menu not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, menu)
}

// UpdateMenu updates a menu
// @Summary Update menu
// @Description Update menu information by ID
// @Tags Menus
// @Accept json
// @Produce json
// @Param id path int true "Menu ID"
// @Param request body dto.UpdateMenuRequest true "Menu update data"
// @Success 200 {object} models.Menu "Menu updated successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid request"
// @Failure 404 {object} dto.ErrorResponse "Menu not found"
// @Failure 409 {object} dto.ErrorResponse "Menu name already exists"
// @Security BearerAuth
// @Router /menus/{id} [put]
func (h *MenuHandler) UpdateMenu(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid menu ID")
	}

	var req dto.UpdateMenuRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	menu, err := h.menuService.UpdateMenu(uint(id), services.UpdateMenuInput{
		Name:        req.Name,
		Title:       req.Title,
		Icon:        req.Icon,
		Path:        req.Path,
		Component:   req.Component,
		ParentID:    req.ParentID,
		Sort:        req.Sort,
		IsVisible:   req.IsVisible,
		IsEnabled:   req.IsEnabled,
		Permission:  req.Permission,
		MenuType:    req.MenuType,
		ExternalURL: req.ExternalURL,
		Target:      req.Target,
	})

	if err != nil {
		if err.Error() == "menu not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if err.Error() == "menu name already exists" || err.Error() == "parent menu not found" || err.Error() == "menu cannot be its own parent" {
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, menu)
}

// DeleteMenu deletes a menu
// @Summary Delete menu
// @Description Delete a menu by ID (soft delete)
// @Tags Menus
// @Accept json
// @Produce json
// @Param id path int true "Menu ID"
// @Success 200 {object} dto.MessageResponse "Menu deleted successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid menu ID"
// @Failure 404 {object} dto.ErrorResponse "Menu not found"
// @Failure 409 {object} dto.ErrorResponse "Cannot delete menu with children"
// @Security BearerAuth
// @Router /menus/{id} [delete]
func (h *MenuHandler) DeleteMenu(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid menu ID")
	}

	err = h.menuService.DeleteMenu(uint(id))
	if err != nil {
		if err.Error() == "menu not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if err.Error() == "cannot delete menu with children" {
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "menu deleted successfully",
	})
}

// GetUserMenus returns menus that the current user has permission to access
// @Summary Get user accessible menus
// @Description Get menu tree that the current user has permission to access
// @Tags Menus
// @Accept json
// @Produce json
// @Success 200 {object} object "User menus retrieved successfully"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /menus/user [get]
func (h *MenuHandler) GetUserMenus(c echo.Context) error {
	// Get user permissions from context (should be set by middleware)
	permissions, ok := c.Get("user_permissions").([]string)
	if !ok {
		permissions = []string{}
	}

	menus, err := h.menuService.GetMenusByPermission(permissions)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"menus": menus,
	})
}

// InitializeDefaults initializes default system menus
// @Summary Initialize default menus
// @Description Initialize default system menus (admin only)
// @Tags Menus
// @Accept json
// @Produce json
// @Success 200 {object} dto.MessageResponse "Default menus initialized successfully"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /menus/initialize [post]
func (h *MenuHandler) InitializeDefaults(c echo.Context) error {
	err := h.menuService.InitializeDefaultMenus()
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "default menus initialized successfully",
	})
}
