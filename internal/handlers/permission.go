package handlers

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
	"xiaoxingcloud.com/admin/internal/dto"
	"xiaoxingcloud.com/admin/internal/services"
)

type PermissionHandler struct {
	permissionService services.PermissionServiceInterface
}

// NewPermissionHandler creates a new permission handler
func NewPermissionHandler(permissionService services.PermissionServiceInterface) *PermissionHandler {
	return &PermissionHandler{
		permissionService: permissionService,
	}
}

// CreatePermission creates a new permission
// @Summary Create new permission
// @Description Create a new permission with the provided information
// @Tags Permissions
// @Accept json
// @Produce json
// @Param request body dto.CreatePermissionRequest true "Permission creation data"
// @Success 201 {object} dto.PermissionResponse "Permission created successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid request"
// @Failure 409 {object} dto.ErrorResponse "Permission already exists"
// @Security BearerAuth
// @Router /permissions [post]
func (h *PermissionHandler) CreatePermission(c echo.Context) error {
	var req dto.CreatePermissionRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	permission, err := h.permissionService.CreatePermission(req.Name, req.Resource, req.Action, req.Description)
	if err != nil {
		if err.Error() == "permission name already exists" {
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusCreated, permission)
}

// GetPermissions returns all permissions with pagination
// @Summary Get all permissions
// @Description Get paginated list of permissions
// @Tags Permissions
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} object "Permissions retrieved successfully"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /permissions [get]
func (h *PermissionHandler) GetPermissions(c echo.Context) error {
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Check if search query is provided
	query := c.QueryParam("q")
	if query != "" {
		permissions, total, err := h.permissionService.SearchPermissions(query, page, limit)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
		}

		return c.JSON(http.StatusOK, map[string]any{
			"permissions": permissions,
			"pagination": map[string]any{
				"page":  page,
				"limit": limit,
				"total": total,
			},
			"query": query,
		})
	}

	permissions, total, err := h.permissionService.GetAllPermissions(page, limit)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"permissions": permissions,
		"pagination": map[string]any{
			"page":  page,
			"limit": limit,
			"total": total,
		},
	})
}

// GetPermission returns a specific permission by ID
// @Summary Get permission by ID
// @Description Get detailed information about a specific permission
// @Tags Permissions
// @Accept json
// @Produce json
// @Param id path int true "Permission ID"
// @Success 200 {object} dto.PermissionResponse "Permission retrieved successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid permission ID"
// @Failure 404 {object} dto.ErrorResponse "Permission not found"
// @Security BearerAuth
// @Router /permissions/{id} [get]
func (h *PermissionHandler) GetPermission(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid permission ID")
	}

	permission, err := h.permissionService.GetPermissionByID(uint(id))
	if err != nil {
		if err.Error() == "permission not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, permission)
}

// UpdatePermission updates a permission
// @Summary Update permission
// @Description Update permission information by ID
// @Tags Permissions
// @Accept json
// @Produce json
// @Param id path int true "Permission ID"
// @Param request body dto.UpdatePermissionRequest true "Permission update data"
// @Success 200 {object} dto.PermissionResponse "Permission updated successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid request"
// @Failure 404 {object} dto.ErrorResponse "Permission not found"
// @Failure 409 {object} dto.ErrorResponse "Permission name already exists"
// @Security BearerAuth
// @Router /permissions/{id} [put]
func (h *PermissionHandler) UpdatePermission(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid permission ID")
	}

	var req dto.UpdatePermissionRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	permission, err := h.permissionService.UpdatePermission(uint(id), req.Name, req.Resource, req.Action, req.Description)
	if err != nil {
		if err.Error() == "permission not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if err.Error() == "permission name already exists" {
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, permission)
}

// DeletePermission deletes a permission
// @Summary Delete permission
// @Description Delete a permission by ID (soft delete)
// @Tags Permissions
// @Accept json
// @Produce json
// @Param id path int true "Permission ID"
// @Success 200 {object} dto.MessageResponse "Permission deleted successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid permission ID"
// @Failure 404 {object} dto.ErrorResponse "Permission not found"
// @Failure 409 {object} dto.ErrorResponse "Cannot delete permission assigned to roles"
// @Security BearerAuth
// @Router /permissions/{id} [delete]
func (h *PermissionHandler) DeletePermission(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid permission ID")
	}

	err = h.permissionService.DeletePermission(uint(id))
	if err != nil {
		if err.Error() == "permission not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if err.Error() == "cannot delete permission that is assigned to roles" {
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "permission deleted successfully",
	})
}

// GetPermissionsByResource returns permissions for a specific resource
// @Summary Get permissions by resource
// @Description Get all permissions for a specific resource type
// @Tags Permissions
// @Accept json
// @Produce json
// @Param resource path string true "Resource name"
// @Success 200 {object} object "Permissions retrieved successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid resource parameter"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /permissions/resource/{resource} [get]
func (h *PermissionHandler) GetPermissionsByResource(c echo.Context) error {
	resource := c.Param("resource")
	if resource == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "resource parameter is required")
	}

	permissions, err := h.permissionService.GetPermissionsByResource(resource)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"resource":    resource,
		"permissions": permissions,
	})
}

// GetResourceList returns a list of all unique resources
// @Summary Get resource list
// @Description Get a list of all unique resource types
// @Tags Permissions
// @Accept json
// @Produce json
// @Success 200 {object} object "Resource list retrieved successfully"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /permissions/resources [get]
func (h *PermissionHandler) GetResourceList(c echo.Context) error {
	resources, err := h.permissionService.GetResourceList()
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"resources": resources,
	})
}

// GetPermissionsGrouped returns permissions grouped by resource
// @Summary Get permissions grouped by resource
// @Description Get all permissions organized by resource type
// @Tags Permissions
// @Accept json
// @Produce json
// @Success 200 {object} object "Grouped permissions retrieved successfully"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /permissions/grouped [get]
func (h *PermissionHandler) GetPermissionsGrouped(c echo.Context) error {
	grouped, err := h.permissionService.GetPermissionsByResourceGrouped()
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"permissions": grouped,
	})
}
