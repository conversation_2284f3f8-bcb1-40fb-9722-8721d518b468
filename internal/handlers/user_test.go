package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/dto"
	"xiaoxingcloud.com/admin/internal/models"
	"xiaoxingcloud.com/admin/internal/services"
	"xiaoxingcloud.com/admin/pkg"
)

type UserHandlerTestSuite struct {
	suite.Suite
	db          *gorm.DB
	echo        *echo.Echo
	handler     *UserHandler
	userService *services.UserService
	rbacService *services.RBACService
	testUser    *models.User
}

func (suite *UserHandlerTestSuite) SetupSuite() {
	// Setup in-memory database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Auto migrate
	err = db.AutoMigrate(
		&models.User{},
		&models.Role{},
		&models.Permission{},
		&models.UserRole{},
		&models.RolePermission{},
	)
	suite.Require().NoError(err)

	suite.db = db

	// Setup services
	suite.userService = services.NewUserService(db)
	suite.rbacService = services.NewRBACService(db)
	suite.handler = NewUserHandler(suite.userService, suite.rbacService)

	// Setup Echo
	suite.echo = echo.New()
	suite.echo.Validator = pkg.NewValidator()
}

func (suite *UserHandlerTestSuite) SetupTest() {
	// Clean up database
	suite.db.Exec("DELETE FROM users")
	suite.db.Exec("DELETE FROM roles")
	suite.db.Exec("DELETE FROM permissions")

	// Create test user
	user, err := suite.userService.CreateUser("testuser", "<EMAIL>", "Password123!", "Test", "User")
	suite.Require().NoError(err)
	suite.testUser = user
}

func (suite *UserHandlerTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *UserHandlerTestSuite) TestGetUsers_Success() {
	req := httptest.NewRequest(http.MethodGet, "/users", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.GetUsers(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	users := response["users"].([]any)
	assert.Len(suite.T(), users, 1)

	pagination := response["pagination"].(map[string]any)
	assert.Equal(suite.T(), float64(1), pagination["total"])
	assert.Equal(suite.T(), float64(1), pagination["page"])
	assert.Equal(suite.T(), float64(20), pagination["limit"])
}

func (suite *UserHandlerTestSuite) TestGetUsers_WithPagination() {
	// Create more test users
	for i := 2; i <= 5; i++ {
		_, err := suite.userService.CreateUser(
			fmt.Sprintf("user%d", i),
			fmt.Sprintf("<EMAIL>", i),
			"Password123!",
			fmt.Sprintf("User%d", i),
			"Test",
		)
		suite.Require().NoError(err)
	}

	req := httptest.NewRequest(http.MethodGet, "/users?page=1&limit=3", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.GetUsers(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	users := response["users"].([]any)
	assert.Len(suite.T(), users, 3)

	pagination := response["pagination"].(map[string]any)
	assert.Equal(suite.T(), float64(5), pagination["total"])
	assert.Equal(suite.T(), float64(1), pagination["page"])
	assert.Equal(suite.T(), float64(3), pagination["limit"])
}

func (suite *UserHandlerTestSuite) TestGetUsers_InvalidPagination() {
	req := httptest.NewRequest(http.MethodGet, "/users?page=invalid&limit=abc", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.GetUsers(c)
	assert.NoError(suite.T(), err) // Should use default values
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	pagination := response["pagination"].(map[string]any)
	assert.Equal(suite.T(), float64(1), pagination["page"])   // Default
	assert.Equal(suite.T(), float64(20), pagination["limit"]) // Default
}

func (suite *UserHandlerTestSuite) TestGetUser_Success() {
	userID := fmt.Sprintf("%d", suite.testUser.ID)
	req := httptest.NewRequest(http.MethodGet, "/users/"+userID, nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(userID)

	err := suite.handler.GetUser(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "testuser", response["username"])
	assert.Equal(suite.T(), "<EMAIL>", response["email"])
	assert.Equal(suite.T(), "Test", response["first_name"])
	assert.Equal(suite.T(), "User", response["last_name"])
}

func (suite *UserHandlerTestSuite) TestGetUser_InvalidID() {
	req := httptest.NewRequest(http.MethodGet, "/users/invalid", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("invalid")

	err := suite.handler.GetUser(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *UserHandlerTestSuite) TestGetUser_NotFound() {
	req := httptest.NewRequest(http.MethodGet, "/users/999", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("999")

	err := suite.handler.GetUser(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusNotFound, httpErr.Code)
}

func (suite *UserHandlerTestSuite) TestCreateUser_Success() {
	createReq := dto.CreateUserRequest{
		Username:  "newuser",
		Email:     "<EMAIL>",
		Password:  "Password123!",
		FirstName: "New",
		LastName:  "User",
	}

	reqBody, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/users", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.CreateUser(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusCreated, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "newuser", response["username"])
	assert.Equal(suite.T(), "<EMAIL>", response["email"])
	assert.Equal(suite.T(), "New", response["first_name"])
	assert.Equal(suite.T(), "User", response["last_name"])
	assert.Equal(suite.T(), true, response["is_active"])
}

func (suite *UserHandlerTestSuite) TestCreateUser_DuplicateUsername() {
	createReq := dto.CreateUserRequest{
		Username:  "testuser", // Already exists
		Email:     "<EMAIL>",
		Password:  "Password123!",
		FirstName: "Another",
		LastName:  "User",
	}

	reqBody, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/users", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.CreateUser(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *UserHandlerTestSuite) TestCreateUser_InvalidJSON() {
	req := httptest.NewRequest(http.MethodPost, "/users", bytes.NewBuffer([]byte("invalid json")))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.CreateUser(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *UserHandlerTestSuite) TestCreateUser_MissingFields() {
	createReq := dto.CreateUserRequest{
		Username: "incompleteuser",
		// Missing required fields
	}

	reqBody, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/users", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.CreateUser(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *UserHandlerTestSuite) TestUpdateUser_Success() {
	userID := fmt.Sprintf("%d", suite.testUser.ID)
	updateReq := dto.UpdateUserRequest{
		FirstName: "Updated",
		LastName:  "Name",
		IsActive:  boolPtr(true),
	}

	reqBody, _ := json.Marshal(updateReq)
	req := httptest.NewRequest(http.MethodPut, "/users/"+userID, bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(userID)

	err := suite.handler.UpdateUser(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "Updated", response["first_name"])
	assert.Equal(suite.T(), "Name", response["last_name"])
}

func (suite *UserHandlerTestSuite) TestUpdateUser_InvalidID() {
	updateReq := dto.UpdateUserRequest{
		FirstName: "Updated",
	}

	reqBody, _ := json.Marshal(updateReq)
	req := httptest.NewRequest(http.MethodPut, "/users/invalid", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("invalid")

	err := suite.handler.UpdateUser(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *UserHandlerTestSuite) TestDeleteUser_Success() {
	// Create another user to delete (don't delete the test user)
	userToDelete, err := suite.userService.CreateUser("deleteuser", "<EMAIL>", "Password123!", "Delete", "User")
	suite.Require().NoError(err)

	userID := fmt.Sprintf("%d", userToDelete.ID)
	req := httptest.NewRequest(http.MethodDelete, "/users/"+userID, nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(userID)

	err = suite.handler.DeleteUser(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "user deleted successfully", response["message"])
}

func (suite *UserHandlerTestSuite) TestDeleteUser_InvalidID() {
	req := httptest.NewRequest(http.MethodDelete, "/users/invalid", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("invalid")

	err := suite.handler.DeleteUser(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *UserHandlerTestSuite) TestDeleteUser_NotFound() {
	req := httptest.NewRequest(http.MethodDelete, "/users/999", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("999")

	err := suite.handler.DeleteUser(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *UserHandlerTestSuite) TestAssignRole_Success() {
	// Create a test role
	createdRole := models.Role{
		Name:        "test-role",
		Description: "Test Role",
		IsActive:    true,
	}
	err := suite.db.Create(&createdRole).Error
	suite.Require().NoError(err)

	assignReq := dto.AssignSingleRoleRequest{
		RoleID: createdRole.ID,
	}

	userID := fmt.Sprintf("%d", suite.testUser.ID)
	reqBody, _ := json.Marshal(assignReq)
	req := httptest.NewRequest(http.MethodPost, "/users/"+userID+"/roles", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(userID)

	err = suite.handler.AssignRole(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "role assigned successfully", response["message"])
}

// Helper functions
func boolPtr(b bool) *bool {
	return &b
}

func TestUserHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(UserHandlerTestSuite))
}
