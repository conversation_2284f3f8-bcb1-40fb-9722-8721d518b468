package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/dto"
	"xiaoxingcloud.com/admin/internal/models"
	"xiaoxingcloud.com/admin/internal/services"
	"xiaoxingcloud.com/admin/pkg"
)

type MenuHandlerTestSuite struct {
	suite.Suite
	db          *gorm.DB
	echo        *echo.Echo
	handler     *MenuHandler
	menuService *services.MenuService
	testMenu    *models.Menu
}

func (suite *MenuHandlerTestSuite) SetupSuite() {
	// Setup in-memory database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Auto migrate
	err = db.AutoMigrate(&models.Menu{})
	suite.Require().NoError(err)

	suite.db = db

	// Setup services
	suite.menuService = services.NewMenuService(db)
	suite.handler = NewMenuHandler(suite.menuService)

	// Setup Echo
	suite.echo = echo.New()
	suite.echo.Validator = pkg.NewValidator()
}

func (suite *MenuHandlerTestSuite) SetupTest() {
	// Clean up database
	suite.db.Exec("DELETE FROM menus")

	// Create test menu
	menu := &models.Menu{
		Name:      "test-menu",
		Title:     "Test Menu",
		Path:      "/test",
		Icon:      "test-icon",
		Sort:      1,
		IsVisible: true,
		IsEnabled: true,
		MenuType:  "menu",
	}
	err := suite.db.Create(menu).Error
	suite.Require().NoError(err)
	suite.testMenu = menu
}

func (suite *MenuHandlerTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *MenuHandlerTestSuite) TestCreateMenu_Success() {
	createReq := dto.CreateMenuRequest{
		Name:      "new-menu",
		Title:     "New Menu",
		Path:      "/new",
		Icon:      "new-icon",
		Sort:      2,
		IsVisible: true,
		IsEnabled: true,
		MenuType:  "menu",
	}

	reqBody, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/menus", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.CreateMenu(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusCreated, rec.Code)

	var response models.Menu
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "new-menu", response.Name)
	assert.Equal(suite.T(), "New Menu", response.Title)
	assert.Equal(suite.T(), "/new", response.Path)
	assert.Equal(suite.T(), "new-icon", response.Icon)
	assert.Equal(suite.T(), 2, response.Sort)
	assert.True(suite.T(), response.IsVisible)
	assert.True(suite.T(), response.IsEnabled)
	assert.Equal(suite.T(), "menu", response.MenuType)
}

func (suite *MenuHandlerTestSuite) TestCreateMenu_WithParent() {
	createReq := dto.CreateMenuRequest{
		Name:      "child-menu",
		Title:     "Child Menu",
		Path:      "/test/child",
		Icon:      "child-icon",
		ParentID:  &suite.testMenu.ID,
		Sort:      1,
		IsVisible: true,
		IsEnabled: true,
		MenuType:  "menu",
	}

	reqBody, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/menus", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.CreateMenu(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusCreated, rec.Code)

	var response models.Menu
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "child-menu", response.Name)
	assert.Equal(suite.T(), suite.testMenu.ID, *response.ParentID)
}

func (suite *MenuHandlerTestSuite) TestCreateMenu_DuplicateName() {
	createReq := dto.CreateMenuRequest{
		Name:      "test-menu", // Already exists
		Title:     "Duplicate Menu",
		Path:      "/duplicate",
		Icon:      "duplicate-icon",
		Sort:      3,
		IsVisible: true,
		IsEnabled: true,
		MenuType:  "menu",
	}

	reqBody, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/menus", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.CreateMenu(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusConflict, httpErr.Code)
}

func (suite *MenuHandlerTestSuite) TestCreateMenu_InvalidJSON() {
	req := httptest.NewRequest(http.MethodPost, "/menus", bytes.NewBuffer([]byte("invalid json")))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.CreateMenu(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *MenuHandlerTestSuite) TestCreateMenu_MissingFields() {
	createReq := dto.CreateMenuRequest{
		Name: "incomplete-menu",
		// Missing required fields
	}

	reqBody, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/menus", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.CreateMenu(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *MenuHandlerTestSuite) TestGetMenus_List() {
	req := httptest.NewRequest(http.MethodGet, "/menus", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.GetMenus(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "list", response["type"])

	menus := response["menus"].([]any)
	assert.Len(suite.T(), menus, 1)
}

func (suite *MenuHandlerTestSuite) TestGetMenus_Tree() {
	req := httptest.NewRequest(http.MethodGet, "/menus?tree=true", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.GetMenus(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "tree", response["type"])

	menus := response["menus"].([]any)
	assert.Len(suite.T(), menus, 1)
}

func (suite *MenuHandlerTestSuite) TestGetMenus_VisibleTree() {
	req := httptest.NewRequest(http.MethodGet, "/menus?tree=true&visible=true", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.GetMenus(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "visible_tree", response["type"])

	menus := response["menus"].([]any)
	assert.Len(suite.T(), menus, 1)
}

func (suite *MenuHandlerTestSuite) TestGetMenu_Success() {
	menuID := fmt.Sprintf("%d", suite.testMenu.ID)
	req := httptest.NewRequest(http.MethodGet, "/menus/"+menuID, nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(menuID)

	err := suite.handler.GetMenu(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response models.Menu
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "test-menu", response.Name)
	assert.Equal(suite.T(), "Test Menu", response.Title)
}

func (suite *MenuHandlerTestSuite) TestGetMenu_InvalidID() {
	req := httptest.NewRequest(http.MethodGet, "/menus/invalid", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("invalid")

	err := suite.handler.GetMenu(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *MenuHandlerTestSuite) TestGetMenu_NotFound() {
	req := httptest.NewRequest(http.MethodGet, "/menus/999", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("999")

	err := suite.handler.GetMenu(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusNotFound, httpErr.Code)
}

func TestMenuHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(MenuHandlerTestSuite))
}
