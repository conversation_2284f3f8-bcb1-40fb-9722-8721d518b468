package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/dto"
	"xiaoxingcloud.com/admin/internal/models"
	"xiaoxingcloud.com/admin/internal/services"
	"xiaoxingcloud.com/admin/pkg"
)

type NotificationHandlerTestSuite struct {
	suite.Suite
	db                  *gorm.DB
	echo                *echo.Echo
	handler             *NotificationHandler
	notificationService *services.NotificationService
	testUser            *models.User
	testNotification    *models.Notification
}

func (suite *NotificationHandlerTestSuite) SetupSuite() {
	// Setup in-memory database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Auto migrate
	err = db.AutoMigrate(
		&models.User{},
		&models.Notification{},
	)
	suite.Require().NoError(err)

	suite.db = db

	// Setup services
	suite.notificationService = services.NewNotificationService(db)
	suite.handler = NewNotificationHandler(suite.notificationService)

	// Setup Echo
	suite.echo = echo.New()
	suite.echo.Validator = pkg.NewValidator()
}

func (suite *NotificationHandlerTestSuite) SetupTest() {
	// Clean up database
	suite.db.Exec("DELETE FROM users")
	suite.db.Exec("DELETE FROM notifications")

	// Create test user
	testUser := &models.User{
		Username:  "testuser",
		Email:     "<EMAIL>",
		FirstName: "Test",
		LastName:  "User",
		IsActive:  true,
	}
	err := suite.db.Create(testUser).Error
	suite.Require().NoError(err)
	suite.testUser = testUser

	// Create test notification
	testNotification := &models.Notification{
		Title:      "Test Notification",
		Content:    "This is a test notification",
		Type:       "info",
		Category:   "user",
		Priority:   1,
		IsGlobal:   false,
		IsRead:     false,
		SenderID:   &testUser.ID,
		ReceiverID: &testUser.ID,
	}
	err = suite.db.Create(testNotification).Error
	suite.Require().NoError(err)
	suite.testNotification = testNotification
}

func (suite *NotificationHandlerTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *NotificationHandlerTestSuite) TestCreateNotification_Success() {
	createReq := dto.CreateNotificationRequest{
		Title:      "New Notification",
		Content:    "This is a new notification",
		Type:       "warning",
		Category:   "system",
		Priority:   2,
		IsGlobal:   false,
		ReceiverID: &suite.testUser.ID,
	}

	reqBody, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/notifications", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.Set("user_id", suite.testUser.ID)

	err := suite.handler.CreateNotification(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusCreated, rec.Code)

	var response models.Notification
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "New Notification", response.Title)
	assert.Equal(suite.T(), "This is a new notification", response.Content)
	assert.Equal(suite.T(), "warning", response.Type)
	assert.Equal(suite.T(), "system", response.Category)
	assert.Equal(suite.T(), 2, response.Priority)
	assert.False(suite.T(), response.IsGlobal)
}

func (suite *NotificationHandlerTestSuite) TestCreateNotification_GlobalNotification() {
	createReq := dto.CreateNotificationRequest{
		Title:    "Global Notification",
		Content:  "This is a global notification",
		Type:     "info",
		Category: "system",
		Priority: 1,
		IsGlobal: true,
	}

	reqBody, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/notifications", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.Set("user_id", suite.testUser.ID)

	err := suite.handler.CreateNotification(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusCreated, rec.Code)

	var response models.Notification
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "Global Notification", response.Title)
	assert.True(suite.T(), response.IsGlobal)
	assert.Nil(suite.T(), response.ReceiverID)
}

func (suite *NotificationHandlerTestSuite) TestCreateNotification_InvalidJSON() {
	req := httptest.NewRequest(http.MethodPost, "/notifications", bytes.NewBuffer([]byte("invalid json")))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.Set("user_id", suite.testUser.ID)

	err := suite.handler.CreateNotification(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *NotificationHandlerTestSuite) TestCreateNotification_MissingFields() {
	createReq := dto.CreateNotificationRequest{
		Title: "Incomplete Notification",
		// Missing required fields
	}

	reqBody, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/notifications", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.Set("user_id", suite.testUser.ID)

	err := suite.handler.CreateNotification(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *NotificationHandlerTestSuite) TestGetMyNotifications_Success() {
	req := httptest.NewRequest(http.MethodGet, "/notifications/my", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.Set("user_id", suite.testUser.ID)

	err := suite.handler.GetMyNotifications(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	notifications := response["notifications"].([]any)
	assert.Len(suite.T(), notifications, 1)

	pagination := response["pagination"].(map[string]any)
	assert.Equal(suite.T(), float64(1), pagination["total"])
	assert.Equal(suite.T(), float64(1), pagination["page"])
	assert.Equal(suite.T(), float64(20), pagination["limit"])
}

func (suite *NotificationHandlerTestSuite) TestGetMyNotifications_WithPagination() {
	req := httptest.NewRequest(http.MethodGet, "/notifications/my?page=1&limit=5", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.Set("user_id", suite.testUser.ID)

	err := suite.handler.GetMyNotifications(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	pagination := response["pagination"].(map[string]any)
	assert.Equal(suite.T(), float64(1), pagination["page"])
	assert.Equal(suite.T(), float64(5), pagination["limit"])
}

func (suite *NotificationHandlerTestSuite) TestGetMyNotifications_UnreadOnly() {
	req := httptest.NewRequest(http.MethodGet, "/notifications/my?unread=true", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.Set("user_id", suite.testUser.ID)

	err := suite.handler.GetMyNotifications(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	notifications := response["notifications"].([]any)
	assert.Len(suite.T(), notifications, 1) // Our test notification is unread

	pagination := response["pagination"].(map[string]any)
	assert.Equal(suite.T(), float64(1), pagination["total"])

	assert.Equal(suite.T(), true, response["unread_only"])
}

func (suite *NotificationHandlerTestSuite) TestMarkAsRead_Success() {
	notificationID := fmt.Sprintf("%d", suite.testNotification.ID)
	req := httptest.NewRequest(http.MethodPatch, "/notifications/"+notificationID+"/read", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(notificationID)
	c.Set("user_id", suite.testUser.ID)

	err := suite.handler.MarkAsRead(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "notification marked as read", response["message"])
}

func (suite *NotificationHandlerTestSuite) TestMarkAsRead_InvalidID() {
	req := httptest.NewRequest(http.MethodPatch, "/notifications/invalid/read", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("invalid")
	c.Set("user_id", suite.testUser.ID)

	err := suite.handler.MarkAsRead(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *NotificationHandlerTestSuite) TestMarkAllAsRead_Success() {
	req := httptest.NewRequest(http.MethodPatch, "/notifications/mark-all-read", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.Set("user_id", suite.testUser.ID)

	err := suite.handler.MarkAllAsRead(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "all notifications marked as read", response["message"])
	assert.Equal(suite.T(), float64(1), response["count"]) // Should mark 1 notification as read
}

func TestNotificationHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(NotificationHandlerTestSuite))
}
