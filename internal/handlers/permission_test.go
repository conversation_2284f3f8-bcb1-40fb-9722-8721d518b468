package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/dto"
	"xiaoxingcloud.com/admin/internal/models"
	"xiaoxingcloud.com/admin/internal/services"
	"xiaoxingcloud.com/admin/pkg"
)

type PermissionHandlerTestSuite struct {
	suite.Suite
	db                *gorm.DB
	echo              *echo.Echo
	handler           *PermissionHandler
	permissionService *services.PermissionService
	testPermission    *models.Permission
}

func (suite *PermissionHandlerTestSuite) SetupSuite() {
	// Setup in-memory database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Auto migrate
	err = db.AutoMigrate(&models.Permission{})
	suite.Require().NoError(err)

	suite.db = db

	// Setup services
	suite.permissionService = services.NewPermissionService(db)
	suite.handler = NewPermissionHandler(suite.permissionService)

	// Setup Echo
	suite.echo = echo.New()
	suite.echo.Validator = pkg.NewValidator()
}

func (suite *PermissionHandlerTestSuite) SetupTest() {
	// Clean up database
	suite.db.Exec("DELETE FROM permissions")

	// Create test permission
	permission, err := suite.permissionService.CreatePermission("test.read", "test", "read", "Test Read Permission")
	suite.Require().NoError(err)
	suite.testPermission = permission
}

func (suite *PermissionHandlerTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *PermissionHandlerTestSuite) TestCreatePermission_Success() {
	createReq := dto.CreatePermissionRequest{
		Name:        "user.create",
		Resource:    "user",
		Action:      "create",
		Description: "Create user permission",
	}

	reqBody, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/permissions", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.CreatePermission(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusCreated, rec.Code)

	var response models.Permission
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "user.create", response.Name)
	assert.Equal(suite.T(), "user", response.Resource)
	assert.Equal(suite.T(), "create", response.Action)
	assert.Equal(suite.T(), "Create user permission", response.Description)
}

func (suite *PermissionHandlerTestSuite) TestCreatePermission_DuplicateName() {
	createReq := dto.CreatePermissionRequest{
		Name:        "test.read", // Already exists
		Resource:    "test",
		Action:      "read",
		Description: "Duplicate permission",
	}

	reqBody, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/permissions", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.CreatePermission(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusConflict, httpErr.Code)
}

func (suite *PermissionHandlerTestSuite) TestCreatePermission_InvalidJSON() {
	req := httptest.NewRequest(http.MethodPost, "/permissions", bytes.NewBuffer([]byte("invalid json")))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.CreatePermission(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *PermissionHandlerTestSuite) TestCreatePermission_MissingFields() {
	createReq := dto.CreatePermissionRequest{
		Name: "incomplete.permission",
		// Missing required fields
	}

	reqBody, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/permissions", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.CreatePermission(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *PermissionHandlerTestSuite) TestGetPermissions_Success() {
	req := httptest.NewRequest(http.MethodGet, "/permissions", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.GetPermissions(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	permissions := response["permissions"].([]any)
	assert.Len(suite.T(), permissions, 1)

	pagination := response["pagination"].(map[string]any)
	assert.Equal(suite.T(), float64(1), pagination["total"])
	assert.Equal(suite.T(), float64(1), pagination["page"])
	assert.Equal(suite.T(), float64(20), pagination["limit"])
}

func (suite *PermissionHandlerTestSuite) TestGetPermissions_WithPagination() {
	// Create more test permissions
	for i := 2; i <= 5; i++ {
		_, err := suite.permissionService.CreatePermission(
			fmt.Sprintf("test.action%d", i),
			"test",
			fmt.Sprintf("action%d", i),
			fmt.Sprintf("Test Action %d", i),
		)
		suite.Require().NoError(err)
	}

	req := httptest.NewRequest(http.MethodGet, "/permissions?page=1&limit=3", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.handler.GetPermissions(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	permissions := response["permissions"].([]any)
	assert.Len(suite.T(), permissions, 3)

	pagination := response["pagination"].(map[string]any)
	assert.Equal(suite.T(), float64(5), pagination["total"])
	assert.Equal(suite.T(), float64(1), pagination["page"])
	assert.Equal(suite.T(), float64(3), pagination["limit"])
}

func (suite *PermissionHandlerTestSuite) TestGetPermissions_WithSearch() {
	// Create more test permissions with different names
	_, err := suite.permissionService.CreatePermission("user.read", "user", "read", "Read user permission")
	suite.Require().NoError(err)
	_, err = suite.permissionService.CreatePermission("role.read", "role", "read", "Read role permission")
	suite.Require().NoError(err)

	req := httptest.NewRequest(http.MethodGet, "/permissions?search=user", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err = suite.handler.GetPermissions(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	permissions := response["permissions"].([]any)
	assert.Len(suite.T(), permissions, 1) // Only user.read should match

	pagination := response["pagination"].(map[string]any)
	assert.Equal(suite.T(), float64(1), pagination["total"])
}

func (suite *PermissionHandlerTestSuite) TestGetPermission_Success() {
	permissionID := fmt.Sprintf("%d", suite.testPermission.ID)
	req := httptest.NewRequest(http.MethodGet, "/permissions/"+permissionID, nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(permissionID)

	err := suite.handler.GetPermission(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response models.Permission
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "test.read", response.Name)
	assert.Equal(suite.T(), "test", response.Resource)
	assert.Equal(suite.T(), "read", response.Action)
}

func (suite *PermissionHandlerTestSuite) TestGetPermission_InvalidID() {
	req := httptest.NewRequest(http.MethodGet, "/permissions/invalid", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("invalid")

	err := suite.handler.GetPermission(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusBadRequest, httpErr.Code)
}

func (suite *PermissionHandlerTestSuite) TestGetPermission_NotFound() {
	req := httptest.NewRequest(http.MethodGet, "/permissions/999", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("999")

	err := suite.handler.GetPermission(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusNotFound, httpErr.Code)
}

func (suite *PermissionHandlerTestSuite) TestUpdatePermission_Success() {
	updateReq := dto.UpdatePermissionRequest{
		Description: "Updated test permission description",
	}

	permissionID := fmt.Sprintf("%d", suite.testPermission.ID)
	reqBody, _ := json.Marshal(updateReq)
	req := httptest.NewRequest(http.MethodPut, "/permissions/"+permissionID, bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(permissionID)

	err := suite.handler.UpdatePermission(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response models.Permission
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "Updated test permission description", response.Description)
}

func (suite *PermissionHandlerTestSuite) TestDeletePermission_Success() {
	// Create another permission to delete (don't delete the test permission)
	permissionToDelete, err := suite.permissionService.CreatePermission("delete.test", "test", "delete", "Delete test permission")
	suite.Require().NoError(err)

	permissionID := fmt.Sprintf("%d", permissionToDelete.ID)
	req := httptest.NewRequest(http.MethodDelete, "/permissions/"+permissionID, nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(permissionID)

	err = suite.handler.DeletePermission(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "permission deleted successfully", response["message"])
}

func TestPermissionHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(PermissionHandlerTestSuite))
}
