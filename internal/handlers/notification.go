package handlers

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
	"xiaoxingcloud.com/admin/internal/dto"
	"xiaoxingcloud.com/admin/internal/models"
	"xiaoxingcloud.com/admin/internal/services"
)

type NotificationHandler struct {
	notificationService services.NotificationServiceInterface
}

// NewNotificationHandler creates a new notification handler
func NewNotificationHandler(notificationService services.NotificationServiceInterface) *NotificationHandler {
	return &NotificationHandler{
		notificationService: notificationService,
	}
}

// CreateNotification creates a new notification
// @Summary Create new notification
// @Description Create a new notification for a specific user or globally
// @Tags Notifications
// @Accept json
// @Produce json
// @Param request body dto.CreateNotificationRequest true "Notification creation data"
// @Success 201 {object} dto.NotificationResponse "Notification created successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid request"
// @Failure 401 {object} dto.ErrorResponse "User not authenticated"
// @Security BearerAuth
// @Router /admin/notifications [post]
func (h *NotificationHandler) CreateNotification(c echo.Context) error {
	var req dto.CreateNotificationRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// Get sender ID from context
	senderID, ok := c.Get("user_id").(uint)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "user not authenticated")
	}

	notification, err := h.notificationService.CreateNotification(services.CreateNotificationInput{
		Title:      req.Title,
		Content:    req.Content,
		Type:       req.Type,
		Category:   req.Category,
		Priority:   req.Priority,
		IsGlobal:   req.IsGlobal,
		SenderID:   &senderID,
		ReceiverID: req.ReceiverID,
		ActionURL:  req.ActionURL,
		ExpiresAt:  req.ExpiresAt,
	})

	if err != nil {
		if err.Error() == "receiver_id is required for non-global notifications" ||
			err.Error() == "sender not found" ||
			err.Error() == "receiver not found" {
			return echo.NewHTTPError(http.StatusBadRequest, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusCreated, notification)
}

// GetMyNotifications returns notifications for the current user
// @Summary Get my notifications
// @Description Get paginated list of notifications for the current user
// @Tags Notifications
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param unread query bool false "Get only unread notifications"
// @Success 200 {object} dto.NotificationListResponse "Notifications retrieved successfully"
// @Failure 401 {object} dto.ErrorResponse "User not authenticated"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /notifications [get]
func (h *NotificationHandler) GetMyNotifications(c echo.Context) error {
	userID, ok := c.Get("user_id").(uint)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "user not authenticated")
	}

	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit < 1 || limit > 100 {
		limit = 20
	}

	unreadOnly := c.QueryParam("unread") == "true"

	var notifications []models.Notification
	var total int64
	var err error

	if unreadOnly {
		notifications, total, err = h.notificationService.GetUnreadNotifications(userID, page, limit)
	} else {
		notifications, total, err = h.notificationService.GetUserNotifications(userID, page, limit)
	}

	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"notifications": notifications,
		"pagination": map[string]any{
			"page":  page,
			"limit": limit,
			"total": total,
		},
		"unread_only": unreadOnly,
	})
}

// GetNotification returns a specific notification by ID
// @Summary Get notification by ID
// @Description Get detailed information about a specific notification
// @Tags Notifications
// @Accept json
// @Produce json
// @Param id path int true "Notification ID"
// @Success 200 {object} dto.NotificationResponse "Notification retrieved successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid notification ID"
// @Failure 401 {object} dto.ErrorResponse "User not authenticated"
// @Failure 403 {object} dto.ErrorResponse "Access denied"
// @Failure 404 {object} dto.ErrorResponse "Notification not found"
// @Security BearerAuth
// @Router /notifications/{id} [get]
func (h *NotificationHandler) GetNotification(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid notification ID")
	}

	notification, err := h.notificationService.GetNotificationByID(uint(id))
	if err != nil {
		if err.Error() == "notification not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	// Check permission: user can only view their own notifications or global notifications
	userID, ok := c.Get("user_id").(uint)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "user not authenticated")
	}

	if !notification.IsGlobal && (notification.ReceiverID == nil || *notification.ReceiverID != userID) {
		return echo.NewHTTPError(http.StatusForbidden, "access denied")
	}

	return c.JSON(http.StatusOK, notification)
}

// MarkAsRead marks a notification as read
// @Summary Mark notification as read
// @Description Mark a specific notification as read for the current user
// @Tags Notifications
// @Accept json
// @Produce json
// @Param id path int true "Notification ID"
// @Success 200 {object} dto.MessageResponse "Notification marked as read"
// @Failure 400 {object} dto.ErrorResponse "Invalid notification ID"
// @Failure 401 {object} dto.ErrorResponse "User not authenticated"
// @Failure 403 {object} dto.ErrorResponse "Permission denied"
// @Failure 404 {object} dto.ErrorResponse "Notification not found"
// @Security BearerAuth
// @Router /notifications/{id}/read [put]
func (h *NotificationHandler) MarkAsRead(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid notification ID")
	}

	userID, ok := c.Get("user_id").(uint)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "user not authenticated")
	}

	err = h.notificationService.MarkAsRead(uint(id), userID)
	if err != nil {
		if err.Error() == "notification not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if err.Error() == "permission denied" {
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "notification marked as read",
	})
}

// MarkAllAsRead marks all notifications for the current user as read
// @Summary Mark all notifications as read
// @Description Mark all notifications for the current user as read
// @Tags Notifications
// @Accept json
// @Produce json
// @Success 200 {object} object "All notifications marked as read"
// @Failure 401 {object} dto.ErrorResponse "User not authenticated"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /notifications/read-all [put]
func (h *NotificationHandler) MarkAllAsRead(c echo.Context) error {
	userID, ok := c.Get("user_id").(uint)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "user not authenticated")
	}

	count, err := h.notificationService.MarkAllAsRead(userID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"message": "all notifications marked as read",
		"count":   count,
	})
}

// DeleteNotification deletes a notification
// @Summary Delete notification
// @Description Delete a specific notification for the current user
// @Tags Notifications
// @Accept json
// @Produce json
// @Param id path int true "Notification ID"
// @Success 200 {object} dto.MessageResponse "Notification deleted successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid notification ID"
// @Failure 401 {object} dto.ErrorResponse "User not authenticated"
// @Failure 403 {object} dto.ErrorResponse "Permission denied"
// @Failure 404 {object} dto.ErrorResponse "Notification not found"
// @Security BearerAuth
// @Router /notifications/{id} [delete]
func (h *NotificationHandler) DeleteNotification(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid notification ID")
	}

	userID, ok := c.Get("user_id").(uint)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "user not authenticated")
	}

	err = h.notificationService.DeleteNotification(uint(id), userID)
	if err != nil {
		if err.Error() == "notification not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if err.Error() == "permission denied" {
			return echo.NewHTTPError(http.StatusForbidden, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "notification deleted successfully",
	})
}

// GetMyNotificationStats returns notification statistics for the current user
// @Summary Get my notification statistics
// @Description Get notification statistics for the current user
// @Tags Notifications
// @Accept json
// @Produce json
// @Success 200 {object} dto.NotificationStatsResponse "Statistics retrieved successfully"
// @Failure 401 {object} dto.ErrorResponse "User not authenticated"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /notifications/stats [get]
func (h *NotificationHandler) GetMyNotificationStats(c echo.Context) error {
	userID, ok := c.Get("user_id").(uint)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "user not authenticated")
	}

	stats, err := h.notificationService.GetUserNotificationStats(userID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, stats)
}

// BroadcastNotification creates a global notification (admin only)
// @Summary Broadcast notification
// @Description Create a global notification for all users (admin only)
// @Tags Notifications
// @Accept json
// @Produce json
// @Param request body dto.BroadcastNotificationRequest true "Broadcast notification data"
// @Success 201 {object} dto.NotificationResponse "Notification broadcasted successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid request"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /admin/notifications/broadcast [post]
func (h *NotificationHandler) BroadcastNotification(c echo.Context) error {
	var req dto.BroadcastNotificationRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	notification, err := h.notificationService.BroadcastNotification(
		req.Title,
		req.Content,
		req.Type,
		req.Category,
		req.Priority,
		req.ActionURL,
		req.ExpiresAt,
	)

	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusCreated, notification)
}

// CleanupExpired removes expired notifications (admin only)
// @Summary Cleanup expired notifications
// @Description Remove all expired notifications from the system (admin only)
// @Tags Notifications
// @Accept json
// @Produce json
// @Success 200 {object} object "Expired notifications cleaned up"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /admin/notifications/cleanup [delete]
func (h *NotificationHandler) CleanupExpired(c echo.Context) error {
	count, err := h.notificationService.CleanupExpiredNotifications()
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"message": "expired notifications cleaned up",
		"count":   count,
	})
}
