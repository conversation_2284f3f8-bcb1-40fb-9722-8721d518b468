package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
	"xiaoxingcloud.com/admin/internal/services"
)

type <PERSON>t<PERSON><PERSON><PERSON> struct {
	auditService services.AuditServiceInterface
}

// NewAuditHandler creates a new audit handler
func NewAuditHandler(auditService services.AuditServiceInterface) *AuditHandler {
	return &AuditHandler{
		auditService: auditService,
	}
}

// GetAuditLogs returns audit logs with filtering and pagination
// @Summary Get audit logs
// @Description Get paginated audit logs with optional filtering
// @Tags Audit
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param username query string false "Filter by username"
// @Param action query string false "Filter by action"
// @Param resource query string false "Filter by resource"
// @Param status query string false "Filter by status"
// @Param ip_address query string false "Filter by IP address"
// @Param resource_id query int false "Filter by resource ID"
// @Param start_date query string false "Start date (YYYY-MM-DD)"
// @Param end_date query string false "End date (YYYY-MM-DD)"
// @Success 200 {object} object "Audit logs retrieved successfully"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /audit/logs [get]
func (h *AuditHandler) GetAuditLogs(c echo.Context) error {
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Build filter from query parameters
	filter := services.AuditLogFilter{
		Page:      page,
		Limit:     limit,
		Username:  c.QueryParam("username"),
		Action:    c.QueryParam("action"),
		Resource:  c.QueryParam("resource"),
		Status:    c.QueryParam("status"),
		IPAddress: c.QueryParam("ip_address"),
	}

	// Parse resource ID if provided
	if resourceIDStr := c.QueryParam("resource_id"); resourceIDStr != "" {
		if resourceID, err := strconv.ParseUint(resourceIDStr, 10, 32); err == nil {
			resourceIDUint := uint(resourceID)
			filter.ResourceID = &resourceIDUint
		}
	}

	// Parse date range if provided
	if startDateStr := c.QueryParam("start_date"); startDateStr != "" {
		if startDate, err := time.Parse("2006-01-02", startDateStr); err == nil {
			filter.StartDate = &startDate
		}
	}

	if endDateStr := c.QueryParam("end_date"); endDateStr != "" {
		if endDate, err := time.Parse("2006-01-02", endDateStr); err == nil {
			// Set to end of day
			endDate = endDate.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
			filter.EndDate = &endDate
		}
	}

	logs, total, err := h.auditService.GetAuditLogs(filter)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"logs": logs,
		"pagination": map[string]any{
			"page":  page,
			"limit": limit,
			"total": total,
		},
		"filters": map[string]any{
			"username":    filter.Username,
			"action":      filter.Action,
			"resource":    filter.Resource,
			"status":      filter.Status,
			"ip_address":  filter.IPAddress,
			"resource_id": filter.ResourceID,
			"start_date":  filter.StartDate,
			"end_date":    filter.EndDate,
		},
	})
}

// GetUserAuditLogs returns audit logs for a specific user
// @Summary Get user audit logs
// @Description Get paginated audit logs for a specific user
// @Tags Audit
// @Accept json
// @Produce json
// @Param userId path int true "User ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} object "User audit logs retrieved successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid user ID"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /audit/users/{userId} [get]
func (h *AuditHandler) GetUserAuditLogs(c echo.Context) error {
	userID, err := strconv.ParseUint(c.Param("userId"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid user ID")
	}

	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit < 1 || limit > 100 {
		limit = 20
	}

	logs, total, err := h.auditService.GetUserAuditLogs(uint(userID), page, limit)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"logs": logs,
		"pagination": map[string]any{
			"page":  page,
			"limit": limit,
			"total": total,
		},
		"user_id": userID,
	})
}

// GetAuditStats returns audit statistics
// @Summary Get audit statistics
// @Description Get overall audit log statistics
// @Tags Audit
// @Accept json
// @Produce json
// @Success 200 {object} object "Audit statistics retrieved successfully"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /audit/stats [get]
func (h *AuditHandler) GetAuditStats(c echo.Context) error {
	stats, err := h.auditService.GetAuditStats()
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, stats)
}

// CleanupOldLogs removes old audit logs
func (h *AuditHandler) CleanupOldLogs(c echo.Context) error {
	daysToKeep, err := strconv.Atoi(c.QueryParam("days"))
	if err != nil || daysToKeep < 1 {
		daysToKeep = 90 // Default to 90 days
	}

	// Only allow admin users to perform cleanup
	// This should be protected by appropriate middleware

	deletedCount, err := h.auditService.CleanupOldLogs(daysToKeep)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"message":       "cleanup completed successfully",
		"deleted_count": deletedCount,
		"days_kept":     daysToKeep,
	})
}

// GetMyAuditLogs returns audit logs for the current user
func (h *AuditHandler) GetMyAuditLogs(c echo.Context) error {
	userID, ok := c.Get("user_id").(uint)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "user not authenticated")
	}

	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit < 1 || limit > 100 {
		limit = 20
	}

	logs, total, err := h.auditService.GetUserAuditLogs(userID, page, limit)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"logs": logs,
		"pagination": map[string]any{
			"page":  page,
			"limit": limit,
			"total": total,
		},
	})
}

// GetAuditActions returns a list of available audit actions for filtering
func (h *AuditHandler) GetAuditActions(c echo.Context) error {
	// This could be made dynamic by querying the database
	actions := []string{
		"login",
		"logout",
		"create_user",
		"update_user",
		"delete_user",
		"create_role",
		"update_role",
		"delete_role",
		"assign_role",
		"remove_role",
		"create_permission",
		"update_permission",
		"delete_permission",
		"assign_permission",
		"remove_permission",
		"system_startup",
		"system_shutdown",
		"backup_created",
		"backup_restored",
	}

	return c.JSON(http.StatusOK, map[string]any{
		"actions": actions,
	})
}

// GetAuditResources returns a list of available audit resources for filtering
func (h *AuditHandler) GetAuditResources(c echo.Context) error {
	// This could be made dynamic by querying the database
	resources := []string{
		"auth",
		"users",
		"roles",
		"permissions",
		"system",
		"settings",
		"files",
		"notifications",
	}

	return c.JSON(http.StatusOK, map[string]any{
		"resources": resources,
	})
}
