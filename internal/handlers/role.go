package handlers

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
	"xiaoxingcloud.com/admin/internal/dto"
	"xiaoxingcloud.com/admin/internal/services"
)

type RoleHandler struct {
	roleService services.RoleServiceInterface
}

// NewRoleHandler creates a new role handler
func NewRoleHandler(roleService services.RoleServiceInterface) *RoleHandler {
	return &RoleHandler{
		roleService: roleService,
	}
}

// CreateRole creates a new role
// @Summary Create new role
// @Description Create a new role with the provided information
// @Tags Roles
// @Accept json
// @Produce json
// @Param request body dto.CreateRoleRequest true "Role creation data"
// @Success 201 {object} dto.RoleResponse "Role created successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid request or role already exists"
// @Security BearerAuth
// @Router /roles [post]
func (h *RoleHandler) CreateRole(c echo.Context) error {
	var req dto.CreateRoleRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	role, err := h.roleService.CreateRole(req.Name, req.Description)
	if err != nil {
		if err.Error() == "role name already exists" {
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusCreated, role)
}

// GetRoles returns all roles with pagination
// @Summary Get all roles
// @Description Get paginated list of roles
// @Tags Roles
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} dto.RoleListResponse "Roles retrieved successfully"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /roles [get]
func (h *RoleHandler) GetRoles(c echo.Context) error {
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit < 1 || limit > 100 {
		limit = 10
	}

	roles, total, err := h.roleService.GetAllRoles(page, limit)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"roles": roles,
		"pagination": map[string]any{
			"page":  page,
			"limit": limit,
			"total": total,
		},
	})
}

// GetRole returns a specific role by ID
// @Summary Get role by ID
// @Description Get detailed information about a specific role
// @Tags Roles
// @Accept json
// @Produce json
// @Param id path int true "Role ID"
// @Success 200 {object} dto.RoleResponse "Role retrieved successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid role ID"
// @Failure 404 {object} dto.ErrorResponse "Role not found"
// @Security BearerAuth
// @Router /roles/{id} [get]
func (h *RoleHandler) GetRole(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid role ID")
	}

	role, err := h.roleService.GetRoleByID(uint(id))
	if err != nil {
		if err.Error() == "role not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, role)
}

// UpdateRole updates a role
// @Summary Update role
// @Description Update role information by ID
// @Tags Roles
// @Accept json
// @Produce json
// @Param id path int true "Role ID"
// @Param request body dto.UpdateRoleRequest true "Role update data"
// @Success 200 {object} dto.RoleResponse "Role updated successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid request"
// @Failure 404 {object} dto.ErrorResponse "Role not found"
// @Failure 409 {object} dto.ErrorResponse "Role name already exists"
// @Security BearerAuth
// @Router /roles/{id} [put]
func (h *RoleHandler) UpdateRole(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid role ID")
	}

	var req dto.UpdateRoleRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	role, err := h.roleService.UpdateRole(uint(id), req.Name, req.Description, req.IsActive)
	if err != nil {
		if err.Error() == "role not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if err.Error() == "role name already exists" {
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, role)
}

// DeleteRole deletes a role
// @Summary Delete role
// @Description Delete a role by ID (soft delete)
// @Tags Roles
// @Accept json
// @Produce json
// @Param id path int true "Role ID"
// @Success 200 {object} dto.MessageResponse "Role deleted successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid role ID"
// @Failure 404 {object} dto.ErrorResponse "Role not found"
// @Security BearerAuth
// @Router /roles/{id} [delete]
func (h *RoleHandler) DeleteRole(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid role ID")
	}

	err = h.roleService.DeleteRole(uint(id))
	if err != nil {
		if err.Error() == "role not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if err.Error() == "cannot delete role that is assigned to users" {
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "role deleted successfully",
	})
}

// AssignPermission assigns a permission to a role
// @Summary Assign permission to role
// @Description Assign a permission to a specific role
// @Tags Roles
// @Accept json
// @Produce json
// @Param id path int true "Role ID"
// @Param request body dto.AssignPermissionRequest true "Permission assignment data"
// @Success 200 {object} dto.MessageResponse "Permission assigned successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid request"
// @Failure 404 {object} dto.ErrorResponse "Role or permission not found"
// @Failure 409 {object} dto.ErrorResponse "Permission already assigned"
// @Security BearerAuth
// @Router /roles/{id}/permissions [post]
func (h *RoleHandler) AssignPermission(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid role ID")
	}

	var req dto.AssignPermissionRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	err = h.roleService.AssignPermissionToRole(uint(id), req.PermissionID)
	if err != nil {
		if err.Error() == "role not found" || err.Error() == "permission not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		if err.Error() == "permission already assigned to role" {
			return echo.NewHTTPError(http.StatusConflict, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "permission assigned successfully",
	})
}

// RemovePermission removes a permission from a role
// @Summary Remove permission from role
// @Description Remove a specific permission from a role
// @Tags Roles
// @Accept json
// @Produce json
// @Param id path int true "Role ID"
// @Param permissionId path int true "Permission ID"
// @Success 200 {object} dto.MessageResponse "Permission removed successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid request"
// @Failure 404 {object} dto.ErrorResponse "Permission assignment not found"
// @Security BearerAuth
// @Router /roles/{id}/permissions/{permissionId} [delete]
func (h *RoleHandler) RemovePermission(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid role ID")
	}

	permissionID, err := strconv.ParseUint(c.Param("permissionId"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid permission ID")
	}

	err = h.roleService.RemovePermissionFromRole(uint(id), uint(permissionID))
	if err != nil {
		if err.Error() == "permission assignment not found" {
			return echo.NewHTTPError(http.StatusNotFound, err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "permission removed successfully",
	})
}

// GetRolePermissions returns all permissions for a role
// @Summary Get role permissions
// @Description Get all permissions assigned to a specific role
// @Tags Roles
// @Accept json
// @Produce json
// @Param id path int true "Role ID"
// @Success 200 {object} object "Role permissions retrieved successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid role ID"
// @Failure 500 {object} dto.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /roles/{id}/permissions [get]
func (h *RoleHandler) GetRolePermissions(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid role ID")
	}

	permissions, err := h.roleService.GetRolePermissions(uint(id))
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]any{
		"permissions": permissions,
	})
}
