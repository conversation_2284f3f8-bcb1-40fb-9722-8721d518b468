package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/auth"
	"xiaoxingcloud.com/admin/internal/config"
	"xiaoxingcloud.com/admin/internal/dto"
	"xiaoxingcloud.com/admin/internal/models"
	"xiaoxingcloud.com/admin/internal/services"
	"xiaoxingcloud.com/admin/pkg"
)

type HandlersTestSuite struct {
	suite.Suite
	db                  *gorm.DB
	echo                *echo.Echo
	authHandler         *AuthHandler
	userHandler         *UserHandler
	menuHandler         *MenuHandler
	notificationHandler *NotificationHandler
	userService         *services.UserService
	jwtService          *auth.JWTService
	testUser            *models.User
}

func (suite *HandlersTestSuite) SetupSuite() {
	// Setup in-memory database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Auto migrate
	err = db.AutoMigrate(
		&models.User{},
		&models.Role{},
		&models.Permission{},
		&models.Menu{},
		&models.Notification{},
		&models.SystemSetting{},
		&models.AuditLog{},
		&models.Attribute{},
		&models.Policy{},
	)
	suite.Require().NoError(err)

	suite.db = db

	// Setup services
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:      "test-secret-key-for-testing",
			ExpireHours: 24,
		},
	}

	suite.userService = services.NewUserService(db)
	suite.jwtService = auth.NewJWTService(cfg)
	menuService := services.NewMenuService(db)
	notificationService := services.NewNotificationService(db)
	rbacService := services.NewRBACService(db)

	// Setup handlers
	suite.authHandler = NewAuthHandler(suite.userService, suite.jwtService)
	suite.userHandler = NewUserHandler(suite.userService, rbacService)
	suite.menuHandler = NewMenuHandler(menuService)
	suite.notificationHandler = NewNotificationHandler(notificationService)

	// Setup Echo
	suite.echo = echo.New()
	suite.echo.Validator = pkg.NewValidator()
}

func (suite *HandlersTestSuite) SetupTest() {
	// Clean up database
	suite.db.Exec("DELETE FROM users")
	suite.db.Exec("DELETE FROM roles")
	suite.db.Exec("DELETE FROM permissions")
	suite.db.Exec("DELETE FROM menus")
	suite.db.Exec("DELETE FROM notifications")

	// Create test user
	user, err := suite.userService.CreateUser("testuser", "<EMAIL>", "Password123!", "Test", "User")
	suite.Require().NoError(err)
	suite.testUser = user
}

func (suite *HandlersTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

// Auth Handler Tests
func (suite *HandlersTestSuite) TestAuthHandler_Login_Success() {
	loginReq := dto.LoginRequest{
		Identifier: "testuser",
		Password:   "Password123!",
	}

	reqBody, _ := json.Marshal(loginReq)
	req := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.authHandler.Login(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response dto.LoginResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), response.Token)
	assert.Equal(suite.T(), "testuser", response.User.Username)
}

func (suite *HandlersTestSuite) TestAuthHandler_Login_InvalidCredentials() {
	loginReq := dto.LoginRequest{
		Identifier: "testuser",
		Password:   "wrongpassword",
	}

	reqBody, _ := json.Marshal(loginReq)
	req := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.authHandler.Login(c)
	assert.Error(suite.T(), err)
	httpErr, ok := err.(*echo.HTTPError)
	assert.True(suite.T(), ok)
	assert.Equal(suite.T(), http.StatusUnauthorized, httpErr.Code)
}

func (suite *HandlersTestSuite) TestAuthHandler_Register_Success() {
	registerReq := dto.RegisterRequest{
		Username:  "newuser",
		Email:     "<EMAIL>",
		Password:  "Password123!",
		FirstName: "New",
		LastName:  "User",
	}

	reqBody, _ := json.Marshal(registerReq)
	req := httptest.NewRequest(http.MethodPost, "/auth/register", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.authHandler.Register(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusCreated, rec.Code)

	var response dto.LoginResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), response.Token)
	assert.Equal(suite.T(), "newuser", response.User.Username)
}

func (suite *HandlersTestSuite) TestAuthHandler_GetProfile_Success() {
	req := httptest.NewRequest(http.MethodGet, "/auth/profile", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.Set("user_id", suite.testUser.ID)

	err := suite.authHandler.GetProfile(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "testuser", response["username"])
}

func (suite *HandlersTestSuite) TestAuthHandler_Logout_Success() {
	req := httptest.NewRequest(http.MethodPost, "/auth/logout", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.authHandler.Logout(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "logged out successfully", response["message"])
}

// User Handler Tests
func (suite *HandlersTestSuite) TestUserHandler_GetUsers_Success() {
	req := httptest.NewRequest(http.MethodGet, "/users", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.userHandler.GetUsers(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	users := response["users"].([]any)
	assert.Len(suite.T(), users, 1)

	pagination := response["pagination"].(map[string]any)
	assert.Equal(suite.T(), float64(1), pagination["total"])
}

func (suite *HandlersTestSuite) TestUserHandler_GetUser_Success() {
	// Use the actual user ID from the test user
	userID := fmt.Sprintf("%d", suite.testUser.ID)
	req := httptest.NewRequest(http.MethodGet, "/users/"+userID, nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(userID)

	err := suite.userHandler.GetUser(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusOK, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "testuser", response["username"])
}

func (suite *HandlersTestSuite) TestUserHandler_CreateUser_Success() {
	createReq := dto.CreateUserRequest{
		Username:  "newuser2",
		Email:     "<EMAIL>",
		Password:  "Password123!",
		FirstName: "New2",
		LastName:  "User2",
	}

	reqBody, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/users", bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	err := suite.userHandler.CreateUser(c)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), http.StatusCreated, rec.Code)

	var response map[string]any
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "newuser2", response["username"])
}

func TestHandlersTestSuite(t *testing.T) {
	suite.Run(t, new(HandlersTestSuite))
}
