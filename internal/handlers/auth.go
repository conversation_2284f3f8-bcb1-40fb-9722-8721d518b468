package handlers

import (
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	"xiaoxingcloud.com/admin/internal/auth"
	"xiaoxingcloud.com/admin/internal/dto"
	"xiaoxingcloud.com/admin/internal/services"
)

type AuthHandler struct {
	userService *services.UserService
	jwtService  *auth.JWTService
}

func NewAuthHandler(userService *services.UserService, jwtService *auth.JWTService) *AuthHandler {
	return &AuthHandler{
		userService: userService,
		jwtService:  jwtService,
	}
}

// <PERSON>gin handles user login
// @Summary User login
// @Description Authenticate user with username/email and password
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body dto.LoginRequest true "Login credentials"
// @Success 200 {object} dto.LoginResponse "Login successful"
// @Failure 400 {object} dto.ErrorResponse "Invalid request"
// @Failure 401 {object} dto.ErrorResponse "Invalid credentials"
// @Router /auth/login [post]
func (h *AuthHandler) Login(c echo.Context) error {
	var req dto.LoginRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// Authenticate user
	user, err := h.userService.AuthenticateUser(req.Identifier, req.Password)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, err.Error())
	}

	// Generate JWT token
	token, err := h.jwtService.GenerateToken(user.ID, user.Username, user.Email)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "failed to generate token")
	}

	// Create response using DTO
	response := &dto.LoginResponse{
		Token:     token,
		ExpiresAt: time.Now().Add(time.Duration(24) * time.Hour), // TODO: Get from config
		User:      *dto.ToUserInfo(user),
	}

	return c.JSON(http.StatusOK, response)
}

// Register handles user registration
// @Summary User registration
// @Description Register a new user account
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body dto.RegisterRequest true "Registration data"
// @Success 201 {object} dto.LoginResponse "Registration successful"
// @Failure 400 {object} dto.ErrorResponse "Invalid request"
// @Failure 409 {object} dto.ErrorResponse "User already exists"
// @Router /auth/register [post]
func (h *AuthHandler) Register(c echo.Context) error {
	var req dto.RegisterRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// Create user
	user, err := h.userService.CreateUser(req.Username, req.Email, req.Password, req.FirstName, req.LastName)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// Generate JWT token
	token, err := h.jwtService.GenerateToken(user.ID, user.Username, user.Email)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "failed to generate token")
	}

	// Create response using DTO
	response := &dto.LoginResponse{
		Token:     token,
		ExpiresAt: time.Now().Add(time.Duration(24) * time.Hour), // TODO: Get from config
		User:      *dto.ToUserInfo(user),
	}

	return c.JSON(http.StatusCreated, response)
}

// RefreshToken handles token refresh
// @Summary Refresh JWT token
// @Description Refresh an existing JWT token to extend session
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body dto.RefreshTokenRequest true "Refresh token request"
// @Success 200 {object} map[string]string "Token refreshed successfully"
// @Failure 400 {object} dto.ErrorResponse "Invalid request"
// @Failure 401 {object} dto.ErrorResponse "Invalid or expired token"
// @Router /auth/refresh [post]
func (h *AuthHandler) RefreshToken(c echo.Context) error {
	var req dto.RefreshTokenRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// Refresh token
	newToken, err := h.jwtService.RefreshToken(req.Token)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{
		"token": newToken,
	})
}

// GetProfile returns the current user's profile
// @Summary Get current user profile
// @Description Get the profile information of the currently authenticated user
// @Tags Authentication
// @Accept json
// @Produce json
// @Success 200 {object} dto.UserResponse "User profile retrieved successfully"
// @Failure 401 {object} dto.ErrorResponse "User not authenticated"
// @Failure 404 {object} dto.ErrorResponse "User not found"
// @Security BearerAuth
// @Router /auth/profile [get]
func (h *AuthHandler) GetProfile(c echo.Context) error {
	userID, ok := c.Get("user_id").(uint)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "user not authenticated")
	}

	user, err := h.userService.GetUserByID(userID)
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, err.Error())
	}

	// Return user without password
	userResponse := map[string]any{
		"id":         user.ID,
		"username":   user.Username,
		"email":      user.Email,
		"first_name": user.FirstName,
		"last_name":  user.LastName,
		"is_active":  user.IsActive,
		"roles":      user.Roles,
		"attributes": user.Attributes,
		"created_at": user.CreatedAt,
		"updated_at": user.UpdatedAt,
	}

	return c.JSON(http.StatusOK, userResponse)
}

// Logout handles user logout (client-side token removal)
// @Summary User logout
// @Description Logout user (client-side token removal, server doesn't maintain token state)
// @Tags Authentication
// @Accept json
// @Produce json
// @Success 200 {object} dto.MessageResponse "Logout successful"
// @Security BearerAuth
// @Router /auth/logout [post]
func (h *AuthHandler) Logout(c echo.Context) error {
	return c.JSON(http.StatusOK, map[string]string{
		"message": "logged out successfully",
	})
}
