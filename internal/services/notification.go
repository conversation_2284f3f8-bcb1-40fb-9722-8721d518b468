package services

import (
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/models"
)

// NotificationServiceInterface defines the interface for notification service
type NotificationServiceInterface interface {
	CreateNotification(input CreateNotificationInput) (*models.Notification, error)
	GetNotificationByID(id uint) (*models.Notification, error)
	GetUserNotifications(userID uint, page, limit int) ([]models.Notification, int64, error)
	GetUnreadNotifications(userID uint, page, limit int) ([]models.Notification, int64, error)
	MarkAsRead(notificationID, userID uint) error
	MarkAllAsRead(userID uint) (int64, error)
	DeleteNotification(notificationID, userID uint) error
	GetUserNotificationStats(userID uint) (*NotificationStats, error)
	CleanupExpiredNotifications() (int64, error)
	BroadcastNotification(title, content, notificationType, category string, priority int, actionURL string, expiresAt *time.Time) (*models.Notification, error)
}

type NotificationService struct {
	db *gorm.DB
}

// Compile-time interface compliance check
var _ NotificationServiceInterface = (*NotificationService)(nil)

// CreateNotificationInput represents input for creating a notification
type CreateNotificationInput struct {
	Title      string     `json:"title"`
	Content    string     `json:"content"`
	Type       string     `json:"type"`
	Category   string     `json:"category"`
	Priority   int        `json:"priority"`
	IsGlobal   bool       `json:"is_global"`
	SenderID   *uint      `json:"sender_id"`
	ReceiverID *uint      `json:"receiver_id"`
	ActionURL  string     `json:"action_url"`
	ExpiresAt  *time.Time `json:"expires_at"`
}

// NotificationStats represents notification statistics
type NotificationStats struct {
	TotalNotifications int64            `json:"total_notifications"`
	UnreadCount        int64            `json:"unread_count"`
	ReadCount          int64            `json:"read_count"`
	ByType             map[string]int64 `json:"by_type"`
	ByCategory         map[string]int64 `json:"by_category"`
	ByPriority         map[int]int64    `json:"by_priority"`
}

// NewNotificationService creates a new notification service
func NewNotificationService(db *gorm.DB) *NotificationService {
	return &NotificationService{db: db}
}

// CreateNotification creates a new notification
func (n *NotificationService) CreateNotification(input CreateNotificationInput) (*models.Notification, error) {
	// Validate input
	if input.Title == "" {
		return nil, errors.New("title is required")
	}
	if input.Content == "" {
		return nil, errors.New("content is required")
	}

	// For non-global notifications, receiver is required
	if !input.IsGlobal && input.ReceiverID == nil {
		return nil, errors.New("receiver_id is required for non-global notifications")
	}

	// Validate sender exists if provided
	if input.SenderID != nil {
		var sender models.User
		err := n.db.First(&sender, *input.SenderID).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, errors.New("sender not found")
			}
			return nil, fmt.Errorf("failed to check sender: %w", err)
		}
	}

	// Validate receiver exists if provided
	if input.ReceiverID != nil {
		var receiver models.User
		err := n.db.First(&receiver, *input.ReceiverID).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, errors.New("receiver not found")
			}
			return nil, fmt.Errorf("failed to check receiver: %w", err)
		}
	}

	// Set default values
	if input.Type == "" {
		input.Type = "info"
	}
	if input.Category == "" {
		input.Category = "general"
	}

	// Create notification
	notification := &models.Notification{
		Title:      input.Title,
		Content:    input.Content,
		Type:       input.Type,
		Category:   input.Category,
		Priority:   input.Priority,
		IsGlobal:   input.IsGlobal,
		SenderID:   input.SenderID,
		ReceiverID: input.ReceiverID,
		ActionURL:  input.ActionURL,
		ExpiresAt:  input.ExpiresAt,
		IsRead:     false,
	}

	if err := n.db.Create(notification).Error; err != nil {
		return nil, fmt.Errorf("failed to create notification: %w", err)
	}

	return notification, nil
}

// GetNotificationByID gets a notification by ID
func (n *NotificationService) GetNotificationByID(id uint) (*models.Notification, error) {
	var notification models.Notification
	err := n.db.Preload("Sender").Preload("Receiver").First(&notification, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("notification not found")
		}
		return nil, fmt.Errorf("failed to get notification: %w", err)
	}

	return &notification, nil
}

// GetUserNotifications gets notifications for a specific user (including global notifications)
func (n *NotificationService) GetUserNotifications(userID uint, page, limit int) ([]models.Notification, int64, error) {
	var notifications []models.Notification
	var total int64

	// Build query for user notifications and global notifications
	query := n.db.Model(&models.Notification{}).Where("receiver_id = ? OR is_global = ?", userID, true)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count notifications: %w", err)
	}

	// Get notifications with pagination
	offset := (page - 1) * limit
	err := query.Preload("Sender").Preload("Receiver").
		Order("priority DESC, created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&notifications).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get user notifications: %w", err)
	}

	return notifications, total, nil
}

// GetUnreadNotifications gets unread notifications for a user
func (n *NotificationService) GetUnreadNotifications(userID uint, page, limit int) ([]models.Notification, int64, error) {
	var notifications []models.Notification
	var total int64

	// Build query for unread notifications
	query := n.db.Model(&models.Notification{}).Where("(receiver_id = ? OR is_global = ?) AND is_read = ?", userID, true, false)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count unread notifications: %w", err)
	}

	// Get notifications with pagination
	offset := (page - 1) * limit
	err := query.Preload("Sender").Preload("Receiver").
		Order("priority DESC, created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&notifications).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get unread notifications: %w", err)
	}

	return notifications, total, nil
}

// MarkAsRead marks a notification as read
func (n *NotificationService) MarkAsRead(notificationID, userID uint) error {
	var notification models.Notification
	err := n.db.First(&notification, notificationID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("notification not found")
		}
		return fmt.Errorf("failed to find notification: %w", err)
	}

	// Check permission: user can only mark their own notifications or global notifications as read
	if !notification.IsGlobal && (notification.ReceiverID == nil || *notification.ReceiverID != userID) {
		return errors.New("permission denied")
	}

	// Mark as read
	notification.IsRead = true
	if err := n.db.Save(&notification).Error; err != nil {
		return fmt.Errorf("failed to mark notification as read: %w", err)
	}

	return nil
}

// MarkAllAsRead marks all notifications for a user as read
func (n *NotificationService) MarkAllAsRead(userID uint) (int64, error) {
	result := n.db.Model(&models.Notification{}).
		Where("(receiver_id = ? OR is_global = ?) AND is_read = ?", userID, true, false).
		Update("is_read", true)

	if result.Error != nil {
		return 0, fmt.Errorf("failed to mark all notifications as read: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// DeleteNotification soft deletes a notification
func (n *NotificationService) DeleteNotification(notificationID, userID uint) error {
	var notification models.Notification
	err := n.db.First(&notification, notificationID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("notification not found")
		}
		return fmt.Errorf("failed to find notification: %w", err)
	}

	// Check permission: user can only delete their own notifications
	if notification.ReceiverID == nil || *notification.ReceiverID != userID {
		return errors.New("permission denied")
	}

	// Soft delete the notification
	if err := n.db.Delete(&notification).Error; err != nil {
		return fmt.Errorf("failed to delete notification: %w", err)
	}

	return nil
}

// GetUserNotificationStats gets notification statistics for a user
func (n *NotificationService) GetUserNotificationStats(userID uint) (*NotificationStats, error) {
	stats := &NotificationStats{
		ByType:     make(map[string]int64),
		ByCategory: make(map[string]int64),
		ByPriority: make(map[int]int64),
	}

	// Total notifications
	if err := n.db.Model(&models.Notification{}).
		Where("receiver_id = ? OR is_global = ?", userID, true).
		Count(&stats.TotalNotifications).Error; err != nil {
		return nil, fmt.Errorf("failed to count total notifications: %w", err)
	}

	// Unread count
	if err := n.db.Model(&models.Notification{}).
		Where("(receiver_id = ? OR is_global = ?) AND is_read = ?", userID, true, false).
		Count(&stats.UnreadCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count unread notifications: %w", err)
	}

	// Read count
	stats.ReadCount = stats.TotalNotifications - stats.UnreadCount

	// By type
	var typeStats []struct {
		Type  string `json:"type"`
		Count int64  `json:"count"`
	}
	if err := n.db.Model(&models.Notification{}).
		Select("type, COUNT(*) as count").
		Where("receiver_id = ? OR is_global = ?", userID, true).
		Group("type").
		Find(&typeStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get type stats: %w", err)
	}
	for _, stat := range typeStats {
		stats.ByType[stat.Type] = stat.Count
	}

	// By category
	var categoryStats []struct {
		Category string `json:"category"`
		Count    int64  `json:"count"`
	}
	if err := n.db.Model(&models.Notification{}).
		Select("category, COUNT(*) as count").
		Where("receiver_id = ? OR is_global = ?", userID, true).
		Group("category").
		Find(&categoryStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get category stats: %w", err)
	}
	for _, stat := range categoryStats {
		stats.ByCategory[stat.Category] = stat.Count
	}

	// By priority
	var priorityStats []struct {
		Priority int   `json:"priority"`
		Count    int64 `json:"count"`
	}
	if err := n.db.Model(&models.Notification{}).
		Select("priority, COUNT(*) as count").
		Where("receiver_id = ? OR is_global = ?", userID, true).
		Group("priority").
		Find(&priorityStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get priority stats: %w", err)
	}
	for _, stat := range priorityStats {
		stats.ByPriority[stat.Priority] = stat.Count
	}

	return stats, nil
}

// CleanupExpiredNotifications removes expired notifications
func (n *NotificationService) CleanupExpiredNotifications() (int64, error) {
	now := time.Now()
	result := n.db.Where("expires_at IS NOT NULL AND expires_at < ?", now).Delete(&models.Notification{})
	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup expired notifications: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// BroadcastNotification creates a global notification for all users
func (n *NotificationService) BroadcastNotification(title, content, notificationType, category string, priority int, actionURL string, expiresAt *time.Time) (*models.Notification, error) {
	return n.CreateNotification(CreateNotificationInput{
		Title:     title,
		Content:   content,
		Type:      notificationType,
		Category:  category,
		Priority:  priority,
		IsGlobal:  true,
		ActionURL: actionURL,
		ExpiresAt: expiresAt,
	})
}
