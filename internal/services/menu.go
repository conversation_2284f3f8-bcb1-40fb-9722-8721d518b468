package services

import (
	"errors"
	"fmt"

	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/models"
)

// MenuServiceInterface defines the interface for menu service
type MenuServiceInterface interface {
	CreateMenu(input CreateMenuInput) (*models.Menu, error)
	GetMenuByID(id uint) (*models.Menu, error)
	GetAllMenus() ([]models.Menu, error)
	GetMenuTree() ([]models.Menu, error)
	GetVisibleMenuTree() ([]models.Menu, error)
	GetMenusByPermission(permissions []string) ([]models.Menu, error)
	UpdateMenu(id uint, input UpdateMenuInput) (*models.Menu, error)
	DeleteMenu(id uint) error
	InitializeDefaultMenus() error
}

type MenuService struct {
	db *gorm.DB
}

// Compile-time interface compliance check
var _ MenuServiceInterface = (*MenuService)(nil)

// CreateMenuInput represents input for creating a menu
type CreateMenuInput struct {
	Name        string `json:"name"`
	Title       string `json:"title"`
	Icon        string `json:"icon"`
	Path        string `json:"path"`
	Component   string `json:"component"`
	ParentID    *uint  `json:"parent_id"`
	Sort        int    `json:"sort"`
	IsVisible   bool   `json:"is_visible"`
	IsEnabled   bool   `json:"is_enabled"`
	Permission  string `json:"permission"`
	MenuType    string `json:"menu_type"`
	ExternalURL string `json:"external_url"`
	Target      string `json:"target"`
}

// UpdateMenuInput represents input for updating a menu
type UpdateMenuInput struct {
	Name        string `json:"name"`
	Title       string `json:"title"`
	Icon        string `json:"icon"`
	Path        string `json:"path"`
	Component   string `json:"component"`
	ParentID    *uint  `json:"parent_id"`
	Sort        int    `json:"sort"`
	IsVisible   bool   `json:"is_visible"`
	IsEnabled   bool   `json:"is_enabled"`
	Permission  string `json:"permission"`
	MenuType    string `json:"menu_type"`
	ExternalURL string `json:"external_url"`
	Target      string `json:"target"`
}

// NewMenuService creates a new menu service
func NewMenuService(db *gorm.DB) *MenuService {
	return &MenuService{db: db}
}

// CreateMenu creates a new menu item
func (m *MenuService) CreateMenu(input CreateMenuInput) (*models.Menu, error) {
	// Validate parent menu exists if ParentID is provided
	if input.ParentID != nil {
		var parentMenu models.Menu
		err := m.db.First(&parentMenu, *input.ParentID).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, errors.New("parent menu not found")
			}
			return nil, fmt.Errorf("failed to check parent menu: %w", err)
		}
	}

	// Check if menu name already exists
	var existingMenu models.Menu
	err := m.db.Where("name = ?", input.Name).First(&existingMenu).Error
	if err == nil {
		return nil, errors.New("menu name already exists")
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check existing menu: %w", err)
	}

	// Create new menu
	menu := &models.Menu{
		Name:        input.Name,
		Title:       input.Title,
		Icon:        input.Icon,
		Path:        input.Path,
		Component:   input.Component,
		ParentID:    input.ParentID,
		Sort:        input.Sort,
		IsVisible:   input.IsVisible,
		IsEnabled:   input.IsEnabled,
		Permission:  input.Permission,
		MenuType:    input.MenuType,
		ExternalURL: input.ExternalURL,
		Target:      input.Target,
	}

	if err := m.db.Create(menu).Error; err != nil {
		return nil, fmt.Errorf("failed to create menu: %w", err)
	}

	return menu, nil
}

// GetMenuByID gets a menu by ID
func (m *MenuService) GetMenuByID(id uint) (*models.Menu, error) {
	var menu models.Menu
	err := m.db.Preload("Parent").Preload("Children", func(db *gorm.DB) *gorm.DB {
		return db.Order("sort ASC")
	}).First(&menu, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("menu not found")
		}
		return nil, fmt.Errorf("failed to get menu: %w", err)
	}

	return &menu, nil
}

// GetAllMenus gets all menus ordered by sort
func (m *MenuService) GetAllMenus() ([]models.Menu, error) {
	var menus []models.Menu
	err := m.db.Preload("Parent").Order("sort ASC, id ASC").Find(&menus).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get all menus: %w", err)
	}

	return menus, nil
}

// GetMenuTree gets all menus in tree structure
func (m *MenuService) GetMenuTree() ([]models.Menu, error) {
	var menus []models.Menu
	err := m.db.Where("parent_id IS NULL").
		Preload("Children", func(db *gorm.DB) *gorm.DB {
			return db.Order("sort ASC")
		}).
		Order("sort ASC").
		Find(&menus).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get menu tree: %w", err)
	}

	return menus, nil
}

// GetVisibleMenuTree gets visible menus in tree structure
func (m *MenuService) GetVisibleMenuTree() ([]models.Menu, error) {
	var menus []models.Menu
	err := m.db.Where("parent_id IS NULL AND is_visible = ? AND is_enabled = ?", true, true).
		Preload("Children", func(db *gorm.DB) *gorm.DB {
			return db.Where("is_visible = ? AND is_enabled = ?", true, true).Order("sort ASC")
		}).
		Order("sort ASC").
		Find(&menus).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get visible menu tree: %w", err)
	}

	return menus, nil
}

// GetMenusByPermission gets menus that user has permission to access
func (m *MenuService) GetMenusByPermission(permissions []string) ([]models.Menu, error) {
	var menus []models.Menu
	query := m.db.Where("is_visible = ? AND is_enabled = ?", true, true)

	if len(permissions) > 0 {
		// Include menus with no permission requirement or with matching permissions
		query = query.Where("permission = '' OR permission IS NULL OR permission IN ?", permissions)
	} else {
		// Only include menus with no permission requirement
		query = query.Where("permission = '' OR permission IS NULL")
	}

	err := query.Where("parent_id IS NULL").
		Preload("Children", func(db *gorm.DB) *gorm.DB {
			childQuery := db.Where("is_visible = ? AND is_enabled = ?", true, true)
			if len(permissions) > 0 {
				childQuery = childQuery.Where("permission = '' OR permission IS NULL OR permission IN ?", permissions)
			} else {
				childQuery = childQuery.Where("permission = '' OR permission IS NULL")
			}
			return childQuery.Order("sort ASC")
		}).
		Order("sort ASC").
		Find(&menus).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get menus by permission: %w", err)
	}

	return menus, nil
}

// UpdateMenu updates a menu
func (m *MenuService) UpdateMenu(id uint, input UpdateMenuInput) (*models.Menu, error) {
	var menu models.Menu
	err := m.db.First(&menu, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("menu not found")
		}
		return nil, fmt.Errorf("failed to find menu: %w", err)
	}

	// Validate parent menu exists if ParentID is provided
	if input.ParentID != nil {
		var parentMenu models.Menu
		err := m.db.First(&parentMenu, *input.ParentID).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, errors.New("parent menu not found")
			}
			return nil, fmt.Errorf("failed to check parent menu: %w", err)
		}

		// Prevent circular reference
		if *input.ParentID == id {
			return nil, errors.New("menu cannot be its own parent")
		}
	}

	// Check if new name conflicts with existing menu (if name is being changed)
	if menu.Name != input.Name {
		var existingMenu models.Menu
		err := m.db.Where("name = ? AND id != ?", input.Name, id).First(&existingMenu).Error
		if err == nil {
			return nil, errors.New("menu name already exists")
		}
		if err != gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("failed to check existing menu: %w", err)
		}
	}

	// Update menu
	menu.Name = input.Name
	menu.Title = input.Title
	menu.Icon = input.Icon
	menu.Path = input.Path
	menu.Component = input.Component
	menu.ParentID = input.ParentID
	menu.Sort = input.Sort
	menu.IsVisible = input.IsVisible
	menu.IsEnabled = input.IsEnabled
	menu.Permission = input.Permission
	menu.MenuType = input.MenuType
	menu.ExternalURL = input.ExternalURL
	menu.Target = input.Target

	if err := m.db.Save(&menu).Error; err != nil {
		return nil, fmt.Errorf("failed to update menu: %w", err)
	}

	return &menu, nil
}

// DeleteMenu soft deletes a menu
func (m *MenuService) DeleteMenu(id uint) error {
	var menu models.Menu
	err := m.db.First(&menu, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("menu not found")
		}
		return fmt.Errorf("failed to find menu: %w", err)
	}

	// Check if menu has children
	var childCount int64
	if err := m.db.Model(&models.Menu{}).Where("parent_id = ?", id).Count(&childCount).Error; err != nil {
		return fmt.Errorf("failed to check menu children: %w", err)
	}

	if childCount > 0 {
		return errors.New("cannot delete menu with children")
	}

	// Soft delete the menu
	if err := m.db.Delete(&menu).Error; err != nil {
		return fmt.Errorf("failed to delete menu: %w", err)
	}

	return nil
}

// InitializeDefaultMenus creates default system menus
func (m *MenuService) InitializeDefaultMenus() error {
	defaultMenus := []CreateMenuInput{
		{
			Name:       "dashboard",
			Title:      "Dashboard",
			Icon:       "dashboard",
			Path:       "/dashboard",
			Component:  "Dashboard",
			Sort:       1,
			IsVisible:  true,
			IsEnabled:  true,
			Permission: "",
			MenuType:   "menu",
		},
		{
			Name:       "system",
			Title:      "System Management",
			Icon:       "settings",
			Path:       "/system",
			Component:  "",
			Sort:       2,
			IsVisible:  true,
			IsEnabled:  true,
			Permission: "system.admin",
			MenuType:   "menu",
		},
		{
			Name:       "users",
			Title:      "User Management",
			Icon:       "users",
			Path:       "/users",
			Component:  "UserList",
			Sort:       3,
			IsVisible:  true,
			IsEnabled:  true,
			Permission: "users.read",
			MenuType:   "menu",
		},
		{
			Name:       "roles",
			Title:      "Role Management",
			Icon:       "user-check",
			Path:       "/roles",
			Component:  "RoleList",
			Sort:       4,
			IsVisible:  true,
			IsEnabled:  true,
			Permission: "roles.read",
			MenuType:   "menu",
		},
		{
			Name:       "permissions",
			Title:      "Permission Management",
			Icon:       "shield",
			Path:       "/permissions",
			Component:  "PermissionList",
			Sort:       5,
			IsVisible:  true,
			IsEnabled:  true,
			Permission: "permissions.read",
			MenuType:   "menu",
		},
	}

	for _, menuInput := range defaultMenus {
		// Only create if doesn't exist
		var existingMenu models.Menu
		err := m.db.Where("name = ?", menuInput.Name).First(&existingMenu).Error
		if err != nil && err == gorm.ErrRecordNotFound {
			_, err = m.CreateMenu(menuInput)
			if err != nil {
				return fmt.Errorf("failed to create default menu %s: %w", menuInput.Name, err)
			}
		}
	}

	return nil
}
