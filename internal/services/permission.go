package services

import (
	"errors"
	"fmt"

	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/models"
)

// PermissionServiceInterface defines the interface for permission service
type PermissionServiceInterface interface {
	CreatePermission(name, resource, action, description string) (*models.Permission, error)
	GetPermissionByID(id uint) (*models.Permission, error)
	GetAllPermissions(page, limit int) ([]models.Permission, int64, error)
	GetPermissionsByResource(resource string) ([]models.Permission, error)
	UpdatePermission(id uint, name, resource, action, description string) (*models.Permission, error)
	DeletePermission(id uint) error
	GetResourceList() ([]string, error)
	GetPermissionsByResourceGrouped() (map[string][]models.Permission, error)
	SearchPermissions(query string, page, limit int) ([]models.Permission, int64, error)
}

type PermissionService struct {
	db *gorm.DB
}

// Compile-time interface compliance check
var _ PermissionServiceInterface = (*PermissionService)(nil)

// NewPermissionService creates a new permission service
func NewPermissionService(db *gorm.DB) *PermissionService {
	return &PermissionService{db: db}
}

// CreatePermission creates a new permission
func (p *PermissionService) CreatePermission(name, resource, action, description string) (*models.Permission, error) {
	// Check if permission name already exists
	var existingPermission models.Permission
	err := p.db.Where("name = ?", name).First(&existingPermission).Error
	if err == nil {
		return nil, errors.New("permission name already exists")
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check existing permission: %w", err)
	}

	// Create new permission
	permission := &models.Permission{
		Name:        name,
		Resource:    resource,
		Action:      action,
		Description: description,
	}

	if err := p.db.Create(permission).Error; err != nil {
		return nil, fmt.Errorf("failed to create permission: %w", err)
	}

	return permission, nil
}

// GetPermissionByID gets a permission by ID
func (p *PermissionService) GetPermissionByID(id uint) (*models.Permission, error) {
	var permission models.Permission
	err := p.db.First(&permission, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("permission not found")
		}
		return nil, fmt.Errorf("failed to get permission: %w", err)
	}

	return &permission, nil
}

// GetAllPermissions gets all permissions with pagination
func (p *PermissionService) GetAllPermissions(page, limit int) ([]models.Permission, int64, error) {
	var permissions []models.Permission
	var total int64

	// Count total permissions
	if err := p.db.Model(&models.Permission{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count permissions: %w", err)
	}

	// Get permissions with pagination
	offset := (page - 1) * limit
	err := p.db.Offset(offset).Limit(limit).Find(&permissions).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get permissions: %w", err)
	}

	return permissions, total, nil
}

// GetPermissionsByResource gets permissions by resource
func (p *PermissionService) GetPermissionsByResource(resource string) ([]models.Permission, error) {
	var permissions []models.Permission
	err := p.db.Where("resource = ?", resource).Find(&permissions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get permissions by resource: %w", err)
	}

	return permissions, nil
}

// UpdatePermission updates a permission
func (p *PermissionService) UpdatePermission(id uint, name, resource, action, description string) (*models.Permission, error) {
	var permission models.Permission
	err := p.db.First(&permission, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("permission not found")
		}
		return nil, fmt.Errorf("failed to find permission: %w", err)
	}

	// Check if new name conflicts with existing permission (if name is being changed)
	if permission.Name != name {
		var existingPermission models.Permission
		err := p.db.Where("name = ? AND id != ?", name, id).First(&existingPermission).Error
		if err == nil {
			return nil, errors.New("permission name already exists")
		}
		if err != gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("failed to check existing permission: %w", err)
		}
	}

	// Update permission
	permission.Name = name
	permission.Resource = resource
	permission.Action = action
	permission.Description = description

	if err := p.db.Save(&permission).Error; err != nil {
		return nil, fmt.Errorf("failed to update permission: %w", err)
	}

	return &permission, nil
}

// DeletePermission soft deletes a permission
func (p *PermissionService) DeletePermission(id uint) error {
	var permission models.Permission
	err := p.db.First(&permission, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("permission not found")
		}
		return fmt.Errorf("failed to find permission: %w", err)
	}

	// Check if permission is assigned to any roles
	var rolePermissionCount int64
	if err := p.db.Model(&models.RolePermission{}).Where("permission_id = ?", id).Count(&rolePermissionCount).Error; err != nil {
		return fmt.Errorf("failed to check permission assignments: %w", err)
	}

	if rolePermissionCount > 0 {
		return errors.New("cannot delete permission that is assigned to roles")
	}

	// Soft delete the permission
	if err := p.db.Delete(&permission).Error; err != nil {
		return fmt.Errorf("failed to delete permission: %w", err)
	}

	return nil
}

// GetResourceList gets a list of all unique resources
func (p *PermissionService) GetResourceList() ([]string, error) {
	var resources []string
	err := p.db.Model(&models.Permission{}).Distinct("resource").Pluck("resource", &resources).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get resource list: %w", err)
	}

	return resources, nil
}

// GetPermissionsByResourceGrouped gets permissions grouped by resource
func (p *PermissionService) GetPermissionsByResourceGrouped() (map[string][]models.Permission, error) {
	var permissions []models.Permission
	err := p.db.Order("resource, action").Find(&permissions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get permissions: %w", err)
	}

	// Group permissions by resource
	grouped := make(map[string][]models.Permission)
	for _, permission := range permissions {
		grouped[permission.Resource] = append(grouped[permission.Resource], permission)
	}

	return grouped, nil
}

// SearchPermissions searches permissions by name, resource, or action
func (p *PermissionService) SearchPermissions(query string, page, limit int) ([]models.Permission, int64, error) {
	var permissions []models.Permission
	var total int64

	// Build search query
	searchQuery := "%" + query + "%"
	whereClause := "name LIKE ? OR resource LIKE ? OR action LIKE ? OR description LIKE ?"

	// Count total matching permissions
	if err := p.db.Model(&models.Permission{}).Where(whereClause, searchQuery, searchQuery, searchQuery, searchQuery).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count permissions: %w", err)
	}

	// Get permissions with pagination
	offset := (page - 1) * limit
	err := p.db.Where(whereClause, searchQuery, searchQuery, searchQuery, searchQuery).
		Offset(offset).Limit(limit).Find(&permissions).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to search permissions: %w", err)
	}

	return permissions, total, nil
}
