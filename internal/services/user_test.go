package services

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/models"
)

type UserServiceTestSuite struct {
	suite.Suite
	db          *gorm.DB
	userService *UserService
}

func (suite *UserServiceTestSuite) SetupSuite() {
	// 使用内存SQLite数据库进行测试
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 运行迁移
	err = db.AutoMigrate(
		&models.User{},
		&models.Role{},
		&models.Permission{},
		&models.UserRole{},
		&models.RolePermission{},
		&models.Attribute{},
	)
	suite.Require().NoError(err)

	suite.db = db
	suite.userService = NewUserService(db)
}

func (suite *UserServiceTestSuite) TearDownTest() {
	// 清理测试数据
	suite.db.Exec("DELETE FROM users")
	suite.db.Exec("DELETE FROM user_roles")
	suite.db.Exec("DELETE FROM attributes")
}

func (suite *UserServiceTestSuite) TestCreateUser() {
	// Test: 创建用户
	user, err := suite.userService.CreateUser(
		"testuser",
		"<EMAIL>",
		"Password123!",
		"Test",
		"User",
	)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), user)
	assert.Equal(suite.T(), "testuser", user.Username)
	assert.Equal(suite.T(), "<EMAIL>", user.Email)
	assert.Equal(suite.T(), "Test", user.FirstName)
	assert.Equal(suite.T(), "User", user.LastName)
	assert.True(suite.T(), user.IsActive)
	assert.NotEmpty(suite.T(), user.Password)                 // 密码应该被哈希
	assert.NotEqual(suite.T(), "Password123!", user.Password) // 不应该是明文密码
}

func (suite *UserServiceTestSuite) TestCreateUserDuplicateUsername() {
	// 先创建一个用户
	_, err := suite.userService.CreateUser("testuser", "<EMAIL>", "Password123!", "Test", "User")
	suite.Require().NoError(err)

	// Test: 创建重复用户名的用户（应该失败）
	_, err = suite.userService.CreateUser("testuser", "<EMAIL>", "Password123!", "Test", "User2")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "username already exists")
}

func (suite *UserServiceTestSuite) TestCreateUserDuplicateEmail() {
	// 先创建一个用户
	_, err := suite.userService.CreateUser("testuser1", "<EMAIL>", "Password123!", "Test", "User")
	suite.Require().NoError(err)

	// Test: 创建重复邮箱的用户（应该失败）
	_, err = suite.userService.CreateUser("testuser2", "<EMAIL>", "Password123!", "Test", "User2")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "email already exists")
}

func (suite *UserServiceTestSuite) TestGetUserByID() {
	// 先创建用户
	createdUser, err := suite.userService.CreateUser("testuser", "<EMAIL>", "Password123!", "Test", "User")
	suite.Require().NoError(err)

	// Test: 通过ID获取用户
	user, err := suite.userService.GetUserByID(createdUser.ID)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), user)
	assert.Equal(suite.T(), createdUser.ID, user.ID)
	assert.Equal(suite.T(), "testuser", user.Username)
}

func (suite *UserServiceTestSuite) TestGetUserByIDNotFound() {
	// Test: 获取不存在的用户
	_, err := suite.userService.GetUserByID(999)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "user not found")
}

func (suite *UserServiceTestSuite) TestGetUserByUsername() {
	// 先创建用户
	_, err := suite.userService.CreateUser("testuser", "<EMAIL>", "Password123!", "Test", "User")
	suite.Require().NoError(err)

	// Test: 通过用户名获取用户
	user, err := suite.userService.GetUserByUsername("testuser")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), user)
	assert.Equal(suite.T(), "testuser", user.Username)
}

func (suite *UserServiceTestSuite) TestGetUserByUsernameNotFound() {
	// Test: 获取不存在的用户
	_, err := suite.userService.GetUserByUsername("nonexistent")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "user not found")
}

func (suite *UserServiceTestSuite) TestGetUserByEmail() {
	// 先创建用户
	_, err := suite.userService.CreateUser("testuser", "<EMAIL>", "Password123!", "Test", "User")
	suite.Require().NoError(err)

	// Test: 通过邮箱获取用户
	user, err := suite.userService.GetUserByEmail("<EMAIL>")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), user)
	assert.Equal(suite.T(), "<EMAIL>", user.Email)
}

func (suite *UserServiceTestSuite) TestGetAllUsers() {
	// 创建多个用户
	_, err := suite.userService.CreateUser("user1", "<EMAIL>", "Password123!", "User", "One")
	suite.Require().NoError(err)

	_, err = suite.userService.CreateUser("user2", "<EMAIL>", "Password123!", "User", "Two")
	suite.Require().NoError(err)

	_, err = suite.userService.CreateUser("user3", "<EMAIL>", "Password123!", "User", "Three")
	suite.Require().NoError(err)

	// Test: 获取所有用户（分页）
	users, total, err := suite.userService.GetAllUsers(1, 10)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), users, 3)
	assert.Equal(suite.T(), int64(3), total)

	// Test: 分页测试
	users, total, err = suite.userService.GetAllUsers(1, 2)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), users, 2)
	assert.Equal(suite.T(), int64(3), total)
}

func (suite *UserServiceTestSuite) TestUpdateUser() {
	// 先创建用户
	user, err := suite.userService.CreateUser("testuser", "<EMAIL>", "Password123!", "Test", "User")
	suite.Require().NoError(err)

	// Test: 更新用户
	updates := map[string]any{
		"first_name": "Updated",
		"last_name":  "Name",
		"email":      "<EMAIL>",
	}

	updatedUser, err := suite.userService.UpdateUser(user.ID, updates)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), updatedUser)
	assert.Equal(suite.T(), "Updated", updatedUser.FirstName)
	assert.Equal(suite.T(), "Name", updatedUser.LastName)
	assert.Equal(suite.T(), "<EMAIL>", updatedUser.Email)
	assert.Equal(suite.T(), "testuser", updatedUser.Username) // 用户名不应该改变
}

func (suite *UserServiceTestSuite) TestUpdateUserNotFound() {
	// Test: 更新不存在的用户
	updates := map[string]any{"first_name": "Updated"}
	_, err := suite.userService.UpdateUser(999, updates)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "user not found")
}

func (suite *UserServiceTestSuite) TestDeleteUser() {
	// 先创建用户
	user, err := suite.userService.CreateUser("testuser", "<EMAIL>", "Password123!", "Test", "User")
	suite.Require().NoError(err)

	// Test: 删除用户
	err = suite.userService.DeleteUser(user.ID)
	assert.NoError(suite.T(), err)

	// 验证用户已被软删除
	_, err = suite.userService.GetUserByID(user.ID)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "user not found")
}

func (suite *UserServiceTestSuite) TestAuthenticateUser() {
	// 先创建用户
	_, err := suite.userService.CreateUser("testuser", "<EMAIL>", "Password123!", "Test", "User")
	suite.Require().NoError(err)

	// Test: 使用用户名认证
	user, err := suite.userService.AuthenticateUser("testuser", "Password123!")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), user)
	assert.Equal(suite.T(), "testuser", user.Username)

	// Test: 使用邮箱认证
	user, err = suite.userService.AuthenticateUser("<EMAIL>", "Password123!")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), user)
	assert.Equal(suite.T(), "<EMAIL>", user.Email)

	// Test: 错误密码
	_, err = suite.userService.AuthenticateUser("testuser", "wrongpassword")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "invalid credentials")

	// Test: 不存在的用户
	_, err = suite.userService.AuthenticateUser("nonexistent", "Password123!")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "invalid credentials")
}

func (suite *UserServiceTestSuite) TestActivateUser() {
	// 先创建用户并设为非活跃
	user, err := suite.userService.CreateUser("testuser", "<EMAIL>", "Password123!", "Test", "User")
	suite.Require().NoError(err)

	err = suite.userService.DeactivateUser(user.ID)
	suite.Require().NoError(err)

	// Test: 激活用户
	err = suite.userService.ActivateUser(user.ID)
	assert.NoError(suite.T(), err)

	// 验证用户已激活
	updatedUser, err := suite.userService.GetUserByID(user.ID)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), updatedUser.IsActive)
}

func (suite *UserServiceTestSuite) TestDeactivateUser() {
	// 先创建用户
	user, err := suite.userService.CreateUser("testuser", "<EMAIL>", "Password123!", "Test", "User")
	suite.Require().NoError(err)

	// Test: 停用用户
	err = suite.userService.DeactivateUser(user.ID)
	assert.NoError(suite.T(), err)

	// 验证用户已停用
	updatedUser, err := suite.userService.GetUserByID(user.ID)
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), updatedUser.IsActive)
}

func TestUserServiceTestSuite(t *testing.T) {
	suite.Run(t, new(UserServiceTestSuite))
}
