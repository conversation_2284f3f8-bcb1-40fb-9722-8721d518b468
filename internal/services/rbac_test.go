package services

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/models"
)

type RBACServiceTestSuite struct {
	suite.Suite
	db          *gorm.DB
	rbacService *RBACService
	testUser    *models.User
	testRole    *models.Role
	testPerm    *models.Permission
}

func (suite *RBACServiceTestSuite) SetupSuite() {
	// 使用内存SQLite数据库进行测试
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 运行迁移
	err = db.AutoMigrate(
		&models.User{},
		&models.Role{},
		&models.Permission{},
		&models.UserRole{},
		&models.RolePermission{},
	)
	suite.Require().NoError(err)

	suite.db = db
	suite.rbacService = NewRBACService(db)

	// 创建测试用户
	suite.testUser = &models.User{
		Username:  "testuser",
		Email:     "<EMAIL>",
		Password:  "hashedpassword",
		FirstName: "Test",
		LastName:  "User",
		IsActive:  true,
	}
	err = db.Create(suite.testUser).Error
	suite.Require().NoError(err)

	// 创建测试角色
	suite.testRole = &models.Role{
		Name:        "test_role",
		Description: "Test Role",
		IsActive:    true,
	}
	err = db.Create(suite.testRole).Error
	suite.Require().NoError(err)

	// 创建测试权限
	suite.testPerm = &models.Permission{
		Name:        "test_permission",
		Resource:    "documents",
		Action:      "read",
		Description: "Test Permission",
	}
	err = db.Create(suite.testPerm).Error
	suite.Require().NoError(err)
}

func (suite *RBACServiceTestSuite) TearDownTest() {
	// 清理测试数据
	suite.db.Exec("DELETE FROM user_roles")
	suite.db.Exec("DELETE FROM role_permissions")
}

func (suite *RBACServiceTestSuite) TestAssignRoleToUser() {
	// Test: 分配角色给用户
	err := suite.rbacService.AssignRoleToUser(suite.testUser.ID, suite.testRole.ID)
	assert.NoError(suite.T(), err)

	// 验证角色已分配
	roles, err := suite.rbacService.GetUserRoles(suite.testUser.ID)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), roles, 1)
	assert.Equal(suite.T(), suite.testRole.ID, roles[0].ID)
}

func (suite *RBACServiceTestSuite) TestAssignDuplicateRoleToUser() {
	// 先分配角色
	err := suite.rbacService.AssignRoleToUser(suite.testUser.ID, suite.testRole.ID)
	suite.Require().NoError(err)

	// Test: 重复分配相同角色（应该返回错误）
	err = suite.rbacService.AssignRoleToUser(suite.testUser.ID, suite.testRole.ID)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "already has this role")
}

func (suite *RBACServiceTestSuite) TestRemoveRoleFromUser() {
	// 先分配角色
	err := suite.rbacService.AssignRoleToUser(suite.testUser.ID, suite.testRole.ID)
	suite.Require().NoError(err)

	// Test: 移除角色
	err = suite.rbacService.RemoveRoleFromUser(suite.testUser.ID, suite.testRole.ID)
	assert.NoError(suite.T(), err)

	// 验证角色已移除
	roles, err := suite.rbacService.GetUserRoles(suite.testUser.ID)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), roles, 0)
}

func (suite *RBACServiceTestSuite) TestGetUserRoles() {
	// 创建另一个角色
	role2 := &models.Role{
		Name:        "test_role_2",
		Description: "Test Role 2",
		IsActive:    true,
	}
	err := suite.db.Create(role2).Error
	suite.Require().NoError(err)

	// 分配多个角色
	err = suite.rbacService.AssignRoleToUser(suite.testUser.ID, suite.testRole.ID)
	suite.Require().NoError(err)

	err = suite.rbacService.AssignRoleToUser(suite.testUser.ID, role2.ID)
	suite.Require().NoError(err)

	// Test: 获取用户角色
	roles, err := suite.rbacService.GetUserRoles(suite.testUser.ID)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), roles, 2)

	// 验证角色ID
	roleIDs := make([]uint, len(roles))
	for i, role := range roles {
		roleIDs[i] = role.ID
	}
	assert.Contains(suite.T(), roleIDs, suite.testRole.ID)
	assert.Contains(suite.T(), roleIDs, role2.ID)
}

func (suite *RBACServiceTestSuite) TestAssignPermissionToRole() {
	// Test: 分配权限给角色
	err := suite.rbacService.AssignPermissionToRole(suite.testRole.ID, suite.testPerm.ID)
	assert.NoError(suite.T(), err)

	// 验证权限已分配（通过数据库查询）
	var count int64
	err = suite.db.Model(&models.RolePermission{}).
		Where("role_id = ? AND permission_id = ?", suite.testRole.ID, suite.testPerm.ID).
		Count(&count).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(1), count)
}

func (suite *RBACServiceTestSuite) TestAssignDuplicatePermissionToRole() {
	// 先分配权限
	err := suite.rbacService.AssignPermissionToRole(suite.testRole.ID, suite.testPerm.ID)
	suite.Require().NoError(err)

	// Test: 重复分配相同权限（应该返回错误）
	err = suite.rbacService.AssignPermissionToRole(suite.testRole.ID, suite.testPerm.ID)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "already has this permission")
}

func (suite *RBACServiceTestSuite) TestRemovePermissionFromRole() {
	// 先分配权限
	err := suite.rbacService.AssignPermissionToRole(suite.testRole.ID, suite.testPerm.ID)
	suite.Require().NoError(err)

	// Test: 移除权限
	err = suite.rbacService.RemovePermissionFromRole(suite.testRole.ID, suite.testPerm.ID)
	assert.NoError(suite.T(), err)

	// 验证权限已移除
	var count int64
	err = suite.db.Model(&models.RolePermission{}).
		Where("role_id = ? AND permission_id = ?", suite.testRole.ID, suite.testPerm.ID).
		Count(&count).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(0), count)
}

func (suite *RBACServiceTestSuite) TestCheckPermission() {
	// 设置完整的权限链：用户 -> 角色 -> 权限
	err := suite.rbacService.AssignRoleToUser(suite.testUser.ID, suite.testRole.ID)
	suite.Require().NoError(err)

	err = suite.rbacService.AssignPermissionToRole(suite.testRole.ID, suite.testPerm.ID)
	suite.Require().NoError(err)

	// Test: 检查用户权限（应该有权限）
	hasPermission, err := suite.rbacService.CheckPermission(suite.testUser.ID, "documents", "read")
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), hasPermission)

	// Test: 检查用户没有的权限（应该没有权限）
	hasPermission, err = suite.rbacService.CheckPermission(suite.testUser.ID, "documents", "write")
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), hasPermission)
}

func (suite *RBACServiceTestSuite) TestGetUserPermissions() {
	// 创建另一个权限
	perm2 := &models.Permission{
		Name:        "test_permission_2",
		Resource:    "files",
		Action:      "write",
		Description: "Test Permission 2",
	}
	err := suite.db.Create(perm2).Error
	suite.Require().NoError(err)

	// 设置权限链
	err = suite.rbacService.AssignRoleToUser(suite.testUser.ID, suite.testRole.ID)
	suite.Require().NoError(err)

	err = suite.rbacService.AssignPermissionToRole(suite.testRole.ID, suite.testPerm.ID)
	suite.Require().NoError(err)

	err = suite.rbacService.AssignPermissionToRole(suite.testRole.ID, perm2.ID)
	suite.Require().NoError(err)

	// Test: 获取用户权限
	permissions, err := suite.rbacService.GetUserPermissions(suite.testUser.ID)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), permissions, 2)

	// 验证权限内容
	permNames := make([]string, len(permissions))
	for i, perm := range permissions {
		permNames[i] = perm.Name
	}
	assert.Contains(suite.T(), permNames, "test_permission")
	assert.Contains(suite.T(), permNames, "test_permission_2")
}

func TestRBACServiceTestSuite(t *testing.T) {
	suite.Run(t, new(RBACServiceTestSuite))
}
