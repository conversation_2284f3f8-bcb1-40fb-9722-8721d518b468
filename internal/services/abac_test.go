package services

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/models"
)

type ABACServiceTestSuite struct {
	suite.Suite
	db          *gorm.DB
	abacService *ABACService
	testUser    *models.User
}

func (suite *ABACServiceTestSuite) SetupSuite() {
	// 使用内存SQLite数据库进行测试
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 运行迁移
	err = db.AutoMigrate(
		&models.User{},
		&models.Attribute{},
		&models.Policy{},
	)
	suite.Require().NoError(err)

	suite.db = db
	suite.abacService = NewABACService(db)

	// 创建测试用户
	suite.testUser = &models.User{
		Username:  "testuser",
		Email:     "<EMAIL>",
		Password:  "hashedpassword",
		FirstName: "Test",
		LastName:  "User",
		IsActive:  true,
	}
	err = db.Create(suite.testUser).Error
	suite.Require().NoError(err)
}

func (suite *ABACServiceTestSuite) TearDownTest() {
	// 清理测试数据
	suite.db.Exec("DELETE FROM attributes")
	suite.db.Exec("DELETE FROM policies")
}

func (suite *ABACServiceTestSuite) TestSetUserAttribute() {
	// Test: 设置用户属性
	err := suite.abacService.SetUserAttribute(suite.testUser.ID, "department", "engineering", "string")
	assert.NoError(suite.T(), err)

	// 验证属性已设置
	attributes, err := suite.abacService.GetUserAttributes(suite.testUser.ID)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), attributes, 1)
	assert.Equal(suite.T(), "department", attributes[0].Name)
	assert.Equal(suite.T(), "engineering", attributes[0].Value)
	assert.Equal(suite.T(), "string", attributes[0].Type)
}

func (suite *ABACServiceTestSuite) TestGetUserAttributes() {
	// 设置多个属性
	err := suite.abacService.SetUserAttribute(suite.testUser.ID, "department", "engineering", "string")
	suite.Require().NoError(err)

	err = suite.abacService.SetUserAttribute(suite.testUser.ID, "level", "senior", "string")
	suite.Require().NoError(err)

	// Test: 获取用户属性
	attributes, err := suite.abacService.GetUserAttributes(suite.testUser.ID)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), attributes, 2)

	// 验证属性内容
	attrMap := make(map[string]string)
	for _, attr := range attributes {
		attrMap[attr.Name] = attr.Value
	}
	assert.Equal(suite.T(), "engineering", attrMap["department"])
	assert.Equal(suite.T(), "senior", attrMap["level"])
}

func (suite *ABACServiceTestSuite) TestUpdateUserAttribute() {
	// 设置初始属性
	err := suite.abacService.SetUserAttribute(suite.testUser.ID, "department", "engineering", "string")
	suite.Require().NoError(err)

	// Test: 更新属性
	err = suite.abacService.SetUserAttribute(suite.testUser.ID, "department", "marketing", "string")
	assert.NoError(suite.T(), err)

	// 验证属性已更新
	attributes, err := suite.abacService.GetUserAttributes(suite.testUser.ID)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), attributes, 1)
	assert.Equal(suite.T(), "marketing", attributes[0].Value)
}

func (suite *ABACServiceTestSuite) TestCreatePolicy() {
	// Test: 创建策略
	conditions := PolicyRule{
		Conditions: []PolicyCondition{
			{
				Attribute: "department",
				Operator:  "eq",
				Value:     "engineering",
			},
		},
		Logic: "and",
	}

	err := suite.abacService.CreatePolicy(
		"Engineering Access",
		"Allow engineering department access",
		"documents",
		"read",
		"allow",
		conditions,
	)
	assert.NoError(suite.T(), err)

	// 验证策略已创建
	policies, err := suite.abacService.GetPolicies("documents", "read")
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), policies, 1)
	assert.Equal(suite.T(), "Engineering Access", policies[0].Name)
	assert.Equal(suite.T(), "allow", policies[0].Effect)
}

func (suite *ABACServiceTestSuite) TestGetPolicies() {
	// 创建多个策略
	conditions1 := PolicyRule{
		Conditions: []PolicyCondition{
			{Attribute: "department", Operator: "eq", Value: "engineering"},
		},
		Logic: "and",
	}

	conditions2 := PolicyRule{
		Conditions: []PolicyCondition{
			{Attribute: "level", Operator: "eq", Value: "senior"},
		},
		Logic: "and",
	}

	err := suite.abacService.CreatePolicy("Policy1", "Description1", "documents", "read", "allow", conditions1)
	suite.Require().NoError(err)

	err = suite.abacService.CreatePolicy("Policy2", "Description2", "documents", "read", "allow", conditions2)
	suite.Require().NoError(err)

	err = suite.abacService.CreatePolicy("Policy3", "Description3", "files", "write", "deny", conditions1)
	suite.Require().NoError(err)

	// Test: 获取特定资源和动作的策略
	policies, err := suite.abacService.GetPolicies("documents", "read")
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), policies, 2)

	// Test: 获取不存在的策略
	policies, err = suite.abacService.GetPolicies("nonexistent", "action")
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), policies, 0)
}

func (suite *ABACServiceTestSuite) TestCheckAccess() {
	// 设置用户属性
	err := suite.abacService.SetUserAttribute(suite.testUser.ID, "department", "engineering", "string")
	suite.Require().NoError(err)

	// 创建允许策略
	conditions := PolicyRule{
		Conditions: []PolicyCondition{
			{
				Attribute: "department",
				Operator:  "eq",
				Value:     "engineering",
			},
		},
		Logic: "and",
	}

	err = suite.abacService.CreatePolicy(
		"Engineering Access",
		"Allow engineering access",
		"documents",
		"read",
		"allow",
		conditions,
	)
	suite.Require().NoError(err)

	// Test: 检查访问权限（应该允许）
	hasAccess, err := suite.abacService.CheckAccess(suite.testUser.ID, "documents", "read", nil)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), hasAccess)

	// Test: 检查不匹配的访问权限（应该拒绝）
	hasAccess, err = suite.abacService.CheckAccess(suite.testUser.ID, "documents", "write", nil)
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), hasAccess)
}

func (suite *ABACServiceTestSuite) TestConvertAttributeValue() {
	// Test: 字符串类型（默认）
	result := suite.abacService.convertAttributeValue("test string", "string")
	assert.Equal(suite.T(), "test string", result)

	// Test: 数字类型
	result = suite.abacService.convertAttributeValue("123.45", "number")
	assert.Equal(suite.T(), 123.45, result)

	// Test: 无效数字，应该返回原字符串
	result = suite.abacService.convertAttributeValue("invalid number", "number")
	assert.Equal(suite.T(), "invalid number", result)

	// Test: 布尔类型 - true
	result = suite.abacService.convertAttributeValue("true", "boolean")
	assert.Equal(suite.T(), true, result)

	// Test: 布尔类型 - false
	result = suite.abacService.convertAttributeValue("false", "boolean")
	assert.Equal(suite.T(), false, result)

	// Test: 无效布尔值，应该返回原字符串
	result = suite.abacService.convertAttributeValue("invalid bool", "boolean")
	assert.Equal(suite.T(), "invalid bool", result)

	// Test: 日期类型
	dateStr := "2023-12-25T10:30:00Z"
	result = suite.abacService.convertAttributeValue(dateStr, "date")
	assert.IsType(suite.T(), time.Time{}, result)

	// Test: 无效日期，应该返回原字符串
	result = suite.abacService.convertAttributeValue("invalid date", "date")
	assert.Equal(suite.T(), "invalid date", result)

	// Test: 未知类型，应该返回原字符串
	result = suite.abacService.convertAttributeValue("test value", "unknown")
	assert.Equal(suite.T(), "test value", result)
}

func TestABACServiceTestSuite(t *testing.T) {
	suite.Run(t, new(ABACServiceTestSuite))
}
