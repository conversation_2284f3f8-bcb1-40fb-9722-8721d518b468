package services

import (
	"errors"
	"fmt"

	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/models"
)

// SettingServiceInterface defines the interface for setting service
type SettingServiceInterface interface {
	SetSetting(key, value, settingType, category, description string, isPublic, isEditable bool) error
	GetSetting(key string) (*models.SystemSetting, error)
	GetSettingValue(key string) (string, error)
	GetSettingsByCategory(category string) ([]models.SystemSetting, error)
	GetAllSettings(isAdmin bool) ([]models.SystemSetting, error)
	DeleteSetting(key string) error
	GetCategories() ([]string, error)
	BulkSetSettings(settings map[string]SettingInput) error
	InitializeDefaultSettings() error
}

type SettingService struct {
	db *gorm.DB
}

// Compile-time interface compliance check
var _ SettingServiceInterface = (*SettingService)(nil)

// SettingInput represents input for setting operations
type SettingInput struct {
	Value       string `json:"value"`
	Type        string `json:"type"`
	Category    string `json:"category"`
	Description string `json:"description"`
	IsPublic    bool   `json:"is_public"`
	IsEditable  bool   `json:"is_editable"`
}

// NewSettingService creates a new setting service
func NewSettingService(db *gorm.DB) *SettingService {
	return &SettingService{db: db}
}

// SetSetting creates or updates a system setting
func (s *SettingService) SetSetting(key, value, settingType, category, description string, isPublic, isEditable bool) error {
	var setting models.SystemSetting
	err := s.db.Where("key = ?", key).First(&setting).Error

	if err == gorm.ErrRecordNotFound {
		// Create new setting
		setting = models.SystemSetting{
			Key:         key,
			Value:       value,
			Type:        settingType,
			Category:    category,
			Description: description,
			IsPublic:    isPublic,
			IsEditable:  isEditable,
		}
		if err := s.db.Create(&setting).Error; err != nil {
			return fmt.Errorf("failed to create setting: %w", err)
		}
	} else if err != nil {
		return fmt.Errorf("failed to check existing setting: %w", err)
	} else {
		// Update existing setting
		if !setting.IsEditable {
			return errors.New("setting is not editable")
		}

		setting.Value = value
		setting.Type = settingType
		setting.Category = category
		setting.Description = description
		setting.IsPublic = isPublic
		setting.IsEditable = isEditable

		if err := s.db.Save(&setting).Error; err != nil {
			return fmt.Errorf("failed to update setting: %w", err)
		}
	}

	return nil
}

// GetSetting retrieves a system setting by key
func (s *SettingService) GetSetting(key string) (*models.SystemSetting, error) {
	var setting models.SystemSetting
	err := s.db.Where("key = ?", key).First(&setting).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("setting not found")
		}
		return nil, fmt.Errorf("failed to get setting: %w", err)
	}

	return &setting, nil
}

// GetSettingValue retrieves only the value of a system setting
func (s *SettingService) GetSettingValue(key string) (string, error) {
	setting, err := s.GetSetting(key)
	if err != nil {
		return "", err
	}
	return setting.Value, nil
}

// GetSettingsByCategory retrieves all settings in a specific category
func (s *SettingService) GetSettingsByCategory(category string) ([]models.SystemSetting, error) {
	var settings []models.SystemSetting
	err := s.db.Where("category = ?", category).Order("key").Find(&settings).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get settings by category: %w", err)
	}

	return settings, nil
}

// GetAllSettings retrieves all system settings
func (s *SettingService) GetAllSettings(isAdmin bool) ([]models.SystemSetting, error) {
	var settings []models.SystemSetting
	query := s.db.Order("category, key")

	// Non-admin users can only see public settings
	if !isAdmin {
		query = query.Where("is_public = ?", true)
	}

	err := query.Find(&settings).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get all settings: %w", err)
	}

	return settings, nil
}

// DeleteSetting deletes a system setting
func (s *SettingService) DeleteSetting(key string) error {
	var setting models.SystemSetting
	err := s.db.Where("key = ?", key).First(&setting).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("setting not found")
		}
		return fmt.Errorf("failed to find setting: %w", err)
	}

	if !setting.IsEditable {
		return errors.New("setting is not editable")
	}

	if err := s.db.Delete(&setting).Error; err != nil {
		return fmt.Errorf("failed to delete setting: %w", err)
	}

	return nil
}

// GetCategories retrieves all unique setting categories
func (s *SettingService) GetCategories() ([]string, error) {
	var categories []string
	err := s.db.Model(&models.SystemSetting{}).Distinct("category").Pluck("category", &categories).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get categories: %w", err)
	}

	return categories, nil
}

// BulkSetSettings sets multiple settings at once
func (s *SettingService) BulkSetSettings(settings map[string]SettingInput) error {
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for key, input := range settings {
		err := s.setSettingInTx(tx, key, input.Value, input.Type, input.Category, input.Description, input.IsPublic, input.IsEditable)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to set setting %s: %w", key, err)
		}
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit bulk settings: %w", err)
	}

	return nil
}

// setSettingInTx is a helper method to set a setting within a transaction
func (s *SettingService) setSettingInTx(tx *gorm.DB, key, value, settingType, category, description string, isPublic, isEditable bool) error {
	var setting models.SystemSetting
	err := tx.Where("key = ?", key).First(&setting).Error

	if err == gorm.ErrRecordNotFound {
		// Create new setting
		setting = models.SystemSetting{
			Key:         key,
			Value:       value,
			Type:        settingType,
			Category:    category,
			Description: description,
			IsPublic:    isPublic,
			IsEditable:  isEditable,
		}
		if err := tx.Create(&setting).Error; err != nil {
			return fmt.Errorf("failed to create setting: %w", err)
		}
	} else if err != nil {
		return fmt.Errorf("failed to check existing setting: %w", err)
	} else {
		// Update existing setting
		if !setting.IsEditable {
			return errors.New("setting is not editable")
		}

		setting.Value = value
		setting.Type = settingType
		setting.Category = category
		setting.Description = description
		setting.IsPublic = isPublic
		setting.IsEditable = isEditable

		if err := tx.Save(&setting).Error; err != nil {
			return fmt.Errorf("failed to update setting: %w", err)
		}
	}

	return nil
}

// InitializeDefaultSettings creates default system settings if they don't exist
func (s *SettingService) InitializeDefaultSettings() error {
	defaultSettings := map[string]SettingInput{
		"app.name": {
			Value:       "Admin System",
			Type:        "string",
			Category:    "general",
			Description: "Application name",
			IsPublic:    true,
			IsEditable:  true,
		},
		"app.version": {
			Value:       "1.0.0",
			Type:        "string",
			Category:    "general",
			Description: "Application version",
			IsPublic:    true,
			IsEditable:  false,
		},
		"app.description": {
			Value:       "A modern admin system built with Go and Vue.js",
			Type:        "string",
			Category:    "general",
			Description: "Application description",
			IsPublic:    true,
			IsEditable:  true,
		},
		"security.session_timeout": {
			Value:       "3600",
			Type:        "number",
			Category:    "security",
			Description: "Session timeout in seconds",
			IsPublic:    false,
			IsEditable:  true,
		},
		"security.max_login_attempts": {
			Value:       "5",
			Type:        "number",
			Category:    "security",
			Description: "Maximum login attempts before lockout",
			IsPublic:    false,
			IsEditable:  true,
		},
		"email.enabled": {
			Value:       "false",
			Type:        "boolean",
			Category:    "email",
			Description: "Enable email notifications",
			IsPublic:    false,
			IsEditable:  true,
		},
		"email.smtp_host": {
			Value:       "",
			Type:        "string",
			Category:    "email",
			Description: "SMTP server host",
			IsPublic:    false,
			IsEditable:  true,
		},
		"email.smtp_port": {
			Value:       "587",
			Type:        "number",
			Category:    "email",
			Description: "SMTP server port",
			IsPublic:    false,
			IsEditable:  true,
		},
	}

	for key, input := range defaultSettings {
		// Only create if doesn't exist
		_, err := s.GetSetting(key)
		if err != nil && err.Error() == "setting not found" {
			err = s.SetSetting(key, input.Value, input.Type, input.Category, input.Description, input.IsPublic, input.IsEditable)
			if err != nil {
				return fmt.Errorf("failed to initialize default setting %s: %w", key, err)
			}
		}
	}

	return nil
}
