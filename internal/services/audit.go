package services

import (
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/models"
)

// AuditServiceInterface defines the interface for audit service
type AuditServiceInterface interface {
	GetAuditLogs(filter AuditLogFilter) ([]models.AuditLog, int64, error)
	GetUserAuditLogs(userID uint, page, limit int) ([]models.AuditLog, int64, error)
	GetAuditStats() (*AuditStats, error)
	CleanupOldLogs(daysToKeep int) (int64, error)
}

type AuditService struct {
	db *gorm.DB
}

// Compile-time interface compliance check
var _ AuditServiceInterface = (*AuditService)(nil)

// LogActionParams contains parameters for logging an action
type LogActionParams struct {
	UserID     *uint
	Username   string
	Action     string
	Resource   string
	ResourceID *uint
	Method     string
	Path       string
	IPAddress  string
	UserAgent  string
	Status     string
	Message    string
	Duration   int64
}

// AuditLogFilter contains filter parameters for querying audit logs
type AuditLogFilter struct {
	Page       int
	Limit      int
	Username   string
	Action     string
	Resource   string
	Status     string
	IPAddress  string
	StartDate  *time.Time
	EndDate    *time.Time
	ResourceID *uint
}

// AuditStats contains audit statistics
type AuditStats struct {
	TotalLogs         int64           `json:"total_logs"`
	LogsLast24h       int64           `json:"logs_last_24h"`
	SuccessfulActions int64           `json:"successful_actions"`
	FailedActions     int64           `json:"failed_actions"`
	UniqueUsers       int64           `json:"unique_users"`
	TopActions        []ActionCount   `json:"top_actions"`
	TopResources      []ResourceCount `json:"top_resources"`
}

type ActionCount struct {
	Action string `json:"action"`
	Count  int64  `json:"count"`
}

type ResourceCount struct {
	Resource string `json:"resource"`
	Count    int64  `json:"count"`
}

// NewAuditService creates a new audit service
func NewAuditService(db *gorm.DB) *AuditService {
	return &AuditService{db: db}
}

// LogAction logs a user action
func (a *AuditService) LogAction(params LogActionParams) error {
	log := &models.AuditLog{
		UserID:     params.UserID,
		Username:   params.Username,
		Action:     params.Action,
		Resource:   params.Resource,
		ResourceID: params.ResourceID,
		Method:     params.Method,
		Path:       params.Path,
		IPAddress:  params.IPAddress,
		UserAgent:  params.UserAgent,
		Status:     params.Status,
		Message:    params.Message,
		Duration:   params.Duration,
		CreatedAt:  time.Now(),
	}

	if err := a.db.Create(log).Error; err != nil {
		return fmt.Errorf("failed to create audit log: %w", err)
	}

	return nil
}

// LogActionWithValues logs a user action with old and new values
func (a *AuditService) LogActionWithValues(params LogActionParams, oldValues, newValues interface{}) error {
	var oldJSON, newJSON string

	if oldValues != nil {
		oldBytes, err := json.Marshal(oldValues)
		if err != nil {
			return fmt.Errorf("failed to marshal old values: %w", err)
		}
		oldJSON = string(oldBytes)
	}

	if newValues != nil {
		newBytes, err := json.Marshal(newValues)
		if err != nil {
			return fmt.Errorf("failed to marshal new values: %w", err)
		}
		newJSON = string(newBytes)
	}

	log := &models.AuditLog{
		UserID:     params.UserID,
		Username:   params.Username,
		Action:     params.Action,
		Resource:   params.Resource,
		ResourceID: params.ResourceID,
		Method:     params.Method,
		Path:       params.Path,
		IPAddress:  params.IPAddress,
		UserAgent:  params.UserAgent,
		Status:     params.Status,
		Message:    params.Message,
		OldValues:  oldJSON,
		NewValues:  newJSON,
		Duration:   params.Duration,
		CreatedAt:  time.Now(),
	}

	if err := a.db.Create(log).Error; err != nil {
		return fmt.Errorf("failed to create audit log: %w", err)
	}

	return nil
}

// LogSystemAction logs a system action (no user)
func (a *AuditService) LogSystemAction(action, resource, message string) error {
	return a.LogAction(LogActionParams{
		Username: "system",
		Action:   action,
		Resource: resource,
		Status:   "success",
		Message:  message,
	})
}

// LogLoginAttempt logs a login attempt
func (a *AuditService) LogLoginAttempt(username, ipAddress, userAgent string, success bool, message string) error {
	status := "success"
	if !success {
		status = "failed"
	}

	return a.LogAction(LogActionParams{
		Username:  username,
		Action:    "login",
		Resource:  "auth",
		IPAddress: ipAddress,
		UserAgent: userAgent,
		Status:    status,
		Message:   message,
	})
}

// GetAuditLogs retrieves audit logs with filtering and pagination
func (a *AuditService) GetAuditLogs(filter AuditLogFilter) ([]models.AuditLog, int64, error) {
	var logs []models.AuditLog
	var total int64

	query := a.db.Model(&models.AuditLog{})

	// Apply filters
	if filter.Username != "" {
		query = query.Where("username = ?", filter.Username)
	}
	if filter.Action != "" {
		query = query.Where("action = ?", filter.Action)
	}
	if filter.Resource != "" {
		query = query.Where("resource = ?", filter.Resource)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.IPAddress != "" {
		query = query.Where("ip_address = ?", filter.IPAddress)
	}
	if filter.ResourceID != nil {
		query = query.Where("resource_id = ?", *filter.ResourceID)
	}
	if filter.StartDate != nil {
		query = query.Where("created_at >= ?", *filter.StartDate)
	}
	if filter.EndDate != nil {
		query = query.Where("created_at <= ?", *filter.EndDate)
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count audit logs: %w", err)
	}

	// Get logs with pagination
	offset := (filter.Page - 1) * filter.Limit
	err := query.Preload("User").
		Order("created_at DESC").
		Offset(offset).
		Limit(filter.Limit).
		Find(&logs).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get audit logs: %w", err)
	}

	return logs, total, nil
}

// GetUserAuditLogs retrieves audit logs for a specific user
func (a *AuditService) GetUserAuditLogs(userID uint, page, limit int) ([]models.AuditLog, int64, error) {
	var logs []models.AuditLog
	var total int64

	query := a.db.Model(&models.AuditLog{}).Where("user_id = ?", userID)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count user audit logs: %w", err)
	}

	// Get logs with pagination
	offset := (page - 1) * limit
	err := query.Preload("User").
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&logs).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get user audit logs: %w", err)
	}

	return logs, total, nil
}

// GetAuditStats retrieves audit statistics
func (a *AuditService) GetAuditStats() (*AuditStats, error) {
	stats := &AuditStats{}

	// Total logs
	if err := a.db.Model(&models.AuditLog{}).Count(&stats.TotalLogs).Error; err != nil {
		return nil, fmt.Errorf("failed to count total logs: %w", err)
	}

	// Logs in last 24 hours
	last24h := time.Now().Add(-24 * time.Hour)
	if err := a.db.Model(&models.AuditLog{}).Where("created_at >= ?", last24h).Count(&stats.LogsLast24h).Error; err != nil {
		return nil, fmt.Errorf("failed to count logs last 24h: %w", err)
	}

	// Successful actions
	if err := a.db.Model(&models.AuditLog{}).Where("status = ?", "success").Count(&stats.SuccessfulActions).Error; err != nil {
		return nil, fmt.Errorf("failed to count successful actions: %w", err)
	}

	// Failed actions
	if err := a.db.Model(&models.AuditLog{}).Where("status = ?", "failed").Count(&stats.FailedActions).Error; err != nil {
		return nil, fmt.Errorf("failed to count failed actions: %w", err)
	}

	// Unique users
	if err := a.db.Model(&models.AuditLog{}).Distinct("username").Count(&stats.UniqueUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count unique users: %w", err)
	}

	// Top actions
	var topActions []ActionCount
	if err := a.db.Model(&models.AuditLog{}).
		Select("action, COUNT(*) as count").
		Group("action").
		Order("count DESC").
		Limit(10).
		Find(&topActions).Error; err != nil {
		return nil, fmt.Errorf("failed to get top actions: %w", err)
	}
	stats.TopActions = topActions

	// Top resources
	var topResources []ResourceCount
	if err := a.db.Model(&models.AuditLog{}).
		Select("resource, COUNT(*) as count").
		Group("resource").
		Order("count DESC").
		Limit(10).
		Find(&topResources).Error; err != nil {
		return nil, fmt.Errorf("failed to get top resources: %w", err)
	}
	stats.TopResources = topResources

	return stats, nil
}

// CleanupOldLogs removes audit logs older than the specified number of days
func (a *AuditService) CleanupOldLogs(daysToKeep int) (int64, error) {
	cutoffDate := time.Now().AddDate(0, 0, -daysToKeep)

	result := a.db.Where("created_at < ?", cutoffDate).Delete(&models.AuditLog{})
	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup old logs: %w", result.Error)
	}

	return result.RowsAffected, nil
}
