package services

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/models"
)

type PermissionServiceTestSuite struct {
	suite.Suite
	db                *gorm.DB
	permissionService *PermissionService
}

func (suite *PermissionServiceTestSuite) SetupSuite() {
	// 使用内存SQLite数据库进行测试
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 运行迁移
	err = db.AutoMigrate(
		&models.User{},
		&models.Role{},
		&models.Permission{},
		&models.UserRole{},
		&models.RolePermission{},
	)
	suite.Require().NoError(err)

	suite.db = db
	suite.permissionService = NewPermissionService(db)
}

func (suite *PermissionServiceTestSuite) TearDownTest() {
	// 清理测试数据
	suite.db.Exec("DELETE FROM role_permissions")
	suite.db.Exec("DELETE FROM user_roles")
	suite.db.Exec("DELETE FROM permissions")
	suite.db.Exec("DELETE FROM roles")
	suite.db.Exec("DELETE FROM users")
}

func (suite *PermissionServiceTestSuite) TestCreatePermission() {
	// Test: 创建权限成功
	permission, err := suite.permissionService.CreatePermission("test.permission", "test", "permission", "Test Permission")

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), permission)
	assert.Equal(suite.T(), "test.permission", permission.Name)
	assert.Equal(suite.T(), "test", permission.Resource)
	assert.Equal(suite.T(), "permission", permission.Action)
	assert.Equal(suite.T(), "Test Permission", permission.Description)
	assert.NotZero(suite.T(), permission.ID)

	// Test: 创建重复权限名应该失败
	_, err = suite.permissionService.CreatePermission("test.permission", "test2", "permission2", "Another Permission")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "permission name already exists")
}

func (suite *PermissionServiceTestSuite) TestGetPermissionByID() {
	// 先创建一个权限
	createdPermission, err := suite.permissionService.CreatePermission("test.permission", "test", "permission", "Test Permission")
	suite.Require().NoError(err)

	// Test: 获取存在的权限
	permission, err := suite.permissionService.GetPermissionByID(createdPermission.ID)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), permission)
	assert.Equal(suite.T(), createdPermission.ID, permission.ID)
	assert.Equal(suite.T(), "test.permission", permission.Name)

	// Test: 获取不存在的权限
	_, err = suite.permissionService.GetPermissionByID(999)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "permission not found")
}

func (suite *PermissionServiceTestSuite) TestGetAllPermissions() {
	// 创建多个权限
	_, err := suite.permissionService.CreatePermission("permission1", "resource1", "action1", "Permission 1")
	suite.Require().NoError(err)
	_, err = suite.permissionService.CreatePermission("permission2", "resource2", "action2", "Permission 2")
	suite.Require().NoError(err)

	// Test: 获取所有权限（分页）
	permissions, total, err := suite.permissionService.GetAllPermissions(1, 10)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), permissions, 2)
	assert.Equal(suite.T(), int64(2), total)

	// Test: 分页测试
	permissions, total, err = suite.permissionService.GetAllPermissions(1, 1)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), permissions, 1)
	assert.Equal(suite.T(), int64(2), total)
}

func (suite *PermissionServiceTestSuite) TestGetPermissionsByResource() {
	// 创建不同资源的权限
	_, err := suite.permissionService.CreatePermission("users.read", "users", "read", "Read Users")
	suite.Require().NoError(err)
	_, err = suite.permissionService.CreatePermission("users.write", "users", "write", "Write Users")
	suite.Require().NoError(err)
	_, err = suite.permissionService.CreatePermission("roles.read", "roles", "read", "Read Roles")
	suite.Require().NoError(err)

	// Test: 按资源获取权限
	permissions, err := suite.permissionService.GetPermissionsByResource("users")
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), permissions, 2)
	for _, perm := range permissions {
		assert.Equal(suite.T(), "users", perm.Resource)
	}

	// Test: 获取不存在资源的权限
	permissions, err = suite.permissionService.GetPermissionsByResource("nonexistent")
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), permissions, 0)
}

func (suite *PermissionServiceTestSuite) TestUpdatePermission() {
	// 先创建一个权限
	createdPermission, err := suite.permissionService.CreatePermission("test.permission", "test", "permission", "Original Description")
	suite.Require().NoError(err)

	// Test: 更新权限成功
	updatedPermission, err := suite.permissionService.UpdatePermission(createdPermission.ID, "updated.permission", "updated", "updated_action", "Updated Description")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), updatedPermission)
	assert.Equal(suite.T(), "updated.permission", updatedPermission.Name)
	assert.Equal(suite.T(), "updated", updatedPermission.Resource)
	assert.Equal(suite.T(), "updated_action", updatedPermission.Action)
	assert.Equal(suite.T(), "Updated Description", updatedPermission.Description)

	// Test: 更新不存在的权限
	_, err = suite.permissionService.UpdatePermission(999, "nonexistent", "resource", "action", "Description")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "permission not found")
}

func (suite *PermissionServiceTestSuite) TestDeletePermission() {
	// 先创建一个权限
	createdPermission, err := suite.permissionService.CreatePermission("test.permission", "test", "permission", "Test Description")
	suite.Require().NoError(err)

	// Test: 删除权限成功
	err = suite.permissionService.DeletePermission(createdPermission.ID)
	assert.NoError(suite.T(), err)

	// 验证权限已被软删除
	_, err = suite.permissionService.GetPermissionByID(createdPermission.ID)
	assert.Error(suite.T(), err)

	// Test: 删除不存在的权限
	err = suite.permissionService.DeletePermission(999)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "permission not found")
}

func (suite *PermissionServiceTestSuite) TestDeletePermissionWithRoleAssignment() {
	// 创建权限和角色
	permission, err := suite.permissionService.CreatePermission("test.permission", "test", "permission", "Test Permission")
	suite.Require().NoError(err)

	role := &models.Role{
		Name:        "test_role",
		Description: "Test Role",
		IsActive:    true,
	}
	err = suite.db.Create(role).Error
	suite.Require().NoError(err)

	// 分配权限给角色
	rolePermission := &models.RolePermission{
		RoleID:       role.ID,
		PermissionID: permission.ID,
	}
	err = suite.db.Create(rolePermission).Error
	suite.Require().NoError(err)

	// Test: 删除已分配给角色的权限应该失败
	err = suite.permissionService.DeletePermission(permission.ID)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "cannot delete permission that is assigned to roles")
}

func (suite *PermissionServiceTestSuite) TestGetResourceList() {
	// 创建不同资源的权限
	_, err := suite.permissionService.CreatePermission("users.read", "users", "read", "Read Users")
	suite.Require().NoError(err)
	_, err = suite.permissionService.CreatePermission("users.write", "users", "write", "Write Users")
	suite.Require().NoError(err)
	_, err = suite.permissionService.CreatePermission("roles.read", "roles", "read", "Read Roles")
	suite.Require().NoError(err)
	_, err = suite.permissionService.CreatePermission("system.admin", "system", "admin", "System Admin")
	suite.Require().NoError(err)

	// Test: 获取资源列表
	resources, err := suite.permissionService.GetResourceList()
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), resources, 3) // users, roles, system
	assert.Contains(suite.T(), resources, "users")
	assert.Contains(suite.T(), resources, "roles")
	assert.Contains(suite.T(), resources, "system")
}

func (suite *PermissionServiceTestSuite) TestGetPermissionsByResourceGrouped() {
	// 创建不同资源的权限
	permissions := []struct {
		name     string
		resource string
		action   string
	}{
		{"User Create", "user", "create"},
		{"User Read", "user", "read"},
		{"User Update", "user", "update"},
		{"Role Create", "role", "create"},
		{"Role Read", "role", "read"},
		{"System Config", "system", "config"},
	}

	for _, p := range permissions {
		_, err := suite.permissionService.CreatePermission(p.name, p.resource, p.action, "Test permission")
		suite.Require().NoError(err)
	}

	// Test: 获取按资源分组的权限
	grouped, err := suite.permissionService.GetPermissionsByResourceGrouped()
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), grouped, 3) // user, role, system

	// 验证user资源的权限
	userPermissions, exists := grouped["user"]
	assert.True(suite.T(), exists)
	assert.Len(suite.T(), userPermissions, 3) // create, read, update

	// 验证role资源的权限
	rolePermissions, exists := grouped["role"]
	assert.True(suite.T(), exists)
	assert.Len(suite.T(), rolePermissions, 2) // create, read

	// 验证system资源的权限
	systemPermissions, exists := grouped["system"]
	assert.True(suite.T(), exists)
	assert.Len(suite.T(), systemPermissions, 1) // config
}

func (suite *PermissionServiceTestSuite) TestSearchPermissions() {
	// 创建测试权限
	permissions := []struct {
		name        string
		resource    string
		action      string
		description string
	}{
		{"User Management", "user", "manage", "Manage user accounts"},
		{"User View", "user", "view", "View user information"},
		{"Role Administration", "role", "admin", "Administer roles"},
		{"System Configuration", "system", "config", "Configure system settings"},
	}

	for _, p := range permissions {
		_, err := suite.permissionService.CreatePermission(p.name, p.resource, p.action, p.description)
		suite.Require().NoError(err)
	}

	// Test: 按名称搜索
	results, total, err := suite.permissionService.SearchPermissions("User", 1, 10)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), results, 2) // "User Management" 和 "User View"
	assert.Equal(suite.T(), int64(2), total)

	// Test: 按资源搜索
	results, total, err = suite.permissionService.SearchPermissions("role", 1, 10)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), results, 1) // "Role Administration"
	assert.Equal(suite.T(), int64(1), total)

	// Test: 按操作搜索
	results, total, err = suite.permissionService.SearchPermissions("config", 1, 10)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), results, 1) // "System Configuration"
	assert.Equal(suite.T(), int64(1), total)

	// Test: 按描述搜索
	results, total, err = suite.permissionService.SearchPermissions("Manage", 1, 10)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), results, 1) // "User Management"
	assert.Equal(suite.T(), int64(1), total)

	// Test: 搜索不存在的内容
	results, total, err = suite.permissionService.SearchPermissions("nonexistent", 1, 10)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), results, 0)
	assert.Equal(suite.T(), int64(0), total)

	// Test: 分页测试
	results, total, err = suite.permissionService.SearchPermissions("", 1, 2) // 搜索所有
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), results, 2)
	assert.Equal(suite.T(), int64(4), total)
}

func TestPermissionServiceTestSuite(t *testing.T) {
	suite.Run(t, new(PermissionServiceTestSuite))
}
