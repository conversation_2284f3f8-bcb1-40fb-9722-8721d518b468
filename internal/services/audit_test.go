package services

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/models"
)

type AuditServiceTestSuite struct {
	suite.Suite
	db           *gorm.DB
	auditService *AuditService
	testUser     *models.User
}

func (suite *AuditServiceTestSuite) SetupSuite() {
	// 使用内存SQLite数据库进行测试
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 运行迁移
	err = db.AutoMigrate(
		&models.User{},
		&models.AuditLog{},
	)
	suite.Require().NoError(err)

	suite.db = db
	suite.auditService = NewAuditService(db)

	// 创建测试用户
	suite.testUser = &models.User{
		Username:  "testuser",
		Email:     "<EMAIL>",
		Password:  "hashedpassword",
		FirstName: "Test",
		LastName:  "User",
		IsActive:  true,
	}
	err = db.Create(suite.testUser).Error
	suite.Require().NoError(err)
}

func (suite *AuditServiceTestSuite) TearDownTest() {
	// 清理测试数据
	suite.db.Exec("DELETE FROM audit_logs")
}

func (suite *AuditServiceTestSuite) TestLogAction() {
	// Test: 记录用户操作
	resourceID := uint(123)
	err := suite.auditService.LogAction(LogActionParams{
		UserID:     &suite.testUser.ID,
		Username:   suite.testUser.Username,
		Action:     "create_user",
		Resource:   "users",
		ResourceID: &resourceID,
		Method:     "POST",
		Path:       "/api/v1/users",
		IPAddress:  "***********",
		UserAgent:  "Mozilla/5.0",
		Status:     "success",
		Message:    "User created successfully",
		Duration:   150,
	})

	assert.NoError(suite.T(), err)

	// 验证日志已创建
	var log models.AuditLog
	err = suite.db.First(&log).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), suite.testUser.ID, *log.UserID)
	assert.Equal(suite.T(), "testuser", log.Username)
	assert.Equal(suite.T(), "create_user", log.Action)
	assert.Equal(suite.T(), "users", log.Resource)
	assert.Equal(suite.T(), resourceID, *log.ResourceID)
	assert.Equal(suite.T(), "success", log.Status)
	assert.Equal(suite.T(), int64(150), log.Duration)
}

func (suite *AuditServiceTestSuite) TestLogActionWithValues() {
	oldValues := map[string]interface{}{
		"name":      "old_name",
		"is_active": true,
	}
	newValues := map[string]interface{}{
		"name":      "new_name",
		"is_active": false,
	}

	// Test: 记录带有旧值和新值的操作
	err := suite.auditService.LogActionWithValues(LogActionParams{
		UserID:   &suite.testUser.ID,
		Username: suite.testUser.Username,
		Action:   "update_user",
		Resource: "users",
		Method:   "PUT",
		Path:     "/api/v1/users/123",
		Status:   "success",
		Message:  "User updated successfully",
	}, oldValues, newValues)

	assert.NoError(suite.T(), err)

	// 验证日志已创建并包含值
	var log models.AuditLog
	err = suite.db.First(&log).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "update_user", log.Action)

	// 验证JSON值
	var parsedOldValues, parsedNewValues map[string]interface{}
	err = json.Unmarshal([]byte(log.OldValues), &parsedOldValues)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "old_name", parsedOldValues["name"])

	err = json.Unmarshal([]byte(log.NewValues), &parsedNewValues)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "new_name", parsedNewValues["name"])
}

func (suite *AuditServiceTestSuite) TestLogSystemAction() {
	// Test: 记录系统操作（无用户）
	err := suite.auditService.LogSystemAction("system_startup", "system", "System started successfully")

	assert.NoError(suite.T(), err)

	// 验证日志已创建
	var log models.AuditLog
	err = suite.db.First(&log).Error
	assert.NoError(suite.T(), err)
	assert.Nil(suite.T(), log.UserID)
	assert.Equal(suite.T(), "system", log.Username)
	assert.Equal(suite.T(), "system_startup", log.Action)
	assert.Equal(suite.T(), "system", log.Resource)
	assert.Equal(suite.T(), "success", log.Status)
}

func (suite *AuditServiceTestSuite) TestLogLoginAttempt() {
	// Test: 记录成功登录
	err := suite.auditService.LogLoginAttempt(suite.testUser.Username, "***********", "Mozilla/5.0", true, "")

	assert.NoError(suite.T(), err)

	var log models.AuditLog
	err = suite.db.First(&log).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "login", log.Action)
	assert.Equal(suite.T(), "auth", log.Resource)
	assert.Equal(suite.T(), "success", log.Status)
	assert.Equal(suite.T(), "***********", log.IPAddress)

	// 清理
	suite.db.Exec("DELETE FROM audit_logs")

	// Test: 记录失败登录
	err = suite.auditService.LogLoginAttempt("wronguser", "***********", "Mozilla/5.0", false, "Invalid credentials")

	assert.NoError(suite.T(), err)

	var failedLog models.AuditLog
	err = suite.db.Where("status = ?", "failed").First(&failedLog).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "login", failedLog.Action)
	assert.Equal(suite.T(), "failed", failedLog.Status)
	assert.Equal(suite.T(), "Invalid credentials", failedLog.Message)
}

func (suite *AuditServiceTestSuite) TestGetAuditLogs() {
	// 创建多个测试日志
	logs := []models.AuditLog{
		{
			UserID:    &suite.testUser.ID,
			Username:  suite.testUser.Username,
			Action:    "create_user",
			Resource:  "users",
			Status:    "success",
			CreatedAt: time.Now().Add(-2 * time.Hour),
		},
		{
			UserID:    &suite.testUser.ID,
			Username:  suite.testUser.Username,
			Action:    "update_user",
			Resource:  "users",
			Status:    "success",
			CreatedAt: time.Now().Add(-1 * time.Hour),
		},
		{
			Username:  "system",
			Action:    "system_backup",
			Resource:  "system",
			Status:    "success",
			CreatedAt: time.Now(),
		},
	}

	for _, log := range logs {
		err := suite.db.Create(&log).Error
		suite.Require().NoError(err)
	}

	// Test: 获取所有日志（分页）
	result, total, err := suite.auditService.GetAuditLogs(AuditLogFilter{
		Page:  1,
		Limit: 10,
	})

	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), result, 3)
	assert.Equal(suite.T(), int64(3), total)
	// 验证按时间倒序排列
	assert.Equal(suite.T(), "system_backup", result[0].Action)
	assert.Equal(suite.T(), "update_user", result[1].Action)
	assert.Equal(suite.T(), "create_user", result[2].Action)

	// Test: 按用户过滤
	result, total, err = suite.auditService.GetAuditLogs(AuditLogFilter{
		Page:     1,
		Limit:    10,
		Username: suite.testUser.Username,
	})

	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), result, 2)
	assert.Equal(suite.T(), int64(2), total)

	// Test: 按操作过滤
	result, total, err = suite.auditService.GetAuditLogs(AuditLogFilter{
		Page:   1,
		Limit:  10,
		Action: "create_user",
	})

	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), result, 1)
	assert.Equal(suite.T(), int64(1), total)
	assert.Equal(suite.T(), "create_user", result[0].Action)

	// Test: 按资源过滤
	result, total, err = suite.auditService.GetAuditLogs(AuditLogFilter{
		Page:     1,
		Limit:    10,
		Resource: "system",
	})

	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), result, 1)
	assert.Equal(suite.T(), int64(1), total)
	assert.Equal(suite.T(), "system", result[0].Resource)
}

func (suite *AuditServiceTestSuite) TestGetUserAuditLogs() {
	// 创建测试日志
	log := models.AuditLog{
		UserID:   &suite.testUser.ID,
		Username: suite.testUser.Username,
		Action:   "login",
		Resource: "auth",
		Status:   "success",
	}
	err := suite.db.Create(&log).Error
	suite.Require().NoError(err)

	// Test: 获取用户的审计日志
	result, total, err := suite.auditService.GetUserAuditLogs(suite.testUser.ID, 1, 10)

	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), result, 1)
	assert.Equal(suite.T(), int64(1), total)
	assert.Equal(suite.T(), suite.testUser.ID, *result[0].UserID)
}

func (suite *AuditServiceTestSuite) TestGetAuditStats() {
	// 创建不同状态的测试日志
	logs := []models.AuditLog{
		{Username: "user1", Action: "login", Status: "success", CreatedAt: time.Now()},
		{Username: "user2", Action: "login", Status: "failed", CreatedAt: time.Now()},
		{Username: "user1", Action: "create_user", Status: "success", CreatedAt: time.Now()},
		{Username: "user3", Action: "login", Status: "success", CreatedAt: time.Now().Add(-25 * time.Hour)}, // 超过24小时
	}

	for _, log := range logs {
		err := suite.db.Create(&log).Error
		suite.Require().NoError(err)
	}

	// Test: 获取审计统计
	stats, err := suite.auditService.GetAuditStats()

	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(4), stats.TotalLogs)
	assert.Equal(suite.T(), int64(3), stats.LogsLast24h)
	assert.Equal(suite.T(), int64(3), stats.SuccessfulActions)
	assert.Equal(suite.T(), int64(1), stats.FailedActions)
	assert.Equal(suite.T(), int64(3), stats.UniqueUsers)
}

func TestAuditServiceTestSuite(t *testing.T) {
	suite.Run(t, new(AuditServiceTestSuite))
}
