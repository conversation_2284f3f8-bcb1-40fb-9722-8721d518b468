package services

import (
	"errors"
	"fmt"

	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/models"
)

// RoleServiceInterface defines the interface for role service
type RoleServiceInterface interface {
	CreateRole(name, description string) (*models.Role, error)
	GetRoleByID(id uint) (*models.Role, error)
	GetAllRoles(page, limit int) ([]models.Role, int64, error)
	UpdateRole(id uint, name, description string, isActive bool) (*models.Role, error)
	DeleteRole(id uint) error
	AssignPermissionToRole(roleID, permissionID uint) error
	RemovePermissionFromRole(roleID, permissionID uint) error
	GetRolePermissions(roleID uint) ([]models.Permission, error)
}

type RoleService struct {
	db *gorm.DB
}

// Compile-time interface compliance check
var _ RoleServiceInterface = (*RoleService)(nil)

// NewRoleService creates a new role service
func NewRoleService(db *gorm.DB) *RoleService {
	return &RoleService{db: db}
}

// CreateRole creates a new role
func (r *RoleService) CreateRole(name, description string) (*models.Role, error) {
	// Check if role name already exists
	var existingRole models.Role
	err := r.db.Where("name = ?", name).First(&existingRole).Error
	if err == nil {
		return nil, errors.New("role name already exists")
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check existing role: %w", err)
	}

	// Create new role
	role := &models.Role{
		Name:        name,
		Description: description,
		IsActive:    true,
	}

	if err := r.db.Create(role).Error; err != nil {
		return nil, fmt.Errorf("failed to create role: %w", err)
	}

	return role, nil
}

// GetRoleByID gets a role by ID with permissions
func (r *RoleService) GetRoleByID(id uint) (*models.Role, error) {
	var role models.Role
	err := r.db.Preload("Permissions").First(&role, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("role not found")
		}
		return nil, fmt.Errorf("failed to get role: %w", err)
	}

	return &role, nil
}

// GetAllRoles gets all roles with pagination
func (r *RoleService) GetAllRoles(page, limit int) ([]models.Role, int64, error) {
	var roles []models.Role
	var total int64

	// Count total roles
	if err := r.db.Model(&models.Role{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count roles: %w", err)
	}

	// Get roles with pagination
	offset := (page - 1) * limit
	err := r.db.Preload("Permissions").Offset(offset).Limit(limit).Find(&roles).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get roles: %w", err)
	}

	return roles, total, nil
}

// UpdateRole updates a role
func (r *RoleService) UpdateRole(id uint, name, description string, isActive bool) (*models.Role, error) {
	var role models.Role
	err := r.db.First(&role, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("role not found")
		}
		return nil, fmt.Errorf("failed to find role: %w", err)
	}

	// Check if new name conflicts with existing role (if name is being changed)
	if role.Name != name {
		var existingRole models.Role
		err := r.db.Where("name = ? AND id != ?", name, id).First(&existingRole).Error
		if err == nil {
			return nil, errors.New("role name already exists")
		}
		if err != gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("failed to check existing role: %w", err)
		}
	}

	// Update role
	role.Name = name
	role.Description = description
	role.IsActive = isActive

	if err := r.db.Save(&role).Error; err != nil {
		return nil, fmt.Errorf("failed to update role: %w", err)
	}

	return &role, nil
}

// DeleteRole soft deletes a role
func (r *RoleService) DeleteRole(id uint) error {
	var role models.Role
	err := r.db.First(&role, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("role not found")
		}
		return fmt.Errorf("failed to find role: %w", err)
	}

	// Check if role is assigned to any users
	var userRoleCount int64
	if err := r.db.Model(&models.UserRole{}).Where("role_id = ?", id).Count(&userRoleCount).Error; err != nil {
		return fmt.Errorf("failed to check role assignments: %w", err)
	}

	if userRoleCount > 0 {
		return errors.New("cannot delete role that is assigned to users")
	}

	// Soft delete the role
	if err := r.db.Delete(&role).Error; err != nil {
		return fmt.Errorf("failed to delete role: %w", err)
	}

	return nil
}

// AssignPermissionToRole assigns a permission to a role
func (r *RoleService) AssignPermissionToRole(roleID, permissionID uint) error {
	// Check if role exists
	var role models.Role
	if err := r.db.First(&role, roleID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("role not found")
		}
		return fmt.Errorf("failed to find role: %w", err)
	}

	// Check if permission exists
	var permission models.Permission
	if err := r.db.First(&permission, permissionID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("permission not found")
		}
		return fmt.Errorf("failed to find permission: %w", err)
	}

	// Check if assignment already exists
	var rolePermission models.RolePermission
	err := r.db.Where("role_id = ? AND permission_id = ?", roleID, permissionID).First(&rolePermission).Error
	if err == nil {
		return errors.New("permission already assigned to role")
	}
	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing assignment: %w", err)
	}

	// Create the assignment
	rolePermission = models.RolePermission{
		RoleID:       roleID,
		PermissionID: permissionID,
	}

	if err := r.db.Create(&rolePermission).Error; err != nil {
		return fmt.Errorf("failed to assign permission to role: %w", err)
	}

	return nil
}

// RemovePermissionFromRole removes a permission from a role
func (r *RoleService) RemovePermissionFromRole(roleID, permissionID uint) error {
	result := r.db.Where("role_id = ? AND permission_id = ?", roleID, permissionID).Delete(&models.RolePermission{})
	if result.Error != nil {
		return fmt.Errorf("failed to remove permission from role: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return errors.New("permission assignment not found")
	}

	return nil
}

// GetRolePermissions gets all permissions for a role
func (r *RoleService) GetRolePermissions(roleID uint) ([]models.Permission, error) {
	var permissions []models.Permission
	err := r.db.Table("permissions").
		Select("permissions.*").
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id = ?", roleID).
		Find(&permissions).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get role permissions: %w", err)
	}

	return permissions, nil
}
