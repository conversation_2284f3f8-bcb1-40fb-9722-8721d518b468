package services

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/models"
)

type NotificationServiceTestSuite struct {
	suite.Suite
	db                  *gorm.DB
	notificationService *NotificationService
	testUser            *models.User
	testSender          *models.User
}

func (suite *NotificationServiceTestSuite) SetupSuite() {
	// 使用内存SQLite数据库进行测试
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 运行迁移
	err = db.AutoMigrate(
		&models.User{},
		&models.Notification{},
	)
	suite.Require().NoError(err)

	suite.db = db
	suite.notificationService = NewNotificationService(db)

	// 创建测试用户
	suite.testUser = &models.User{
		Username:  "testuser",
		Email:     "<EMAIL>",
		Password:  "hashedpassword",
		FirstName: "Test",
		LastName:  "User",
		IsActive:  true,
	}
	err = db.Create(suite.testUser).Error
	suite.Require().NoError(err)

	suite.testSender = &models.User{
		Username:  "sender",
		Email:     "<EMAIL>",
		Password:  "hashedpassword",
		FirstName: "Sender",
		LastName:  "User",
		IsActive:  true,
	}
	err = db.Create(suite.testSender).Error
	suite.Require().NoError(err)
}

func (suite *NotificationServiceTestSuite) TearDownTest() {
	// 清理测试数据
	suite.db.Exec("DELETE FROM notifications")
}

func (suite *NotificationServiceTestSuite) TestCreateNotification() {
	// Test: 创建用户通知
	notification, err := suite.notificationService.CreateNotification(CreateNotificationInput{
		Title:      "Test Notification",
		Content:    "This is a test notification",
		Type:       "info",
		Category:   "user",
		Priority:   1,
		SenderID:   &suite.testSender.ID,
		ReceiverID: &suite.testUser.ID,
		ActionURL:  "/test",
	})

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), notification)
	assert.Equal(suite.T(), "Test Notification", notification.Title)
	assert.Equal(suite.T(), "This is a test notification", notification.Content)
	assert.Equal(suite.T(), "info", notification.Type)
	assert.Equal(suite.T(), "user", notification.Category)
	assert.Equal(suite.T(), 1, notification.Priority)
	assert.Equal(suite.T(), suite.testSender.ID, *notification.SenderID)
	assert.Equal(suite.T(), suite.testUser.ID, *notification.ReceiverID)
	assert.Equal(suite.T(), "/test", notification.ActionURL)
	assert.False(suite.T(), notification.IsRead)
	assert.False(suite.T(), notification.IsGlobal)

	// Test: 创建全局通知
	globalNotification, err := suite.notificationService.CreateNotification(CreateNotificationInput{
		Title:    "Global Notification",
		Content:  "This is a global notification",
		Type:     "warning",
		Category: "system",
		Priority: 2,
		IsGlobal: true,
	})

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), globalNotification)
	assert.True(suite.T(), globalNotification.IsGlobal)
	assert.Nil(suite.T(), globalNotification.ReceiverID)
}

func (suite *NotificationServiceTestSuite) TestGetNotificationByID() {
	// 先创建一个通知
	createdNotification, err := suite.notificationService.CreateNotification(CreateNotificationInput{
		Title:      "Test Notification",
		Content:    "Test content",
		Type:       "info",
		Category:   "user",
		ReceiverID: &suite.testUser.ID,
	})
	suite.Require().NoError(err)

	// Test: 获取存在的通知
	notification, err := suite.notificationService.GetNotificationByID(createdNotification.ID)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), notification)
	assert.Equal(suite.T(), createdNotification.ID, notification.ID)
	assert.Equal(suite.T(), "Test Notification", notification.Title)

	// Test: 获取不存在的通知
	_, err = suite.notificationService.GetNotificationByID(999)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "notification not found")
}

func (suite *NotificationServiceTestSuite) TestGetUserNotifications() {
	// 创建多个通知
	for i := 0; i < 3; i++ {
		_, err := suite.notificationService.CreateNotification(CreateNotificationInput{
			Title:      fmt.Sprintf("Notification %d", i),
			Content:    fmt.Sprintf("Content %d", i),
			Type:       "info",
			Category:   "user",
			ReceiverID: &suite.testUser.ID,
		})
		suite.Require().NoError(err)
	}

	// 创建一个全局通知
	_, err := suite.notificationService.CreateNotification(CreateNotificationInput{
		Title:    "Global Notification",
		Content:  "Global content",
		Type:     "info",
		Category: "system",
		IsGlobal: true,
	})
	suite.Require().NoError(err)

	// Test: 获取用户通知（包括全局通知）
	notifications, total, err := suite.notificationService.GetUserNotifications(suite.testUser.ID, 1, 10)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), notifications, 4) // 3个用户通知 + 1个全局通知
	assert.Equal(suite.T(), int64(4), total)

	// Test: 分页测试
	notifications, total, err = suite.notificationService.GetUserNotifications(suite.testUser.ID, 1, 2)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), notifications, 2)
	assert.Equal(suite.T(), int64(4), total)
}

func (suite *NotificationServiceTestSuite) TestGetUnreadNotifications() {
	// 创建已读和未读通知
	readNotification, err := suite.notificationService.CreateNotification(CreateNotificationInput{
		Title:      "Read Notification",
		Content:    "This is read",
		Type:       "info",
		Category:   "user",
		ReceiverID: &suite.testUser.ID,
	})
	suite.Require().NoError(err)

	// 标记为已读
	err = suite.notificationService.MarkAsRead(readNotification.ID, suite.testUser.ID)
	suite.Require().NoError(err)

	// 创建未读通知
	_, err = suite.notificationService.CreateNotification(CreateNotificationInput{
		Title:      "Unread Notification",
		Content:    "This is unread",
		Type:       "info",
		Category:   "user",
		ReceiverID: &suite.testUser.ID,
	})
	suite.Require().NoError(err)

	// Test: 获取未读通知
	notifications, total, err := suite.notificationService.GetUnreadNotifications(suite.testUser.ID, 1, 10)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), notifications, 1)
	assert.Equal(suite.T(), int64(1), total)
	assert.Equal(suite.T(), "Unread Notification", notifications[0].Title)
	assert.False(suite.T(), notifications[0].IsRead)
}

func (suite *NotificationServiceTestSuite) TestMarkAsRead() {
	// 创建一个通知
	notification, err := suite.notificationService.CreateNotification(CreateNotificationInput{
		Title:      "Test Notification",
		Content:    "Test content",
		Type:       "info",
		Category:   "user",
		ReceiverID: &suite.testUser.ID,
	})
	suite.Require().NoError(err)
	assert.False(suite.T(), notification.IsRead)

	// Test: 标记为已读
	err = suite.notificationService.MarkAsRead(notification.ID, suite.testUser.ID)
	assert.NoError(suite.T(), err)

	// 验证已标记为已读
	updatedNotification, err := suite.notificationService.GetNotificationByID(notification.ID)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), updatedNotification.IsRead)

	// Test: 其他用户不能标记别人的通知为已读
	err = suite.notificationService.MarkAsRead(notification.ID, suite.testSender.ID)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "permission denied")
}

func (suite *NotificationServiceTestSuite) TestMarkAllAsRead() {
	// 创建多个未读通知
	for i := 0; i < 3; i++ {
		_, err := suite.notificationService.CreateNotification(CreateNotificationInput{
			Title:      fmt.Sprintf("Notification %d", i),
			Content:    fmt.Sprintf("Content %d", i),
			Type:       "info",
			Category:   "user",
			ReceiverID: &suite.testUser.ID,
		})
		suite.Require().NoError(err)
	}

	// Test: 标记所有通知为已读
	count, err := suite.notificationService.MarkAllAsRead(suite.testUser.ID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(3), count)

	// 验证所有通知都已标记为已读
	notifications, _, err := suite.notificationService.GetUnreadNotifications(suite.testUser.ID, 1, 10)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), notifications, 0)
}

func (suite *NotificationServiceTestSuite) TestDeleteNotification() {
	// 创建一个通知
	notification, err := suite.notificationService.CreateNotification(CreateNotificationInput{
		Title:      "Test Notification",
		Content:    "Test content",
		Type:       "info",
		Category:   "user",
		ReceiverID: &suite.testUser.ID,
	})
	suite.Require().NoError(err)

	// Test: 删除通知成功
	err = suite.notificationService.DeleteNotification(notification.ID, suite.testUser.ID)
	assert.NoError(suite.T(), err)

	// 验证通知已被软删除
	_, err = suite.notificationService.GetNotificationByID(notification.ID)
	assert.Error(suite.T(), err)

	// Test: 其他用户不能删除别人的通知
	notification2, err := suite.notificationService.CreateNotification(CreateNotificationInput{
		Title:      "Test Notification 2",
		Content:    "Test content 2",
		Type:       "info",
		Category:   "user",
		ReceiverID: &suite.testUser.ID,
	})
	suite.Require().NoError(err)

	err = suite.notificationService.DeleteNotification(notification2.ID, suite.testSender.ID)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "permission denied")
}

func (suite *NotificationServiceTestSuite) TestGetNotificationStats() {
	// 创建不同类型和状态的通知
	readNotification, err := suite.notificationService.CreateNotification(CreateNotificationInput{
		Title:      "Read Notification",
		Content:    "Content",
		Type:       "info",
		Category:   "user",
		ReceiverID: &suite.testUser.ID,
	})
	suite.Require().NoError(err)

	_, err = suite.notificationService.CreateNotification(CreateNotificationInput{
		Title:      "Unread Notification",
		Content:    "Content",
		Type:       "warning",
		Category:   "system",
		ReceiverID: &suite.testUser.ID,
	})
	suite.Require().NoError(err)

	// 标记第一个为已读
	err = suite.notificationService.MarkAsRead(readNotification.ID, suite.testUser.ID)
	suite.Require().NoError(err)

	// 创建全局通知（默认未读）
	_, err = suite.notificationService.CreateNotification(CreateNotificationInput{
		Title:    "Global Notification",
		Content:  "Global content",
		Type:     "error",
		Category: "system",
		IsGlobal: true,
	})
	suite.Require().NoError(err)

	// Test: 获取用户通知统计
	stats, err := suite.notificationService.GetUserNotificationStats(suite.testUser.ID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(3), stats.TotalNotifications)
	assert.Equal(suite.T(), int64(2), stats.UnreadCount) // "Unread Notification" + "Global Notification"
	assert.Equal(suite.T(), int64(1), stats.ReadCount)   // "Read Notification"
	assert.Len(suite.T(), stats.ByType, 3)               // info, warning, error
	assert.Len(suite.T(), stats.ByCategory, 2)           // user, system
}

func (suite *NotificationServiceTestSuite) TestCleanupExpiredNotifications() {
	// 创建过期通知
	expiredTime := time.Now().Add(-24 * time.Hour)
	expiredNotification := models.Notification{
		Title:      "Expired Notification",
		Content:    "This is expired",
		Type:       "info",
		Category:   "user",
		ReceiverID: &suite.testUser.ID,
		ExpiresAt:  &expiredTime,
	}
	err := suite.db.Create(&expiredNotification).Error
	suite.Require().NoError(err)

	// 创建未过期通知
	futureTime := time.Now().Add(24 * time.Hour)
	_, err = suite.notificationService.CreateNotification(CreateNotificationInput{
		Title:      "Valid Notification",
		Content:    "This is valid",
		Type:       "info",
		Category:   "user",
		ReceiverID: &suite.testUser.ID,
		ExpiresAt:  &futureTime,
	})
	suite.Require().NoError(err)

	// Test: 清理过期通知
	count, err := suite.notificationService.CleanupExpiredNotifications()
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(1), count)

	// 验证过期通知已被删除
	_, err = suite.notificationService.GetNotificationByID(expiredNotification.ID)
	assert.Error(suite.T(), err)
}

func (suite *NotificationServiceTestSuite) TestBroadcastNotification() {
	// Test: 创建广播通知
	notification, err := suite.notificationService.BroadcastNotification(
		"System Maintenance",
		"The system will be under maintenance from 2:00 AM to 4:00 AM",
		"warning",
		"system",
		2,
		"/maintenance",
		nil,
	)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), notification)
	assert.Equal(suite.T(), "System Maintenance", notification.Title)
	assert.Equal(suite.T(), "The system will be under maintenance from 2:00 AM to 4:00 AM", notification.Content)
	assert.Equal(suite.T(), "warning", notification.Type)
	assert.Equal(suite.T(), "system", notification.Category)
	assert.Equal(suite.T(), 2, notification.Priority)
	assert.True(suite.T(), notification.IsGlobal)
	assert.Equal(suite.T(), "/maintenance", notification.ActionURL)
	assert.Nil(suite.T(), notification.SenderID)
	assert.Nil(suite.T(), notification.ReceiverID)

	// 验证通知已保存到数据库
	var savedNotification models.Notification
	err = suite.db.First(&savedNotification, notification.ID).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "System Maintenance", savedNotification.Title)
	assert.True(suite.T(), savedNotification.IsGlobal)
}

func TestNotificationServiceTestSuite(t *testing.T) {
	suite.Run(t, new(NotificationServiceTestSuite))
}
