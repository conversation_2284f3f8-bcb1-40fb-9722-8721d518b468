package services

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/models"
)

type RoleServiceTestSuite struct {
	suite.Suite
	db          *gorm.DB
	roleService *RoleService
}

func (suite *RoleServiceTestSuite) SetupSuite() {
	// 使用内存SQLite数据库进行测试
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 运行迁移
	err = db.AutoMigrate(
		&models.User{},
		&models.Role{},
		&models.Permission{},
		&models.UserRole{},
		&models.RolePermission{},
	)
	suite.Require().NoError(err)

	suite.db = db
	suite.roleService = NewRoleService(db)
}

func (suite *RoleServiceTestSuite) TearDownTest() {
	// 清理测试数据
	suite.db.Exec("DELETE FROM role_permissions")
	suite.db.Exec("DELETE FROM user_roles")
	suite.db.Exec("DELETE FROM roles")
	suite.db.Exec("DELETE FROM permissions")
	suite.db.Exec("DELETE FROM users")
}

func (suite *RoleServiceTestSuite) TestCreateRole() {
	// Test: 创建角色成功
	role, err := suite.roleService.CreateRole("test_role", "Test Role Description")
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), role)
	assert.Equal(suite.T(), "test_role", role.Name)
	assert.Equal(suite.T(), "Test Role Description", role.Description)
	assert.True(suite.T(), role.IsActive)
	assert.NotZero(suite.T(), role.ID)

	// Test: 创建重复角色名应该失败
	_, err = suite.roleService.CreateRole("test_role", "Another Description")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "role name already exists")
}

func (suite *RoleServiceTestSuite) TestGetRoleByID() {
	// 先创建一个角色
	createdRole, err := suite.roleService.CreateRole("test_role", "Test Description")
	suite.Require().NoError(err)

	// Test: 获取存在的角色
	role, err := suite.roleService.GetRoleByID(createdRole.ID)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), role)
	assert.Equal(suite.T(), createdRole.ID, role.ID)
	assert.Equal(suite.T(), "test_role", role.Name)

	// Test: 获取不存在的角色
	_, err = suite.roleService.GetRoleByID(999)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "role not found")
}

func (suite *RoleServiceTestSuite) TestGetAllRoles() {
	// 创建多个角色
	_, err := suite.roleService.CreateRole("role1", "Role 1")
	suite.Require().NoError(err)
	_, err = suite.roleService.CreateRole("role2", "Role 2")
	suite.Require().NoError(err)

	// Test: 获取所有角色（分页）
	roles, total, err := suite.roleService.GetAllRoles(1, 10)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), roles, 2)
	assert.Equal(suite.T(), int64(2), total)

	// Test: 分页测试
	roles, total, err = suite.roleService.GetAllRoles(1, 1)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), roles, 1)
	assert.Equal(suite.T(), int64(2), total)
}

func (suite *RoleServiceTestSuite) TestUpdateRole() {
	// 先创建一个角色
	createdRole, err := suite.roleService.CreateRole("test_role", "Original Description")
	suite.Require().NoError(err)

	// Test: 更新角色成功
	updatedRole, err := suite.roleService.UpdateRole(createdRole.ID, "updated_role", "Updated Description", false)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), updatedRole)
	assert.Equal(suite.T(), "updated_role", updatedRole.Name)
	assert.Equal(suite.T(), "Updated Description", updatedRole.Description)
	assert.False(suite.T(), updatedRole.IsActive)

	// Test: 更新不存在的角色
	_, err = suite.roleService.UpdateRole(999, "nonexistent", "Description", true)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "role not found")
}

func (suite *RoleServiceTestSuite) TestDeleteRole() {
	// 先创建一个角色
	createdRole, err := suite.roleService.CreateRole("test_role", "Test Description")
	suite.Require().NoError(err)

	// Test: 删除角色成功
	err = suite.roleService.DeleteRole(createdRole.ID)
	assert.NoError(suite.T(), err)

	// 验证角色已被软删除
	_, err = suite.roleService.GetRoleByID(createdRole.ID)
	assert.Error(suite.T(), err)

	// Test: 删除不存在的角色
	err = suite.roleService.DeleteRole(999)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "role not found")
}

func (suite *RoleServiceTestSuite) TestAssignPermissionToRole() {
	// 创建角色和权限
	role, err := suite.roleService.CreateRole("test_role", "Test Role")
	suite.Require().NoError(err)

	permission := &models.Permission{
		Name:        "test.permission",
		Resource:    "test",
		Action:      "permission",
		Description: "Test Permission",
	}
	err = suite.db.Create(permission).Error
	suite.Require().NoError(err)

	// Test: 分配权限给角色
	err = suite.roleService.AssignPermissionToRole(role.ID, permission.ID)
	assert.NoError(suite.T(), err)

	// 验证权限已分配
	roleWithPermissions, err := suite.roleService.GetRoleByID(role.ID)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), roleWithPermissions.Permissions, 1)
	assert.Equal(suite.T(), permission.ID, roleWithPermissions.Permissions[0].ID)

	// Test: 重复分配权限应该失败
	err = suite.roleService.AssignPermissionToRole(role.ID, permission.ID)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "permission already assigned")
}

func (suite *RoleServiceTestSuite) TestRemovePermissionFromRole() {
	// 创建角色和权限，并分配权限
	role, err := suite.roleService.CreateRole("test_role", "Test Role")
	suite.Require().NoError(err)

	permission := &models.Permission{
		Name:        "test.permission",
		Resource:    "test",
		Action:      "permission",
		Description: "Test Permission",
	}
	err = suite.db.Create(permission).Error
	suite.Require().NoError(err)

	err = suite.roleService.AssignPermissionToRole(role.ID, permission.ID)
	suite.Require().NoError(err)

	// Test: 移除权限
	err = suite.roleService.RemovePermissionFromRole(role.ID, permission.ID)
	assert.NoError(suite.T(), err)

	// 验证权限已移除
	roleWithPermissions, err := suite.roleService.GetRoleByID(role.ID)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), roleWithPermissions.Permissions, 0)

	// Test: 移除不存在的权限分配
	err = suite.roleService.RemovePermissionFromRole(role.ID, permission.ID)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "permission assignment not found")
}

func TestRoleServiceTestSuite(t *testing.T) {
	suite.Run(t, new(RoleServiceTestSuite))
}
