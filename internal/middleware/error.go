package middleware

import (
	"net/http"

	"github.com/labstack/echo/v4"
)

// ErrorResponse represents a standardized error response
type ErrorResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Details interface{} `json:"details,omitempty"`
	Path    string      `json:"path"`
	Method  string      `json:"method"`
}

// ValidationErrorDetail represents validation error details
type ValidationErrorDetail struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Value   string `json:"value,omitempty"`
}

// ErrorHandler creates a custom error handler middleware
func ErrorHandler() echo.HTTPErrorHandler {
	return func(err error, c echo.Context) {
		var (
			code    = http.StatusInternalServerError
			message = "Internal Server Error"
			details interface{}
		)

		// Handle Echo HTTP errors
		if he, ok := err.(*echo.HTTPError); ok {
			code = he.Code
			if msg, ok := he.Message.(string); ok {
				message = msg
			} else {
				message = http.StatusText(code)
			}
			details = he.Internal
		}

		// Handle validation errors
		if code == http.StatusBadRequest && isValidationError(err) {
			details = parseValidationError(err)
		}

		// Create standardized error response
		errorResponse := ErrorResponse{
			Code:    code,
			Message: message,
			Details: details,
			Path:    c.Request().URL.Path,
			Method:  c.Request().Method,
		}

		// Don't send response if already sent
		if !c.Response().Committed {
			if c.Request().Header.Get(echo.HeaderAccept) == echo.MIMEApplicationJSON {
				c.JSON(code, errorResponse)
			} else {
				c.JSON(code, errorResponse)
			}
		}
	}
}

// isValidationError checks if the error is a validation error
func isValidationError(err error) bool {
	// This is a simple check - you might want to implement more sophisticated validation error detection
	return err != nil && (err.Error() == "validator not registered" ||
		err.Error() == "invalid request body" ||
		containsValidationKeywords(err.Error()))
}

// containsValidationKeywords checks if error message contains validation keywords
func containsValidationKeywords(message string) bool {
	keywords := []string{
		"required",
		"min",
		"max",
		"email",
		"validate",
		"validation",
	}

	for _, keyword := range keywords {
		if len(message) > len(keyword) {
			for i := 0; i <= len(message)-len(keyword); i++ {
				if message[i:i+len(keyword)] == keyword {
					return true
				}
			}
		}
	}
	return false
}

// parseValidationError parses validation errors into structured format
func parseValidationError(err error) []ValidationErrorDetail {
	// This is a simplified implementation
	// In a real application, you'd parse the actual validation errors
	return []ValidationErrorDetail{
		{
			Field:   "unknown",
			Message: err.Error(),
		},
	}
}

// RequestLogger creates a request logging middleware
func RequestLogger() echo.MiddlewareFunc {
	return echo.MiddlewareFunc(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Log request details
			// req := c.Request()

			// You can add more sophisticated logging here
			// For now, we'll use Echo's built-in logger

			return next(c)
		}
	})
}

// RateLimiter creates a simple rate limiting middleware
func RateLimiter() echo.MiddlewareFunc {
	// This is a placeholder for rate limiting
	// In production, you'd implement proper rate limiting with Redis or similar
	return echo.MiddlewareFunc(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Simple rate limiting logic would go here
			// For now, just pass through
			return next(c)
		}
	})
}

// SecurityHeaders adds security headers to responses
func SecurityHeaders() echo.MiddlewareFunc {
	return echo.MiddlewareFunc(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Add security headers
			c.Response().Header().Set("X-Content-Type-Options", "nosniff")
			c.Response().Header().Set("X-Frame-Options", "DENY")
			c.Response().Header().Set("X-XSS-Protection", "1; mode=block")
			c.Response().Header().Set("Referrer-Policy", "strict-origin-when-cross-origin")
			c.Response().Header().Set("Content-Security-Policy", "default-src 'self'")

			return next(c)
		}
	})
}

// AuditLogger creates an audit logging middleware
func AuditLogger(auditService interface{}) echo.MiddlewareFunc {
	return echo.MiddlewareFunc(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Record start time
			// start := time.Now()

			// Execute the handler
			err := next(c)

			// Log the action after completion
			// This is where you'd integrate with your audit service
			// For now, it's a placeholder

			return err
		}
	})
}

// ResponseWrapper wraps responses in a consistent format
func ResponseWrapper() echo.MiddlewareFunc {
	return echo.MiddlewareFunc(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Execute the handler
			err := next(c)

			// You could modify the response here if needed
			// For example, wrap all successful responses in a standard format

			return err
		}
	})
}

// TransactionMiddleware handles database transactions
func TransactionMiddleware() echo.MiddlewareFunc {
	return echo.MiddlewareFunc(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// This is where you'd start a database transaction
			// and commit/rollback based on the result

			err := next(c)

			// Handle transaction commit/rollback

			return err
		}
	})
}
