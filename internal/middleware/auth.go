package middleware

import (
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
	"xiaoxingcloud.com/admin/internal/auth"
	"xiaoxingcloud.com/admin/internal/services"
)

type AuthMiddleware struct {
	jwtService  *auth.JWTService
	rbacService *services.RBACService
	abacService *services.ABACService
}

func NewAuthMiddleware(jwtService *auth.JWTService, rbacService *services.RBACService, abacService *services.ABACService) *AuthMiddleware {
	return &AuthMiddleware{
		jwtService:  jwtService,
		rbacService: rbacService,
		abacService: abacService,
	}
}

// JWTA<PERSON> validates JWT tokens
func (a *AuthMiddleware) JWTAuth() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			authHeader := c.Request().Header.Get("Authorization")
			if authHeader == "" {
				return echo.NewHTTPError(http.StatusUnauthorized, "missing authorization header")
			}

			// Extract token from "Bearer <token>"
			parts := strings.Split(authHeader, " ")
			if len(parts) != 2 || parts[0] != "Bearer" {
				return echo.NewHTTPError(http.StatusUnauthorized, "invalid authorization header format")
			}

			token := parts[1]
			claims, err := a.jwtService.ValidateToken(token)
			if err != nil {
				return echo.NewHTTPError(http.StatusUnauthorized, "invalid token")
			}

			// Store user info in context
			c.Set("user_id", claims.UserID)
			c.Set("username", claims.Username)
			c.Set("email", claims.Email)

			return next(c)
		}
	}
}

// RequirePermission checks if user has required permission (RBAC)
func (a *AuthMiddleware) RequirePermission(resource, action string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			userID, ok := c.Get("user_id").(uint)
			if !ok {
				return echo.NewHTTPError(http.StatusUnauthorized, "user not authenticated")
			}

			hasPermission, err := a.rbacService.CheckPermission(userID, resource, action)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, "failed to check permission")
			}

			if !hasPermission {
				return echo.NewHTTPError(http.StatusForbidden, "insufficient permissions")
			}

			return next(c)
		}
	}
}

// RequireAccess checks access using ABAC policies
func (a *AuthMiddleware) RequireAccess(resource, action string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			userID, ok := c.Get("user_id").(uint)
			if !ok {
				return echo.NewHTTPError(http.StatusUnauthorized, "user not authenticated")
			}

			// Build context from request
			context := map[string]any{
				"ip_address": c.RealIP(),
				"user_agent": c.Request().UserAgent(),
				"method":     c.Request().Method,
				"path":       c.Request().URL.Path,
			}

			hasAccess, err := a.abacService.CheckAccess(userID, resource, action, context)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, "failed to check access")
			}

			if !hasAccess {
				return echo.NewHTTPError(http.StatusForbidden, "access denied")
			}

			return next(c)
		}
	}
}

// RequireRole checks if user has a specific role
func (a *AuthMiddleware) RequireRole(roleName string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			userID, ok := c.Get("user_id").(uint)
			if !ok {
				return echo.NewHTTPError(http.StatusUnauthorized, "user not authenticated")
			}

			roles, err := a.rbacService.GetUserRoles(userID)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, "failed to get user roles")
			}

			hasRole := false
			for _, role := range roles {
				if role.Name == roleName && role.IsActive {
					hasRole = true
					break
				}
			}

			if !hasRole {
				return echo.NewHTTPError(http.StatusForbidden, "insufficient role")
			}

			return next(c)
		}
	}
}
