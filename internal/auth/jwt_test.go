package auth

import (
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"xiaoxingcloud.com/admin/internal/config"
)

func createTestJWTService() *JWTService {
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:      "test-secret-key-for-jwt-testing",
			ExpireHours: 24,
		},
	}
	return NewJWTService(cfg)
}

func TestNewJWTService(t *testing.T) {
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:      "test-secret",
			ExpireHours: 12,
		},
	}

	service := NewJWTService(cfg)
	assert.NotNil(t, service)
	assert.Equal(t, cfg, service.config)
}

func TestGenerateToken(t *testing.T) {
	service := createTestJWTService()

	tests := []struct {
		name     string
		userID   uint
		username string
		email    string
	}{
		{
			name:     "Valid user data",
			userID:   1,
			username: "testuser",
			email:    "<EMAIL>",
		},
		{
			name:     "User with special characters in username",
			userID:   2,
			username: "test.user-123",
			email:    "<EMAIL>",
		},
		{
			name:     "User with long email",
			userID:   3,
			username: "longuser",
			email:    "<EMAIL>",
		},
		{
			name:     "User ID zero",
			userID:   0,
			username: "zerouser",
			email:    "<EMAIL>",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token, err := service.GenerateToken(tt.userID, tt.username, tt.email)

			assert.NoError(t, err)
			assert.NotEmpty(t, token)

			// Verify token structure (should have 3 parts separated by dots)
			parts := len([]byte(token))
			assert.Greater(t, parts, 50) // JWT tokens are typically much longer

			// Verify we can parse the token back
			claims, err := service.ValidateToken(token)
			assert.NoError(t, err)
			assert.Equal(t, tt.userID, claims.UserID)
			assert.Equal(t, tt.username, claims.Username)
			assert.Equal(t, tt.email, claims.Email)
		})
	}
}

func TestValidateToken(t *testing.T) {
	service := createTestJWTService()
	userID := uint(123)
	username := "testuser"
	email := "<EMAIL>"

	// Generate a valid token
	validToken, err := service.GenerateToken(userID, username, email)
	require.NoError(t, err)

	tests := []struct {
		name        string
		token       string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid token",
			token:       validToken,
			expectError: false,
		},
		{
			name:        "Empty token",
			token:       "",
			expectError: true,
			errorMsg:    "token contains an invalid number of segments",
		},
		{
			name:        "Invalid token format",
			token:       "invalid.token.format",
			expectError: true,
		},
		{
			name:        "Token with wrong signature",
			token:       "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************.wrong_signature",
			expectError: true,
		},
		{
			name:        "Malformed token",
			token:       "not.a.jwt.token.at.all",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			claims, err := service.ValidateToken(tt.token)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, claims)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, claims)
				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, username, claims.Username)
				assert.Equal(t, email, claims.Email)
				assert.Equal(t, "admin-system", claims.Issuer)
				assert.Equal(t, username, claims.Subject)
			}
		})
	}
}

func TestTokenExpiration(t *testing.T) {
	// Create service with very short expiration for testing
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:      "test-secret-key",
			ExpireHours: 0, // This should create immediately expired tokens
		},
	}
	service := NewJWTService(cfg)

	token, err := service.GenerateToken(1, "testuser", "<EMAIL>")
	assert.NoError(t, err)

	// The token should be expired immediately
	time.Sleep(time.Millisecond * 10) // Small delay to ensure expiration
	
	claims, err := service.ValidateToken(token)
	// Note: This test might be flaky depending on timing, but with 0 hours it should fail
	if err != nil {
		assert.Contains(t, err.Error(), "token is expired")
		assert.Nil(t, claims)
	}
}

func TestTokenClaims(t *testing.T) {
	service := createTestJWTService()
	userID := uint(456)
	username := "claimsuser"
	email := "<EMAIL>"

	token, err := service.GenerateToken(userID, username, email)
	require.NoError(t, err)

	claims, err := service.ValidateToken(token)
	require.NoError(t, err)

	// Test all claim fields
	assert.Equal(t, userID, claims.UserID)
	assert.Equal(t, username, claims.Username)
	assert.Equal(t, email, claims.Email)
	assert.Equal(t, "admin-system", claims.Issuer)
	assert.Equal(t, username, claims.Subject)
	
	// Test time-based claims
	now := time.Now()
	assert.True(t, claims.ExpiresAt.Time.After(now))
	assert.True(t, claims.IssuedAt.Time.Before(now.Add(time.Second))) // Allow 1 second tolerance
	assert.True(t, claims.NotBefore.Time.Before(now.Add(time.Second))) // Allow 1 second tolerance
}

func TestValidateTokenWithDifferentSecret(t *testing.T) {
	// Create token with one secret
	service1 := createTestJWTService()
	token, err := service1.GenerateToken(1, "testuser", "<EMAIL>")
	require.NoError(t, err)

	// Try to validate with different secret
	cfg2 := &config.Config{
		JWT: config.JWTConfig{
			Secret:      "different-secret-key",
			ExpireHours: 24,
		},
	}
	service2 := NewJWTService(cfg2)

	claims, err := service2.ValidateToken(token)
	assert.Error(t, err)
	assert.Nil(t, claims)
	assert.Contains(t, err.Error(), "signature is invalid")
}

func TestJWTClaimsStruct(t *testing.T) {
	claims := &JWTClaims{
		UserID:   123,
		Username: "testuser",
		Email:    "<EMAIL>",
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:  "test-issuer",
			Subject: "test-subject",
		},
	}

	assert.Equal(t, uint(123), claims.UserID)
	assert.Equal(t, "testuser", claims.Username)
	assert.Equal(t, "<EMAIL>", claims.Email)
	assert.Equal(t, "test-issuer", claims.Issuer)
	assert.Equal(t, "test-subject", claims.Subject)
}

// Benchmark tests
func BenchmarkGenerateToken(b *testing.B) {
	service := createTestJWTService()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = service.GenerateToken(uint(i), "benchuser", "<EMAIL>")
	}
}

func BenchmarkValidateToken(b *testing.B) {
	service := createTestJWTService()
	token, _ := service.GenerateToken(1, "benchuser", "<EMAIL>")
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = service.ValidateToken(token)
	}
}
