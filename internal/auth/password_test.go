package auth

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHashPassword(t *testing.T) {
	tests := []struct {
		name        string
		password    string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid strong password",
			password:    "StrongPass123!",
			expectError: false,
		},
		{
			name:        "Password too short",
			password:    "Short1!",
			expectError: true,
			errorMsg:    "password must be at least 8 characters long",
		},
		{
			name:        "Password too long",
			password:    "VeryLongPasswordThatExceedsTheMaximumLengthLimitOfOneHundredTwentyEightCharactersAndShouldFailValidationBecauseItIsTooLongAndExceedsTheLimit123!",
			expectError: true,
			errorMsg:    "password must be less than 128 characters long",
		},
		{
			name:        "Password without uppercase",
			password:    "lowercase123!",
			expectError: true,
			errorMsg:    "password must contain at least one uppercase letter",
		},
		{
			name:        "Password without lowercase",
			password:    "UPPERCASE123!",
			expectError: true,
			errorMsg:    "password must contain at least one lowercase letter",
		},
		{
			name:        "Password without digit",
			password:    "NoDigitPass!",
			expectError: true,
			errorMsg:    "password must contain at least one digit",
		},
		{
			name:        "Password without special character",
			password:    "NoSpecialChar123",
			expectError: true,
			errorMsg:    "password must contain at least one special character",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hash, err := HashPassword(tt.password)

			if tt.expectError {
				assert.Error(t, err)
				assert.Equal(t, tt.errorMsg, err.Error())
				assert.Empty(t, hash)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, hash)
				assert.NotEqual(t, tt.password, hash) // Hash should be different from original
			}
		})
	}
}

func TestCheckPassword(t *testing.T) {
	password := "TestPassword123!"

	// Generate hash
	hash, err := HashPassword(password)
	assert.NoError(t, err)
	assert.NotEmpty(t, hash)

	tests := []struct {
		name     string
		password string
		hash     string
		expected bool
	}{
		{
			name:     "Correct password",
			password: password,
			hash:     hash,
			expected: true,
		},
		{
			name:     "Incorrect password",
			password: "WrongPassword123!",
			hash:     hash,
			expected: false,
		},
		{
			name:     "Empty password",
			password: "",
			hash:     hash,
			expected: false,
		},
		{
			name:     "Empty hash",
			password: password,
			hash:     "",
			expected: false,
		},
		{
			name:     "Invalid hash format",
			password: password,
			hash:     "invalid-hash",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CheckPassword(tt.password, tt.hash)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestValidatePassword(t *testing.T) {
	tests := []struct {
		name        string
		password    string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid password with all requirements",
			password:    "ValidPass123!",
			expectError: false,
		},
		{
			name:        "Valid password with different special chars",
			password:    "TestPass123@",
			expectError: false,
		},
		{
			name:        "Valid password with multiple special chars",
			password:    "Complex!Pass@123#",
			expectError: false,
		},
		{
			name:        "Password too short",
			password:    "Short1!",
			expectError: true,
			errorMsg:    "password must be at least 8 characters long",
		},
		{
			name:        "Empty password",
			password:    "",
			expectError: true,
			errorMsg:    "password must be at least 8 characters long",
		},
		{
			name:        "Password exactly minimum length",
			password:    "MinLen1!",
			expectError: false,
		},
		{
			name:        "Password at maximum length",
			password:    "ValidPasswordThatIsExactlyOneHundredTwentySevenCharactersLongWithUppercaseLowercaseDigitsAndSpecialChars123!",
			expectError: false,
		},
		{
			name:        "Password exceeds maximum length",
			password:    "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaA1!", // 130 characters
			expectError: true,
			errorMsg:    "password must be less than 128 characters long",
		},
		{
			name:        "No uppercase letter",
			password:    "nouppercase123!",
			expectError: true,
			errorMsg:    "password must contain at least one uppercase letter",
		},
		{
			name:        "No lowercase letter",
			password:    "NOLOWERCASE123!",
			expectError: true,
			errorMsg:    "password must contain at least one lowercase letter",
		},
		{
			name:        "No digit",
			password:    "NoDigitPassword!",
			expectError: true,
			errorMsg:    "password must contain at least one digit",
		},
		{
			name:        "No special character",
			password:    "NoSpecialChar123",
			expectError: true,
			errorMsg:    "password must contain at least one special character",
		},
		{
			name:        "Only letters",
			password:    "OnlyLetters",
			expectError: true,
			errorMsg:    "password must contain at least one digit",
		},
		{
			name:        "Only numbers",
			password:    "12345678",
			expectError: true,
			errorMsg:    "password must contain at least one uppercase letter",
		},
		{
			name:        "Only special characters",
			password:    "!@#$%^&*",
			expectError: true,
			errorMsg:    "password must contain at least one uppercase letter",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidatePassword(tt.password)

			if tt.expectError {
				assert.Error(t, err)
				if err != nil {
					assert.Equal(t, tt.errorMsg, err.Error())
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestPasswordConstants(t *testing.T) {
	assert.Equal(t, 8, MinPasswordLength)
	assert.Equal(t, 128, MaxPasswordLength)
}

// Benchmark tests
func BenchmarkHashPassword(b *testing.B) {
	password := "BenchmarkPassword123!"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = HashPassword(password)
	}
}

func BenchmarkCheckPassword(b *testing.B) {
	password := "BenchmarkPassword123!"
	hash, _ := HashPassword(password)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = CheckPassword(password, hash)
	}
}

func BenchmarkValidatePassword(b *testing.B) {
	password := "BenchmarkPassword123!"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = ValidatePassword(password)
	}
}
