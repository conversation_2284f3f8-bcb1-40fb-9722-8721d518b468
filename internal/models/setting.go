package models

import (
	"time"

	"gorm.io/gorm"
)

// SystemSetting represents system configuration settings
type SystemSetting struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Key         string         `json:"key" gorm:"type:varchar(100);uniqueIndex;not null"`
	Value       string         `json:"value" gorm:"type:text"`
	Type        string         `json:"type" gorm:"type:varchar(20);default:'string'"` // string, number, boolean, json
	Category    string         `json:"category" gorm:"type:varchar(50);index"`        // general, email, security, etc.
	Description string         `json:"description" gorm:"type:text"`
	IsPublic    bool           `json:"is_public" gorm:"default:false"`  // whether non-admin users can read this setting
	IsEditable  bool           `json:"is_editable" gorm:"default:true"` // whether this setting can be modified
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}
