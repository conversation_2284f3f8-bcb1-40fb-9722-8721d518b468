package models

import (
	"time"

	"gorm.io/gorm"
)

// User represents a user in the system
type User struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	Username  string         `json:"username" gorm:"type:varchar(100);uniqueIndex;not null"`
	Email     string         `json:"email" gorm:"type:varchar(255);uniqueIndex;not null"`
	Password  string         `json:"-" gorm:"type:varchar(255);not null"` // Hidden from JSON
	FirstName string         `json:"first_name" gorm:"type:varchar(100)"`
	LastName  string         `json:"last_name" gorm:"type:varchar(100)"`
	IsActive  bool           `json:"is_active" gorm:"default:true"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Roles      []Role      `json:"roles" gorm:"many2many:user_roles;"`
	Attributes []Attribute `json:"attributes" gorm:"foreignKey:UserID"`
}
