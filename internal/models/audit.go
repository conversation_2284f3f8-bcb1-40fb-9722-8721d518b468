package models

import (
	"time"
)

// AuditLog represents system audit logs
type AuditLog struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	UserID     *uint     `json:"user_id" gorm:"index"`                                   // nullable for system operations
	Username   string    `json:"username" gorm:"type:varchar(100)"`                      // denormalized for performance
	Action     string    `json:"action" gorm:"type:varchar(100);not null;index"`         // login, create_user, update_role, etc.
	Resource   string    `json:"resource" gorm:"type:varchar(100);index"`                // users, roles, permissions, etc.
	ResourceID *uint     `json:"resource_id" gorm:"index"`                               // ID of the affected resource
	Method     string    `json:"method" gorm:"type:varchar(10)"`                         // HTTP method
	Path       string    `json:"path" gorm:"type:varchar(500)"`                          // API path
	IPAddress  string    `json:"ip_address" gorm:"type:varchar(45)"`                     // IPv4/IPv6
	UserAgent  string    `json:"user_agent" gorm:"type:text"`                            // Browser/client info
	Status     string    `json:"status" gorm:"type:varchar(20);default:'success';index"` // success, failed, error
	Message    string    `json:"message" gorm:"type:text"`                               // Description or error message
	OldValues  string    `json:"old_values" gorm:"type:json"`                            // JSON of old values (for updates)
	NewValues  string    `json:"new_values" gorm:"type:json"`                            // JSON of new values (for updates)
	Duration   int64     `json:"duration"`                                               // Request duration in milliseconds
	CreatedAt  time.Time `json:"created_at" gorm:"index"`

	// Relationships
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}
