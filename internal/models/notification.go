package models

import (
	"time"

	"gorm.io/gorm"
)

// Notification represents system notifications
type Notification struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	Title      string         `json:"title" gorm:"type:varchar(200);not null"`
	Content    string         `json:"content" gorm:"type:text;not null"`
	Type       string         `json:"type" gorm:"type:varchar(20);default:'info'"` // info, success, warning, error
	Category   string         `json:"category" gorm:"type:varchar(50);index"`      // system, user, security, etc.
	Priority   int            `json:"priority" gorm:"default:0;index"`             // 0=low, 1=normal, 2=high, 3=urgent
	IsRead     bool           `json:"is_read" gorm:"default:false;index"`
	IsGlobal   bool           `json:"is_global" gorm:"default:false"`              // Global notification for all users
	SenderID   *uint          `json:"sender_id" gorm:"index"`                      // Who sent the notification
	ReceiverID *uint          `json:"receiver_id" gorm:"index"`                    // Who receives the notification
	ActionURL  string         `json:"action_url" gorm:"type:varchar(500)"`         // URL to navigate when clicked
	ExpiresAt  *time.Time     `json:"expires_at" gorm:"index"`                     // When notification expires
	CreatedAt  time.Time      `json:"created_at" gorm:"index"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Sender   *User `json:"sender,omitempty" gorm:"foreignKey:SenderID"`
	Receiver *User `json:"receiver,omitempty" gorm:"foreignKey:ReceiverID"`
}
