package models

import (
	"time"

	"gorm.io/gorm"
)

// Menu represents system menu items
type Menu struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"type:varchar(100);not null"`
	Title       string         `json:"title" gorm:"type:varchar(100);not null"`        // Display title
	Icon        string         `json:"icon" gorm:"type:varchar(50)"`                   // Icon class/name
	Path        string         `json:"path" gorm:"type:varchar(200)"`                  // Route path
	Component   string         `json:"component" gorm:"type:varchar(200)"`             // Vue component path
	ParentID    *uint          `json:"parent_id" gorm:"index"`                         // Parent menu ID
	Sort        int            `json:"sort" gorm:"default:0;index"`                    // Sort order
	IsVisible   bool           `json:"is_visible" gorm:"default:true"`                 // Whether visible in menu
	IsEnabled   bool           `json:"is_enabled" gorm:"default:true"`                 // Whether enabled
	Permission  string         `json:"permission" gorm:"type:varchar(100)"`            // Required permission
	MenuType    string         `json:"menu_type" gorm:"type:varchar(20);default:'menu'"` // menu, button, link
	ExternalURL string         `json:"external_url" gorm:"type:varchar(500)"`          // External link URL
	Target      string         `json:"target" gorm:"type:varchar(20);default:'_self'"` // Link target
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Parent   *Menu  `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Children []Menu `json:"children,omitempty" gorm:"foreignKey:ParentID"`
}
