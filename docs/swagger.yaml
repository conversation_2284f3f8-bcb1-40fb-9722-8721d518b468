basePath: /api/v1
definitions:
  dto.AssignPermissionRequest:
    properties:
      permission_id:
        minimum: 1
        type: integer
    required:
    - permission_id
    type: object
  dto.AssignSingleRoleRequest:
    properties:
      role_id:
        type: integer
    required:
    - role_id
    type: object
  dto.AttributeResponse:
    properties:
      created_at:
        type: string
      id:
        type: integer
      name:
        type: string
      type:
        type: string
      updated_at:
        type: string
      value:
        type: string
    type: object
  dto.BroadcastNotificationRequest:
    properties:
      action_url:
        maxLength: 500
        type: string
      category:
        maxLength: 50
        minLength: 1
        type: string
      content:
        minLength: 1
        type: string
      expires_at:
        type: string
      priority:
        maximum: 3
        minimum: 0
        type: integer
      title:
        maxLength: 200
        minLength: 1
        type: string
      type:
        enum:
        - info
        - success
        - warning
        - error
        type: string
    required:
    - category
    - content
    - title
    type: object
  dto.CreateMenuRequest:
    properties:
      component:
        maxLength: 200
        type: string
      external_url:
        maxLength: 500
        type: string
      icon:
        maxLength: 50
        type: string
      is_enabled:
        type: boolean
      is_visible:
        type: boolean
      menu_type:
        enum:
        - menu
        - button
        - link
        type: string
      name:
        maxLength: 100
        minLength: 2
        type: string
      parent_id:
        type: integer
      path:
        maxLength: 200
        type: string
      permission:
        maxLength: 100
        type: string
      sort:
        type: integer
      target:
        enum:
        - _self
        - _blank
        - _parent
        - _top
        type: string
      title:
        maxLength: 100
        minLength: 2
        type: string
    required:
    - menu_type
    - name
    - title
    type: object
  dto.CreateNotificationRequest:
    properties:
      action_url:
        maxLength: 500
        type: string
      category:
        maxLength: 50
        minLength: 1
        type: string
      content:
        minLength: 1
        type: string
      expires_at:
        type: string
      is_global:
        type: boolean
      priority:
        maximum: 3
        minimum: 0
        type: integer
      receiver_id:
        type: integer
      title:
        maxLength: 200
        minLength: 1
        type: string
      type:
        enum:
        - info
        - success
        - warning
        - error
        type: string
    required:
    - category
    - content
    - title
    type: object
  dto.CreatePermissionRequest:
    properties:
      action:
        maxLength: 50
        minLength: 2
        type: string
      description:
        maxLength: 500
        type: string
      name:
        maxLength: 100
        minLength: 2
        type: string
      resource:
        maxLength: 50
        minLength: 2
        type: string
    required:
    - action
    - name
    - resource
    type: object
  dto.CreateRoleRequest:
    properties:
      description:
        maxLength: 500
        type: string
      is_active:
        description: Pointer to distinguish between false and nil
        type: boolean
      name:
        maxLength: 100
        minLength: 2
        type: string
    required:
    - name
    type: object
  dto.CreateSettingRequest:
    properties:
      category:
        maxLength: 50
        minLength: 2
        type: string
      description:
        maxLength: 500
        type: string
      is_editable:
        type: boolean
      is_public:
        type: boolean
      key:
        maxLength: 100
        minLength: 2
        type: string
      type:
        enum:
        - string
        - number
        - boolean
        - json
        type: string
      value:
        type: string
    required:
    - category
    - key
    - type
    - value
    type: object
  dto.CreateUserRequest:
    properties:
      email:
        type: string
      first_name:
        maxLength: 50
        minLength: 1
        type: string
      is_active:
        description: Pointer to distinguish between false and nil
        type: boolean
      last_name:
        maxLength: 50
        minLength: 1
        type: string
      password:
        minLength: 8
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - email
    - first_name
    - last_name
    - password
    - username
    type: object
  dto.ErrorResponse:
    properties:
      code:
        type: integer
      details:
        additionalProperties:
          type: string
        type: object
      error:
        type: string
      message:
        type: string
    type: object
  dto.LoginRequest:
    properties:
      identifier:
        description: username or email
        type: string
      password:
        type: string
    required:
    - identifier
    - password
    type: object
  dto.LoginResponse:
    properties:
      expires_at:
        type: string
      token:
        type: string
      user:
        $ref: '#/definitions/dto.UserInfo'
    type: object
  dto.MessageResponse:
    properties:
      message:
        type: string
    type: object
  dto.NotificationListResponse:
    properties:
      notifications:
        items:
          $ref: '#/definitions/dto.NotificationResponse'
        type: array
      pagination:
        $ref: '#/definitions/dto.Pagination'
      unread_only:
        type: boolean
    type: object
  dto.NotificationResponse:
    properties:
      action_url:
        type: string
      category:
        type: string
      content:
        type: string
      created_at:
        type: string
      expires_at:
        type: string
      id:
        type: integer
      is_global:
        type: boolean
      is_read:
        type: boolean
      priority:
        type: integer
      receiver:
        $ref: '#/definitions/dto.UserInfo'
      sender:
        $ref: '#/definitions/dto.UserInfo'
      title:
        type: string
      type:
        type: string
      updated_at:
        type: string
    type: object
  dto.NotificationStatsResponse:
    properties:
      by_category:
        additionalProperties:
          format: int64
          type: integer
        type: object
      by_priority:
        additionalProperties:
          format: int64
          type: integer
        type: object
      by_type:
        additionalProperties:
          format: int64
          type: integer
        type: object
      read_count:
        type: integer
      total_notifications:
        type: integer
      unread_count:
        type: integer
    type: object
  dto.Pagination:
    properties:
      limit:
        type: integer
      page:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  dto.PermissionResponse:
    properties:
      action:
        type: string
      created_at:
        type: string
      description:
        type: string
      id:
        type: integer
      name:
        type: string
      resource:
        type: string
      updated_at:
        type: string
    type: object
  dto.RefreshTokenRequest:
    properties:
      token:
        type: string
    required:
    - token
    type: object
  dto.RegisterRequest:
    properties:
      email:
        type: string
      first_name:
        maxLength: 50
        minLength: 1
        type: string
      last_name:
        maxLength: 50
        minLength: 1
        type: string
      password:
        minLength: 8
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - email
    - first_name
    - last_name
    - password
    - username
    type: object
  dto.RoleListResponse:
    properties:
      pagination:
        $ref: '#/definitions/dto.Pagination'
      roles:
        items:
          $ref: '#/definitions/dto.RoleResponse'
        type: array
    type: object
  dto.RoleResponse:
    properties:
      created_at:
        type: string
      description:
        type: string
      id:
        type: integer
      is_active:
        type: boolean
      name:
        type: string
      permissions:
        items:
          $ref: '#/definitions/dto.PermissionResponse'
        type: array
      updated_at:
        type: string
    type: object
  dto.UpdateMenuRequest:
    properties:
      component:
        maxLength: 200
        type: string
      external_url:
        maxLength: 500
        type: string
      icon:
        maxLength: 50
        type: string
      is_enabled:
        type: boolean
      is_visible:
        type: boolean
      menu_type:
        enum:
        - menu
        - button
        - link
        type: string
      name:
        maxLength: 100
        minLength: 2
        type: string
      parent_id:
        type: integer
      path:
        maxLength: 200
        type: string
      permission:
        maxLength: 100
        type: string
      sort:
        type: integer
      target:
        enum:
        - _self
        - _blank
        - _parent
        - _top
        type: string
      title:
        maxLength: 100
        minLength: 2
        type: string
    required:
    - menu_type
    - name
    - title
    type: object
  dto.UpdatePermissionRequest:
    properties:
      action:
        maxLength: 50
        minLength: 2
        type: string
      description:
        maxLength: 500
        type: string
      name:
        maxLength: 100
        minLength: 2
        type: string
      resource:
        maxLength: 50
        minLength: 2
        type: string
    required:
    - action
    - name
    - resource
    type: object
  dto.UpdateRoleRequest:
    properties:
      description:
        maxLength: 500
        type: string
      is_active:
        description: Pointer to distinguish between false and nil
        type: boolean
      name:
        maxLength: 100
        minLength: 2
        type: string
    required:
    - name
    type: object
  dto.UpdateSettingRequest:
    properties:
      category:
        maxLength: 50
        minLength: 2
        type: string
      description:
        maxLength: 500
        type: string
      is_editable:
        type: boolean
      is_public:
        type: boolean
      type:
        enum:
        - string
        - number
        - boolean
        - json
        type: string
      value:
        type: string
    required:
    - category
    - type
    - value
    type: object
  dto.UpdateUserRequest:
    properties:
      email:
        type: string
      first_name:
        maxLength: 50
        minLength: 1
        type: string
      is_active:
        description: Pointer to distinguish between false and nil
        type: boolean
      last_name:
        maxLength: 50
        minLength: 1
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
    type: object
  dto.UserInfo:
    properties:
      created_at:
        type: string
      email:
        type: string
      first_name:
        type: string
      id:
        type: integer
      is_active:
        type: boolean
      last_name:
        type: string
      updated_at:
        type: string
      username:
        type: string
    type: object
  dto.UserListResponse:
    properties:
      pagination:
        $ref: '#/definitions/dto.Pagination'
      users:
        items:
          $ref: '#/definitions/dto.UserResponse'
        type: array
    type: object
  dto.UserResponse:
    properties:
      attributes:
        items:
          $ref: '#/definitions/dto.AttributeResponse'
        type: array
      created_at:
        type: string
      email:
        type: string
      first_name:
        type: string
      id:
        type: integer
      is_active:
        type: boolean
      last_name:
        type: string
      roles:
        items:
          $ref: '#/definitions/dto.RoleResponse'
        type: array
      updated_at:
        type: string
      username:
        type: string
    type: object
  models.Menu:
    properties:
      children:
        items:
          $ref: '#/definitions/models.Menu'
        type: array
      component:
        description: Vue component path
        type: string
      created_at:
        type: string
      external_url:
        description: External link URL
        type: string
      icon:
        description: Icon class/name
        type: string
      id:
        type: integer
      is_enabled:
        description: Whether enabled
        type: boolean
      is_visible:
        description: Whether visible in menu
        type: boolean
      menu_type:
        description: menu, button, link
        type: string
      name:
        type: string
      parent:
        allOf:
        - $ref: '#/definitions/models.Menu'
        description: Relationships
      parent_id:
        description: Parent menu ID
        type: integer
      path:
        description: Route path
        type: string
      permission:
        description: Required permission
        type: string
      sort:
        description: Sort order
        type: integer
      target:
        description: Link target
        type: string
      title:
        description: Display title
        type: string
      updated_at:
        type: string
    type: object
  models.SystemSetting:
    properties:
      category:
        description: general, email, security, etc.
        type: string
      created_at:
        type: string
      description:
        type: string
      id:
        type: integer
      is_editable:
        description: whether this setting can be modified
        type: boolean
      is_public:
        description: whether non-admin users can read this setting
        type: boolean
      key:
        type: string
      type:
        description: string, number, boolean, json
        type: string
      updated_at:
        type: string
      value:
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is a comprehensive admin system API with RBAC/ABAC authorization,
    user management, notifications, and more.
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: Admin System API
  version: "1.0"
paths:
  /admin/notifications:
    post:
      consumes:
      - application/json
      description: Create a new notification for a specific user or globally
      parameters:
      - description: Notification creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.CreateNotificationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Notification created successfully
          schema:
            $ref: '#/definitions/dto.NotificationResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create new notification
      tags:
      - Notifications
  /admin/notifications/broadcast:
    post:
      consumes:
      - application/json
      description: Create a global notification for all users (admin only)
      parameters:
      - description: Broadcast notification data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.BroadcastNotificationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Notification broadcasted successfully
          schema:
            $ref: '#/definitions/dto.NotificationResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Broadcast notification
      tags:
      - Notifications
  /admin/notifications/cleanup:
    delete:
      consumes:
      - application/json
      description: Remove all expired notifications from the system (admin only)
      produces:
      - application/json
      responses:
        "200":
          description: Expired notifications cleaned up
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Cleanup expired notifications
      tags:
      - Notifications
  /audit/logs:
    get:
      consumes:
      - application/json
      description: Get paginated audit logs with optional filtering
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 20
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by username
        in: query
        name: username
        type: string
      - description: Filter by action
        in: query
        name: action
        type: string
      - description: Filter by resource
        in: query
        name: resource
        type: string
      - description: Filter by status
        in: query
        name: status
        type: string
      - description: Filter by IP address
        in: query
        name: ip_address
        type: string
      - description: Filter by resource ID
        in: query
        name: resource_id
        type: integer
      - description: Start date (YYYY-MM-DD)
        in: query
        name: start_date
        type: string
      - description: End date (YYYY-MM-DD)
        in: query
        name: end_date
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Audit logs retrieved successfully
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get audit logs
      tags:
      - Audit
  /audit/stats:
    get:
      consumes:
      - application/json
      description: Get overall audit log statistics
      produces:
      - application/json
      responses:
        "200":
          description: Audit statistics retrieved successfully
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get audit statistics
      tags:
      - Audit
  /audit/users/{userId}:
    get:
      consumes:
      - application/json
      description: Get paginated audit logs for a specific user
      parameters:
      - description: User ID
        in: path
        name: userId
        required: true
        type: integer
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 20
        description: Items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: User audit logs retrieved successfully
          schema:
            type: object
        "400":
          description: Invalid user ID
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get user audit logs
      tags:
      - Audit
  /auth/login:
    post:
      consumes:
      - application/json
      description: Authenticate user with username/email and password
      parameters:
      - description: Login credentials
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Login successful
          schema:
            $ref: '#/definitions/dto.LoginResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "401":
          description: Invalid credentials
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      summary: User login
      tags:
      - Authentication
  /auth/logout:
    post:
      consumes:
      - application/json
      description: Logout user (client-side token removal, server doesn't maintain
        token state)
      produces:
      - application/json
      responses:
        "200":
          description: Logout successful
          schema:
            $ref: '#/definitions/dto.MessageResponse'
      security:
      - BearerAuth: []
      summary: User logout
      tags:
      - Authentication
  /auth/profile:
    get:
      consumes:
      - application/json
      description: Get the profile information of the currently authenticated user
      produces:
      - application/json
      responses:
        "200":
          description: User profile retrieved successfully
          schema:
            $ref: '#/definitions/dto.UserResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get current user profile
      tags:
      - Authentication
  /auth/refresh:
    post:
      consumes:
      - application/json
      description: Refresh an existing JWT token to extend session
      parameters:
      - description: Refresh token request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.RefreshTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Token refreshed successfully
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "401":
          description: Invalid or expired token
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      summary: Refresh JWT token
      tags:
      - Authentication
  /auth/register:
    post:
      consumes:
      - application/json
      description: Register a new user account
      parameters:
      - description: Registration data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.RegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Registration successful
          schema:
            $ref: '#/definitions/dto.LoginResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "409":
          description: User already exists
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      summary: User registration
      tags:
      - Authentication
  /menus:
    get:
      consumes:
      - application/json
      description: Get menus in list or tree format, optionally filtered by visibility
      parameters:
      - description: Return as tree structure
        in: query
        name: tree
        type: boolean
      - description: Return only visible menus (when tree=true)
        in: query
        name: visible
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: Menus retrieved successfully
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get all menus
      tags:
      - Menus
    post:
      consumes:
      - application/json
      description: Create a new menu item with the provided information
      parameters:
      - description: Menu creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.CreateMenuRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Menu created successfully
          schema:
            $ref: '#/definitions/models.Menu'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "409":
          description: Menu name already exists or parent not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create new menu
      tags:
      - Menus
  /menus/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a menu by ID (soft delete)
      parameters:
      - description: Menu ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Menu deleted successfully
          schema:
            $ref: '#/definitions/dto.MessageResponse'
        "400":
          description: Invalid menu ID
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Menu not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "409":
          description: Cannot delete menu with children
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete menu
      tags:
      - Menus
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific menu item
      parameters:
      - description: Menu ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Menu retrieved successfully
          schema:
            $ref: '#/definitions/models.Menu'
        "400":
          description: Invalid menu ID
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Menu not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get menu by ID
      tags:
      - Menus
    put:
      consumes:
      - application/json
      description: Update menu information by ID
      parameters:
      - description: Menu ID
        in: path
        name: id
        required: true
        type: integer
      - description: Menu update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.UpdateMenuRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Menu updated successfully
          schema:
            $ref: '#/definitions/models.Menu'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Menu not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "409":
          description: Menu name already exists
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update menu
      tags:
      - Menus
  /menus/initialize:
    post:
      consumes:
      - application/json
      description: Initialize default system menus (admin only)
      produces:
      - application/json
      responses:
        "200":
          description: Default menus initialized successfully
          schema:
            $ref: '#/definitions/dto.MessageResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Initialize default menus
      tags:
      - Menus
  /menus/user:
    get:
      consumes:
      - application/json
      description: Get menu tree that the current user has permission to access
      produces:
      - application/json
      responses:
        "200":
          description: User menus retrieved successfully
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get user accessible menus
      tags:
      - Menus
  /notifications:
    get:
      consumes:
      - application/json
      description: Get paginated list of notifications for the current user
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 20
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Get only unread notifications
        in: query
        name: unread
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: Notifications retrieved successfully
          schema:
            $ref: '#/definitions/dto.NotificationListResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get my notifications
      tags:
      - Notifications
  /notifications/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a specific notification for the current user
      parameters:
      - description: Notification ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Notification deleted successfully
          schema:
            $ref: '#/definitions/dto.MessageResponse'
        "400":
          description: Invalid notification ID
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "403":
          description: Permission denied
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Notification not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete notification
      tags:
      - Notifications
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific notification
      parameters:
      - description: Notification ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Notification retrieved successfully
          schema:
            $ref: '#/definitions/dto.NotificationResponse'
        "400":
          description: Invalid notification ID
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "403":
          description: Access denied
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Notification not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get notification by ID
      tags:
      - Notifications
  /notifications/{id}/read:
    put:
      consumes:
      - application/json
      description: Mark a specific notification as read for the current user
      parameters:
      - description: Notification ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Notification marked as read
          schema:
            $ref: '#/definitions/dto.MessageResponse'
        "400":
          description: Invalid notification ID
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "403":
          description: Permission denied
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Notification not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Mark notification as read
      tags:
      - Notifications
  /notifications/read-all:
    put:
      consumes:
      - application/json
      description: Mark all notifications for the current user as read
      produces:
      - application/json
      responses:
        "200":
          description: All notifications marked as read
          schema:
            type: object
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Mark all notifications as read
      tags:
      - Notifications
  /notifications/stats:
    get:
      consumes:
      - application/json
      description: Get notification statistics for the current user
      produces:
      - application/json
      responses:
        "200":
          description: Statistics retrieved successfully
          schema:
            $ref: '#/definitions/dto.NotificationStatsResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get my notification statistics
      tags:
      - Notifications
  /permissions:
    get:
      consumes:
      - application/json
      description: Get paginated list of permissions
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Permissions retrieved successfully
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get all permissions
      tags:
      - Permissions
    post:
      consumes:
      - application/json
      description: Create a new permission with the provided information
      parameters:
      - description: Permission creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.CreatePermissionRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Permission created successfully
          schema:
            $ref: '#/definitions/dto.PermissionResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "409":
          description: Permission already exists
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create new permission
      tags:
      - Permissions
  /permissions/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a permission by ID (soft delete)
      parameters:
      - description: Permission ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Permission deleted successfully
          schema:
            $ref: '#/definitions/dto.MessageResponse'
        "400":
          description: Invalid permission ID
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Permission not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "409":
          description: Cannot delete permission assigned to roles
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete permission
      tags:
      - Permissions
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific permission
      parameters:
      - description: Permission ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Permission retrieved successfully
          schema:
            $ref: '#/definitions/dto.PermissionResponse'
        "400":
          description: Invalid permission ID
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Permission not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get permission by ID
      tags:
      - Permissions
    put:
      consumes:
      - application/json
      description: Update permission information by ID
      parameters:
      - description: Permission ID
        in: path
        name: id
        required: true
        type: integer
      - description: Permission update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.UpdatePermissionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Permission updated successfully
          schema:
            $ref: '#/definitions/dto.PermissionResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Permission not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "409":
          description: Permission name already exists
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update permission
      tags:
      - Permissions
  /permissions/grouped:
    get:
      consumes:
      - application/json
      description: Get all permissions organized by resource type
      produces:
      - application/json
      responses:
        "200":
          description: Grouped permissions retrieved successfully
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get permissions grouped by resource
      tags:
      - Permissions
  /permissions/resource/{resource}:
    get:
      consumes:
      - application/json
      description: Get all permissions for a specific resource type
      parameters:
      - description: Resource name
        in: path
        name: resource
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Permissions retrieved successfully
          schema:
            type: object
        "400":
          description: Invalid resource parameter
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get permissions by resource
      tags:
      - Permissions
  /permissions/resources:
    get:
      consumes:
      - application/json
      description: Get a list of all unique resource types
      produces:
      - application/json
      responses:
        "200":
          description: Resource list retrieved successfully
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get resource list
      tags:
      - Permissions
  /roles:
    get:
      consumes:
      - application/json
      description: Get paginated list of roles
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Roles retrieved successfully
          schema:
            $ref: '#/definitions/dto.RoleListResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get all roles
      tags:
      - Roles
    post:
      consumes:
      - application/json
      description: Create a new role with the provided information
      parameters:
      - description: Role creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.CreateRoleRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Role created successfully
          schema:
            $ref: '#/definitions/dto.RoleResponse'
        "400":
          description: Invalid request or role already exists
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create new role
      tags:
      - Roles
  /roles/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a role by ID (soft delete)
      parameters:
      - description: Role ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Role deleted successfully
          schema:
            $ref: '#/definitions/dto.MessageResponse'
        "400":
          description: Invalid role ID
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Role not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete role
      tags:
      - Roles
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific role
      parameters:
      - description: Role ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Role retrieved successfully
          schema:
            $ref: '#/definitions/dto.RoleResponse'
        "400":
          description: Invalid role ID
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Role not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get role by ID
      tags:
      - Roles
    put:
      consumes:
      - application/json
      description: Update role information by ID
      parameters:
      - description: Role ID
        in: path
        name: id
        required: true
        type: integer
      - description: Role update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.UpdateRoleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Role updated successfully
          schema:
            $ref: '#/definitions/dto.RoleResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Role not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "409":
          description: Role name already exists
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update role
      tags:
      - Roles
  /roles/{id}/permissions:
    get:
      consumes:
      - application/json
      description: Get all permissions assigned to a specific role
      parameters:
      - description: Role ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Role permissions retrieved successfully
          schema:
            type: object
        "400":
          description: Invalid role ID
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get role permissions
      tags:
      - Roles
    post:
      consumes:
      - application/json
      description: Assign a permission to a specific role
      parameters:
      - description: Role ID
        in: path
        name: id
        required: true
        type: integer
      - description: Permission assignment data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.AssignPermissionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Permission assigned successfully
          schema:
            $ref: '#/definitions/dto.MessageResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Role or permission not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "409":
          description: Permission already assigned
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Assign permission to role
      tags:
      - Roles
  /roles/{id}/permissions/{permissionId}:
    delete:
      consumes:
      - application/json
      description: Remove a specific permission from a role
      parameters:
      - description: Role ID
        in: path
        name: id
        required: true
        type: integer
      - description: Permission ID
        in: path
        name: permissionId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Permission removed successfully
          schema:
            $ref: '#/definitions/dto.MessageResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Permission assignment not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Remove permission from role
      tags:
      - Roles
  /settings:
    get:
      consumes:
      - application/json
      description: Get all system settings (admin only) or public settings
      produces:
      - application/json
      responses:
        "200":
          description: Settings retrieved successfully
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get all settings
      tags:
      - Settings
    post:
      consumes:
      - application/json
      description: Create a new system setting with the provided information
      parameters:
      - description: Setting creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.CreateSettingRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Setting created successfully
          schema:
            $ref: '#/definitions/models.SystemSetting'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "409":
          description: Setting is not editable
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create new setting
      tags:
      - Settings
  /settings/{key}:
    delete:
      consumes:
      - application/json
      description: Delete a system setting by key
      parameters:
      - description: Setting key
        in: path
        name: key
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Setting deleted successfully
          schema:
            $ref: '#/definitions/dto.MessageResponse'
        "400":
          description: Invalid setting key
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Setting not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "409":
          description: Setting is not editable
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete setting
      tags:
      - Settings
    get:
      consumes:
      - application/json
      description: Get a specific system setting by key
      parameters:
      - description: Setting key
        in: path
        name: key
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Setting retrieved successfully
          schema:
            $ref: '#/definitions/models.SystemSetting'
        "400":
          description: Invalid setting key
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "403":
          description: Access denied to private setting
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Setting not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get setting by key
      tags:
      - Settings
    put:
      consumes:
      - application/json
      description: Update a system setting by key
      parameters:
      - description: Setting key
        in: path
        name: key
        required: true
        type: string
      - description: Setting update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.UpdateSettingRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Setting updated successfully
          schema:
            $ref: '#/definitions/models.SystemSetting'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: Setting not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "409":
          description: Setting is not editable
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update setting
      tags:
      - Settings
  /settings/categories:
    get:
      consumes:
      - application/json
      description: Get all available setting categories
      produces:
      - application/json
      responses:
        "200":
          description: Categories retrieved successfully
          schema:
            type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get setting categories
      tags:
      - Settings
  /users:
    get:
      consumes:
      - application/json
      description: Get paginated list of users
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 20
        description: Items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Users retrieved successfully
          schema:
            $ref: '#/definitions/dto.UserListResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get all users
      tags:
      - Users
    post:
      consumes:
      - application/json
      description: Create a new user account with the provided information
      parameters:
      - description: User creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.CreateUserRequest'
      produces:
      - application/json
      responses:
        "201":
          description: User created successfully
          schema:
            $ref: '#/definitions/dto.UserResponse'
        "400":
          description: Invalid request or user already exists
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create new user
      tags:
      - Users
  /users/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a user by ID (soft delete)
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: User deleted successfully
          schema:
            $ref: '#/definitions/dto.MessageResponse'
        "400":
          description: Invalid user ID
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "403":
          description: Cannot delete super admin
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete user
      tags:
      - Users
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific user
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: User retrieved successfully
          schema:
            $ref: '#/definitions/dto.UserResponse'
        "400":
          description: Invalid user ID
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get user by ID
      tags:
      - Users
    put:
      consumes:
      - application/json
      description: Update user information by ID
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      - description: User update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.UpdateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: User updated successfully
          schema:
            $ref: '#/definitions/dto.UserResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "403":
          description: Forbidden operation
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update user
      tags:
      - Users
  /users/{id}/roles:
    post:
      consumes:
      - application/json
      description: Assign a role to a specific user
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      - description: Role assignment data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.AssignSingleRoleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Role assigned successfully
          schema:
            $ref: '#/definitions/dto.MessageResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Assign role to user
      tags:
      - Users
  /users/{id}/roles/{roleId}:
    delete:
      consumes:
      - application/json
      description: Remove a specific role from a user
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      - description: Role ID
        in: path
        name: roleId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Role removed successfully
          schema:
            $ref: '#/definitions/dto.MessageResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/dto.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Remove role from user
      tags:
      - Users
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
