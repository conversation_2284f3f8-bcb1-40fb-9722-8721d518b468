{"swagger": "2.0", "info": {"description": "This is a comprehensive admin system API with RBAC/ABAC authorization, user management, notifications, and more.", "title": "Admin System API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/api/v1", "paths": {"/admin/notifications": {"post": {"security": [{"BearerAuth": []}], "description": "Create a new notification for a specific user or globally", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Notifications"], "summary": "Create new notification", "parameters": [{"description": "Notification creation data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateNotificationRequest"}}], "responses": {"201": {"description": "Notification created successfully", "schema": {"$ref": "#/definitions/dto.NotificationResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "401": {"description": "User not authenticated", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/admin/notifications/broadcast": {"post": {"security": [{"BearerAuth": []}], "description": "Create a global notification for all users (admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Notifications"], "summary": "Broadcast notification", "parameters": [{"description": "Broadcast notification data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.BroadcastNotificationRequest"}}], "responses": {"201": {"description": "Notification broadcasted successfully", "schema": {"$ref": "#/definitions/dto.NotificationResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/admin/notifications/cleanup": {"delete": {"security": [{"BearerAuth": []}], "description": "Remove all expired notifications from the system (admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Notifications"], "summary": "Cleanup expired notifications", "responses": {"200": {"description": "Expired notifications cleaned up", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/audit/logs": {"get": {"security": [{"BearerAuth": []}], "description": "Get paginated audit logs with optional filtering", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Audit"], "summary": "Get audit logs", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Filter by username", "name": "username", "in": "query"}, {"type": "string", "description": "Filter by action", "name": "action", "in": "query"}, {"type": "string", "description": "Filter by resource", "name": "resource", "in": "query"}, {"type": "string", "description": "Filter by status", "name": "status", "in": "query"}, {"type": "string", "description": "Filter by IP address", "name": "ip_address", "in": "query"}, {"type": "integer", "description": "Filter by resource ID", "name": "resource_id", "in": "query"}, {"type": "string", "description": "Start date (YYYY-MM-DD)", "name": "start_date", "in": "query"}, {"type": "string", "description": "End date (YYYY-MM-DD)", "name": "end_date", "in": "query"}], "responses": {"200": {"description": "Audit logs retrieved successfully", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/audit/stats": {"get": {"security": [{"BearerAuth": []}], "description": "Get overall audit log statistics", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Audit"], "summary": "Get audit statistics", "responses": {"200": {"description": "Audit statistics retrieved successfully", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/audit/users/{userId}": {"get": {"security": [{"BearerAuth": []}], "description": "Get paginated audit logs for a specific user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Audit"], "summary": "Get user audit logs", "parameters": [{"type": "integer", "description": "User ID", "name": "userId", "in": "path", "required": true}, {"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "Items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "User audit logs retrieved successfully", "schema": {"type": "object"}}, "400": {"description": "Invalid user ID", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/auth/login": {"post": {"description": "Authenticate user with username/email and password", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "User login", "parameters": [{"description": "Login credentials", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.LoginRequest"}}], "responses": {"200": {"description": "Login successful", "schema": {"$ref": "#/definitions/dto.LoginResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "401": {"description": "Invalid credentials", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/auth/logout": {"post": {"security": [{"BearerAuth": []}], "description": "Logout user (client-side token removal, server doesn't maintain token state)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "User logout", "responses": {"200": {"description": "Logout successful", "schema": {"$ref": "#/definitions/dto.MessageResponse"}}}}}, "/auth/profile": {"get": {"security": [{"BearerAuth": []}], "description": "Get the profile information of the currently authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Get current user profile", "responses": {"200": {"description": "User profile retrieved successfully", "schema": {"$ref": "#/definitions/dto.UserResponse"}}, "401": {"description": "User not authenticated", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "User not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/auth/refresh": {"post": {"description": "Refresh an existing JWT token to extend session", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Refresh JWT token", "parameters": [{"description": "Refresh token request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.RefreshTokenRequest"}}], "responses": {"200": {"description": "<PERSON><PERSON> refreshed successfully", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "401": {"description": "Invalid or expired token", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/auth/register": {"post": {"description": "Register a new user account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "User registration", "parameters": [{"description": "Registration data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.RegisterRequest"}}], "responses": {"201": {"description": "Registration successful", "schema": {"$ref": "#/definitions/dto.LoginResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "409": {"description": "User already exists", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/menus": {"get": {"security": [{"BearerAuth": []}], "description": "Get menus in list or tree format, optionally filtered by visibility", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Menus"], "summary": "Get all menus", "parameters": [{"type": "boolean", "description": "Return as tree structure", "name": "tree", "in": "query"}, {"type": "boolean", "description": "Return only visible menus (when tree=true)", "name": "visible", "in": "query"}], "responses": {"200": {"description": "<PERSON><PERSON> retrieved successfully", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new menu item with the provided information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Menus"], "summary": "Create new menu", "parameters": [{"description": "Menu creation data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateMenuRequest"}}], "responses": {"201": {"description": "<PERSON><PERSON> created successfully", "schema": {"$ref": "#/definitions/models.Menu"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "409": {"description": "Menu name already exists or parent not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/menus/initialize": {"post": {"security": [{"BearerAuth": []}], "description": "Initialize default system menus (admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Menus"], "summary": "Initialize default menus", "responses": {"200": {"description": "Default menus initialized successfully", "schema": {"$ref": "#/definitions/dto.MessageResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/menus/user": {"get": {"security": [{"BearerAuth": []}], "description": "Get menu tree that the current user has permission to access", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Menus"], "summary": "Get user accessible menus", "responses": {"200": {"description": "User menus retrieved successfully", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/menus/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get detailed information about a specific menu item", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Menus"], "summary": "Get menu by ID", "parameters": [{"type": "integer", "description": "Menu ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "<PERSON><PERSON> retrieved successfully", "schema": {"$ref": "#/definitions/models.Menu"}}, "400": {"description": "Invalid menu ID", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "<PERSON><PERSON> not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update menu information by ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Menus"], "summary": "Update menu", "parameters": [{"type": "integer", "description": "Menu ID", "name": "id", "in": "path", "required": true}, {"description": "Menu update data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.UpdateMenuRequest"}}], "responses": {"200": {"description": "Menu updated successfully", "schema": {"$ref": "#/definitions/models.Menu"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "<PERSON><PERSON> not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "409": {"description": "Menu name already exists", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a menu by ID (soft delete)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Menus"], "summary": "Delete menu", "parameters": [{"type": "integer", "description": "Menu ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "<PERSON><PERSON> deleted successfully", "schema": {"$ref": "#/definitions/dto.MessageResponse"}}, "400": {"description": "Invalid menu ID", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "<PERSON><PERSON> not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "409": {"description": "Cannot delete menu with children", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/notifications": {"get": {"security": [{"BearerAuth": []}], "description": "Get paginated list of notifications for the current user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Notifications"], "summary": "Get my notifications", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "boolean", "description": "Get only unread notifications", "name": "unread", "in": "query"}], "responses": {"200": {"description": "Notifications retrieved successfully", "schema": {"$ref": "#/definitions/dto.NotificationListResponse"}}, "401": {"description": "User not authenticated", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/notifications/read-all": {"put": {"security": [{"BearerAuth": []}], "description": "Mark all notifications for the current user as read", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Notifications"], "summary": "Mark all notifications as read", "responses": {"200": {"description": "All notifications marked as read", "schema": {"type": "object"}}, "401": {"description": "User not authenticated", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/notifications/stats": {"get": {"security": [{"BearerAuth": []}], "description": "Get notification statistics for the current user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Notifications"], "summary": "Get my notification statistics", "responses": {"200": {"description": "Statistics retrieved successfully", "schema": {"$ref": "#/definitions/dto.NotificationStatsResponse"}}, "401": {"description": "User not authenticated", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/notifications/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get detailed information about a specific notification", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Notifications"], "summary": "Get notification by ID", "parameters": [{"type": "integer", "description": "Notification ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Notification retrieved successfully", "schema": {"$ref": "#/definitions/dto.NotificationResponse"}}, "400": {"description": "Invalid notification ID", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "401": {"description": "User not authenticated", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "403": {"description": "Access denied", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "Notification not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a specific notification for the current user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Notifications"], "summary": "Delete notification", "parameters": [{"type": "integer", "description": "Notification ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Notification deleted successfully", "schema": {"$ref": "#/definitions/dto.MessageResponse"}}, "400": {"description": "Invalid notification ID", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "401": {"description": "User not authenticated", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "403": {"description": "Permission denied", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "Notification not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/notifications/{id}/read": {"put": {"security": [{"BearerAuth": []}], "description": "Mark a specific notification as read for the current user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Notifications"], "summary": "Mark notification as read", "parameters": [{"type": "integer", "description": "Notification ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Notification marked as read", "schema": {"$ref": "#/definitions/dto.MessageResponse"}}, "400": {"description": "Invalid notification ID", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "401": {"description": "User not authenticated", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "403": {"description": "Permission denied", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "Notification not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/permissions": {"get": {"security": [{"BearerAuth": []}], "description": "Get paginated list of permissions", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Permissions"], "summary": "Get all permissions", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "Permissions retrieved successfully", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new permission with the provided information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Permissions"], "summary": "Create new permission", "parameters": [{"description": "Permission creation data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreatePermissionRequest"}}], "responses": {"201": {"description": "Permission created successfully", "schema": {"$ref": "#/definitions/dto.PermissionResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "409": {"description": "Permission already exists", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/permissions/grouped": {"get": {"security": [{"BearerAuth": []}], "description": "Get all permissions organized by resource type", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Permissions"], "summary": "Get permissions grouped by resource", "responses": {"200": {"description": "Grouped permissions retrieved successfully", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/permissions/resource/{resource}": {"get": {"security": [{"BearerAuth": []}], "description": "Get all permissions for a specific resource type", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Permissions"], "summary": "Get permissions by resource", "parameters": [{"type": "string", "description": "Resource name", "name": "resource", "in": "path", "required": true}], "responses": {"200": {"description": "Permissions retrieved successfully", "schema": {"type": "object"}}, "400": {"description": "Invalid resource parameter", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/permissions/resources": {"get": {"security": [{"BearerAuth": []}], "description": "Get a list of all unique resource types", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Permissions"], "summary": "Get resource list", "responses": {"200": {"description": "Resource list retrieved successfully", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/permissions/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get detailed information about a specific permission", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Permissions"], "summary": "Get permission by ID", "parameters": [{"type": "integer", "description": "Permission ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Permission retrieved successfully", "schema": {"$ref": "#/definitions/dto.PermissionResponse"}}, "400": {"description": "Invalid permission ID", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "Permission not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update permission information by ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Permissions"], "summary": "Update permission", "parameters": [{"type": "integer", "description": "Permission ID", "name": "id", "in": "path", "required": true}, {"description": "Permission update data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.UpdatePermissionRequest"}}], "responses": {"200": {"description": "Permission updated successfully", "schema": {"$ref": "#/definitions/dto.PermissionResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "Permission not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "409": {"description": "Permission name already exists", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a permission by ID (soft delete)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Permissions"], "summary": "Delete permission", "parameters": [{"type": "integer", "description": "Permission ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Permission deleted successfully", "schema": {"$ref": "#/definitions/dto.MessageResponse"}}, "400": {"description": "Invalid permission ID", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "Permission not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "409": {"description": "Cannot delete permission assigned to roles", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/roles": {"get": {"security": [{"BearerAuth": []}], "description": "Get paginated list of roles", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Roles"], "summary": "Get all roles", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "Roles retrieved successfully", "schema": {"$ref": "#/definitions/dto.RoleListResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new role with the provided information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Roles"], "summary": "Create new role", "parameters": [{"description": "Role creation data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateRoleRequest"}}], "responses": {"201": {"description": "Role created successfully", "schema": {"$ref": "#/definitions/dto.RoleResponse"}}, "400": {"description": "Invalid request or role already exists", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/roles/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get detailed information about a specific role", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Roles"], "summary": "Get role by ID", "parameters": [{"type": "integer", "description": "Role ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Role retrieved successfully", "schema": {"$ref": "#/definitions/dto.RoleResponse"}}, "400": {"description": "Invalid role ID", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "Role not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update role information by ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Roles"], "summary": "Update role", "parameters": [{"type": "integer", "description": "Role ID", "name": "id", "in": "path", "required": true}, {"description": "Role update data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.UpdateRoleRequest"}}], "responses": {"200": {"description": "Role updated successfully", "schema": {"$ref": "#/definitions/dto.RoleResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "Role not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "409": {"description": "Role name already exists", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a role by <PERSON> (soft delete)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Roles"], "summary": "Delete role", "parameters": [{"type": "integer", "description": "Role ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Role deleted successfully", "schema": {"$ref": "#/definitions/dto.MessageResponse"}}, "400": {"description": "Invalid role ID", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "Role not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/roles/{id}/permissions": {"get": {"security": [{"BearerAuth": []}], "description": "Get all permissions assigned to a specific role", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Roles"], "summary": "Get role permissions", "parameters": [{"type": "integer", "description": "Role ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Role permissions retrieved successfully", "schema": {"type": "object"}}, "400": {"description": "Invalid role ID", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Assign a permission to a specific role", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Roles"], "summary": "Assign permission to role", "parameters": [{"type": "integer", "description": "Role ID", "name": "id", "in": "path", "required": true}, {"description": "Permission assignment data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.AssignPermissionRequest"}}], "responses": {"200": {"description": "Permission assigned successfully", "schema": {"$ref": "#/definitions/dto.MessageResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "Role or permission not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "409": {"description": "Permission already assigned", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/roles/{id}/permissions/{permissionId}": {"delete": {"security": [{"BearerAuth": []}], "description": "Remove a specific permission from a role", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Roles"], "summary": "Remove permission from role", "parameters": [{"type": "integer", "description": "Role ID", "name": "id", "in": "path", "required": true}, {"type": "integer", "description": "Permission ID", "name": "permissionId", "in": "path", "required": true}], "responses": {"200": {"description": "Permission removed successfully", "schema": {"$ref": "#/definitions/dto.MessageResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "Permission assignment not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/settings": {"get": {"security": [{"BearerAuth": []}], "description": "Get all system settings (admin only) or public settings", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Settings"], "summary": "Get all settings", "responses": {"200": {"description": "Settings retrieved successfully", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new system setting with the provided information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Settings"], "summary": "Create new setting", "parameters": [{"description": "Setting creation data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateSettingRequest"}}], "responses": {"201": {"description": "Setting created successfully", "schema": {"$ref": "#/definitions/models.SystemSetting"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "409": {"description": "Setting is not editable", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/settings/categories": {"get": {"security": [{"BearerAuth": []}], "description": "Get all available setting categories", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Settings"], "summary": "Get setting categories", "responses": {"200": {"description": "Categories retrieved successfully", "schema": {"type": "object"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/settings/{key}": {"get": {"security": [{"BearerAuth": []}], "description": "Get a specific system setting by key", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Settings"], "summary": "Get setting by key", "parameters": [{"type": "string", "description": "Setting key", "name": "key", "in": "path", "required": true}], "responses": {"200": {"description": "Setting retrieved successfully", "schema": {"$ref": "#/definitions/models.SystemSetting"}}, "400": {"description": "Invalid setting key", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "403": {"description": "Access denied to private setting", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "Setting not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update a system setting by key", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Settings"], "summary": "Update setting", "parameters": [{"type": "string", "description": "Setting key", "name": "key", "in": "path", "required": true}, {"description": "Setting update data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.UpdateSettingRequest"}}], "responses": {"200": {"description": "Setting updated successfully", "schema": {"$ref": "#/definitions/models.SystemSetting"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "Setting not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "409": {"description": "Setting is not editable", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a system setting by key", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Settings"], "summary": "Delete setting", "parameters": [{"type": "string", "description": "Setting key", "name": "key", "in": "path", "required": true}], "responses": {"200": {"description": "Setting deleted successfully", "schema": {"$ref": "#/definitions/dto.MessageResponse"}}, "400": {"description": "Invalid setting key", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "Setting not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "409": {"description": "Setting is not editable", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/users": {"get": {"security": [{"BearerAuth": []}], "description": "Get paginated list of users", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Get all users", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "Items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "Users retrieved successfully", "schema": {"$ref": "#/definitions/dto.UserListResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new user account with the provided information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Create new user", "parameters": [{"description": "User creation data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateUserRequest"}}], "responses": {"201": {"description": "User created successfully", "schema": {"$ref": "#/definitions/dto.UserResponse"}}, "400": {"description": "Invalid request or user already exists", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/users/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get detailed information about a specific user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Get user by ID", "parameters": [{"type": "integer", "description": "User ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "User retrieved successfully", "schema": {"$ref": "#/definitions/dto.UserResponse"}}, "400": {"description": "Invalid user ID", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "User not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update user information by ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Update user", "parameters": [{"type": "integer", "description": "User ID", "name": "id", "in": "path", "required": true}, {"description": "User update data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.UpdateUserRequest"}}], "responses": {"200": {"description": "User updated successfully", "schema": {"$ref": "#/definitions/dto.UserResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "403": {"description": "Forbidden operation", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "User not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a user by ID (soft delete)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Delete user", "parameters": [{"type": "integer", "description": "User ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "User deleted successfully", "schema": {"$ref": "#/definitions/dto.MessageResponse"}}, "400": {"description": "Invalid user ID", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "401": {"description": "User not authenticated", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "403": {"description": "Cannot delete super admin", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}, "404": {"description": "User not found", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/users/{id}/roles": {"post": {"security": [{"BearerAuth": []}], "description": "Assign a role to a specific user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Assign role to user", "parameters": [{"type": "integer", "description": "User ID", "name": "id", "in": "path", "required": true}, {"description": "Role assignment data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.AssignSingleRoleRequest"}}], "responses": {"200": {"description": "Role assigned successfully", "schema": {"$ref": "#/definitions/dto.MessageResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}, "/users/{id}/roles/{roleId}": {"delete": {"security": [{"BearerAuth": []}], "description": "Remove a specific role from a user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Remove role from user", "parameters": [{"type": "integer", "description": "User ID", "name": "id", "in": "path", "required": true}, {"type": "integer", "description": "Role ID", "name": "roleId", "in": "path", "required": true}], "responses": {"200": {"description": "Role removed successfully", "schema": {"$ref": "#/definitions/dto.MessageResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/dto.ErrorResponse"}}}}}}, "definitions": {"dto.AssignPermissionRequest": {"type": "object", "required": ["permission_id"], "properties": {"permission_id": {"type": "integer", "minimum": 1}}}, "dto.AssignSingleRoleRequest": {"type": "object", "required": ["role_id"], "properties": {"role_id": {"type": "integer"}}}, "dto.AttributeResponse": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "type": {"type": "string"}, "updated_at": {"type": "string"}, "value": {"type": "string"}}}, "dto.BroadcastNotificationRequest": {"type": "object", "required": ["category", "content", "title"], "properties": {"action_url": {"type": "string", "maxLength": 500}, "category": {"type": "string", "maxLength": 50, "minLength": 1}, "content": {"type": "string", "minLength": 1}, "expires_at": {"type": "string"}, "priority": {"type": "integer", "maximum": 3, "minimum": 0}, "title": {"type": "string", "maxLength": 200, "minLength": 1}, "type": {"type": "string", "enum": ["info", "success", "warning", "error"]}}}, "dto.CreateMenuRequest": {"type": "object", "required": ["menu_type", "name", "title"], "properties": {"component": {"type": "string", "maxLength": 200}, "external_url": {"type": "string", "maxLength": 500}, "icon": {"type": "string", "maxLength": 50}, "is_enabled": {"type": "boolean"}, "is_visible": {"type": "boolean"}, "menu_type": {"type": "string", "enum": ["menu", "button", "link"]}, "name": {"type": "string", "maxLength": 100, "minLength": 2}, "parent_id": {"type": "integer"}, "path": {"type": "string", "maxLength": 200}, "permission": {"type": "string", "maxLength": 100}, "sort": {"type": "integer"}, "target": {"type": "string", "enum": ["_self", "_blank", "_parent", "_top"]}, "title": {"type": "string", "maxLength": 100, "minLength": 2}}}, "dto.CreateNotificationRequest": {"type": "object", "required": ["category", "content", "title"], "properties": {"action_url": {"type": "string", "maxLength": 500}, "category": {"type": "string", "maxLength": 50, "minLength": 1}, "content": {"type": "string", "minLength": 1}, "expires_at": {"type": "string"}, "is_global": {"type": "boolean"}, "priority": {"type": "integer", "maximum": 3, "minimum": 0}, "receiver_id": {"type": "integer"}, "title": {"type": "string", "maxLength": 200, "minLength": 1}, "type": {"type": "string", "enum": ["info", "success", "warning", "error"]}}}, "dto.CreatePermissionRequest": {"type": "object", "required": ["action", "name", "resource"], "properties": {"action": {"type": "string", "maxLength": 50, "minLength": 2}, "description": {"type": "string", "maxLength": 500}, "name": {"type": "string", "maxLength": 100, "minLength": 2}, "resource": {"type": "string", "maxLength": 50, "minLength": 2}}}, "dto.CreateRoleRequest": {"type": "object", "required": ["name"], "properties": {"description": {"type": "string", "maxLength": 500}, "is_active": {"description": "Pointer to distinguish between false and nil", "type": "boolean"}, "name": {"type": "string", "maxLength": 100, "minLength": 2}}}, "dto.CreateSettingRequest": {"type": "object", "required": ["category", "key", "type", "value"], "properties": {"category": {"type": "string", "maxLength": 50, "minLength": 2}, "description": {"type": "string", "maxLength": 500}, "is_editable": {"type": "boolean"}, "is_public": {"type": "boolean"}, "key": {"type": "string", "maxLength": 100, "minLength": 2}, "type": {"type": "string", "enum": ["string", "number", "boolean", "json"]}, "value": {"type": "string"}}}, "dto.CreateUserRequest": {"type": "object", "required": ["email", "first_name", "last_name", "password", "username"], "properties": {"email": {"type": "string"}, "first_name": {"type": "string", "maxLength": 50, "minLength": 1}, "is_active": {"description": "Pointer to distinguish between false and nil", "type": "boolean"}, "last_name": {"type": "string", "maxLength": 50, "minLength": 1}, "password": {"type": "string", "minLength": 8}, "username": {"type": "string", "maxLength": 50, "minLength": 3}}}, "dto.ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "details": {"type": "object", "additionalProperties": {"type": "string"}}, "error": {"type": "string"}, "message": {"type": "string"}}}, "dto.LoginRequest": {"type": "object", "required": ["identifier", "password"], "properties": {"identifier": {"description": "username or email", "type": "string"}, "password": {"type": "string"}}}, "dto.LoginResponse": {"type": "object", "properties": {"expires_at": {"type": "string"}, "token": {"type": "string"}, "user": {"$ref": "#/definitions/dto.UserInfo"}}}, "dto.MessageResponse": {"type": "object", "properties": {"message": {"type": "string"}}}, "dto.NotificationListResponse": {"type": "object", "properties": {"notifications": {"type": "array", "items": {"$ref": "#/definitions/dto.NotificationResponse"}}, "pagination": {"$ref": "#/definitions/dto.Pagination"}, "unread_only": {"type": "boolean"}}}, "dto.NotificationResponse": {"type": "object", "properties": {"action_url": {"type": "string"}, "category": {"type": "string"}, "content": {"type": "string"}, "created_at": {"type": "string"}, "expires_at": {"type": "string"}, "id": {"type": "integer"}, "is_global": {"type": "boolean"}, "is_read": {"type": "boolean"}, "priority": {"type": "integer"}, "receiver": {"$ref": "#/definitions/dto.UserInfo"}, "sender": {"$ref": "#/definitions/dto.UserInfo"}, "title": {"type": "string"}, "type": {"type": "string"}, "updated_at": {"type": "string"}}}, "dto.NotificationStatsResponse": {"type": "object", "properties": {"by_category": {"type": "object", "additionalProperties": {"type": "integer", "format": "int64"}}, "by_priority": {"type": "object", "additionalProperties": {"type": "integer", "format": "int64"}}, "by_type": {"type": "object", "additionalProperties": {"type": "integer", "format": "int64"}}, "read_count": {"type": "integer"}, "total_notifications": {"type": "integer"}, "unread_count": {"type": "integer"}}}, "dto.Pagination": {"type": "object", "properties": {"limit": {"type": "integer"}, "page": {"type": "integer"}, "total": {"type": "integer"}, "total_pages": {"type": "integer"}}}, "dto.PermissionResponse": {"type": "object", "properties": {"action": {"type": "string"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "resource": {"type": "string"}, "updated_at": {"type": "string"}}}, "dto.RefreshTokenRequest": {"type": "object", "required": ["token"], "properties": {"token": {"type": "string"}}}, "dto.RegisterRequest": {"type": "object", "required": ["email", "first_name", "last_name", "password", "username"], "properties": {"email": {"type": "string"}, "first_name": {"type": "string", "maxLength": 50, "minLength": 1}, "last_name": {"type": "string", "maxLength": 50, "minLength": 1}, "password": {"type": "string", "minLength": 8}, "username": {"type": "string", "maxLength": 50, "minLength": 3}}}, "dto.RoleListResponse": {"type": "object", "properties": {"pagination": {"$ref": "#/definitions/dto.Pagination"}, "roles": {"type": "array", "items": {"$ref": "#/definitions/dto.RoleResponse"}}}}, "dto.RoleResponse": {"type": "object", "properties": {"created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "is_active": {"type": "boolean"}, "name": {"type": "string"}, "permissions": {"type": "array", "items": {"$ref": "#/definitions/dto.PermissionResponse"}}, "updated_at": {"type": "string"}}}, "dto.UpdateMenuRequest": {"type": "object", "required": ["menu_type", "name", "title"], "properties": {"component": {"type": "string", "maxLength": 200}, "external_url": {"type": "string", "maxLength": 500}, "icon": {"type": "string", "maxLength": 50}, "is_enabled": {"type": "boolean"}, "is_visible": {"type": "boolean"}, "menu_type": {"type": "string", "enum": ["menu", "button", "link"]}, "name": {"type": "string", "maxLength": 100, "minLength": 2}, "parent_id": {"type": "integer"}, "path": {"type": "string", "maxLength": 200}, "permission": {"type": "string", "maxLength": 100}, "sort": {"type": "integer"}, "target": {"type": "string", "enum": ["_self", "_blank", "_parent", "_top"]}, "title": {"type": "string", "maxLength": 100, "minLength": 2}}}, "dto.UpdatePermissionRequest": {"type": "object", "required": ["action", "name", "resource"], "properties": {"action": {"type": "string", "maxLength": 50, "minLength": 2}, "description": {"type": "string", "maxLength": 500}, "name": {"type": "string", "maxLength": 100, "minLength": 2}, "resource": {"type": "string", "maxLength": 50, "minLength": 2}}}, "dto.UpdateRoleRequest": {"type": "object", "required": ["name"], "properties": {"description": {"type": "string", "maxLength": 500}, "is_active": {"description": "Pointer to distinguish between false and nil", "type": "boolean"}, "name": {"type": "string", "maxLength": 100, "minLength": 2}}}, "dto.UpdateSettingRequest": {"type": "object", "required": ["category", "type", "value"], "properties": {"category": {"type": "string", "maxLength": 50, "minLength": 2}, "description": {"type": "string", "maxLength": 500}, "is_editable": {"type": "boolean"}, "is_public": {"type": "boolean"}, "type": {"type": "string", "enum": ["string", "number", "boolean", "json"]}, "value": {"type": "string"}}}, "dto.UpdateUserRequest": {"type": "object", "properties": {"email": {"type": "string"}, "first_name": {"type": "string", "maxLength": 50, "minLength": 1}, "is_active": {"description": "Pointer to distinguish between false and nil", "type": "boolean"}, "last_name": {"type": "string", "maxLength": 50, "minLength": 1}, "username": {"type": "string", "maxLength": 50, "minLength": 3}}}, "dto.UserInfo": {"type": "object", "properties": {"created_at": {"type": "string"}, "email": {"type": "string"}, "first_name": {"type": "string"}, "id": {"type": "integer"}, "is_active": {"type": "boolean"}, "last_name": {"type": "string"}, "updated_at": {"type": "string"}, "username": {"type": "string"}}}, "dto.UserListResponse": {"type": "object", "properties": {"pagination": {"$ref": "#/definitions/dto.Pagination"}, "users": {"type": "array", "items": {"$ref": "#/definitions/dto.UserResponse"}}}}, "dto.UserResponse": {"type": "object", "properties": {"attributes": {"type": "array", "items": {"$ref": "#/definitions/dto.AttributeResponse"}}, "created_at": {"type": "string"}, "email": {"type": "string"}, "first_name": {"type": "string"}, "id": {"type": "integer"}, "is_active": {"type": "boolean"}, "last_name": {"type": "string"}, "roles": {"type": "array", "items": {"$ref": "#/definitions/dto.RoleResponse"}}, "updated_at": {"type": "string"}, "username": {"type": "string"}}}, "models.Menu": {"type": "object", "properties": {"children": {"type": "array", "items": {"$ref": "#/definitions/models.Menu"}}, "component": {"description": "Vue component path", "type": "string"}, "created_at": {"type": "string"}, "external_url": {"description": "External link URL", "type": "string"}, "icon": {"description": "Icon class/name", "type": "string"}, "id": {"type": "integer"}, "is_enabled": {"description": "Whether enabled", "type": "boolean"}, "is_visible": {"description": "Whether visible in menu", "type": "boolean"}, "menu_type": {"description": "menu, button, link", "type": "string"}, "name": {"type": "string"}, "parent": {"description": "Relationships", "allOf": [{"$ref": "#/definitions/models.Menu"}]}, "parent_id": {"description": "Parent menu ID", "type": "integer"}, "path": {"description": "Route path", "type": "string"}, "permission": {"description": "Required permission", "type": "string"}, "sort": {"description": "Sort order", "type": "integer"}, "target": {"description": "Link target", "type": "string"}, "title": {"description": "Display title", "type": "string"}, "updated_at": {"type": "string"}}}, "models.SystemSetting": {"type": "object", "properties": {"category": {"description": "general, email, security, etc.", "type": "string"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "is_editable": {"description": "whether this setting can be modified", "type": "boolean"}, "is_public": {"description": "whether non-admin users can read this setting", "type": "boolean"}, "key": {"type": "string"}, "type": {"description": "string, number, boolean, json", "type": "string"}, "updated_at": {"type": "string"}, "value": {"type": "string"}}}}, "securityDefinitions": {"BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}