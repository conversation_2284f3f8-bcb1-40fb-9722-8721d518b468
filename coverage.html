
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>services: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">xiaoxingcloud.com/admin/internal/services/abac.go (67.1%)</option>
				
				<option value="file1">xiaoxingcloud.com/admin/internal/services/audit.go (71.3%)</option>
				
				<option value="file2">xiaoxingcloud.com/admin/internal/services/menu.go (61.9%)</option>
				
				<option value="file3">xiaoxingcloud.com/admin/internal/services/notification.go (70.3%)</option>
				
				<option value="file4">xiaoxingcloud.com/admin/internal/services/permission.go (63.3%)</option>
				
				<option value="file5">xiaoxingcloud.com/admin/internal/services/rbac.go (75.0%)</option>
				
				<option value="file6">xiaoxingcloud.com/admin/internal/services/role.go (70.3%)</option>
				
				<option value="file7">xiaoxingcloud.com/admin/internal/services/setting.go (68.7%)</option>
				
				<option value="file8">xiaoxingcloud.com/admin/internal/services/user.go (72.4%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package services

import (
        "encoding/json"
        "fmt"
        "strconv"
        "strings"
        "time"

        "gorm.io/gorm"
        "xiaoxingcloud.com/admin/internal/models"
)

// ABACServiceInterface defines the interface for ABAC service
type ABACServiceInterface interface {
        CheckAccess(userID uint, resource, action string, context map[string]any) (bool, error)
        GetUserAttributes(userID uint) ([]models.Attribute, error)
        SetUserAttribute(userID uint, name, value, attrType string) error
        GetPolicies(resource, action string) ([]models.Policy, error)
        CreatePolicy(name, description, resource, action, effect string, conditions PolicyRule) error
}

type ABACService struct {
        db *gorm.DB
}

// Compile-time interface compliance check
var _ ABACServiceInterface = (*ABACService)(nil)

type PolicyCondition struct {
        Attribute string `json:"attribute"`
        Operator  string `json:"operator"` // eq, ne, contains
        Value     any    `json:"value"`
}

type PolicyRule struct {
        Conditions []PolicyCondition `json:"conditions"`
        Logic      string            `json:"logic"` // and, or
}

func NewABACService(db *gorm.DB) *ABACService <span class="cov8" title="1">{
        return &amp;ABACService{db: db}
}</span>

// CheckAccess evaluates ABAC policies for a user's access to a resource
func (a *ABACService) CheckAccess(userID uint, resource, action string, context map[string]any) (bool, error) <span class="cov8" title="1">{
        // Get user attributes
        userAttributes, err := a.GetUserAttributes(userID)
        if err != nil </span><span class="cov0" title="0">{
                return false, fmt.Errorf("failed to get user attributes: %w", err)
        }</span>

        // Convert user attributes to map for easier access
        <span class="cov8" title="1">attributeMap := make(map[string]any)
        for _, attr := range userAttributes </span><span class="cov8" title="1">{
                attributeMap[attr.Name] = a.convertAttributeValue(attr.Value, attr.Type)
        }</span>

        // Add context attributes
        <span class="cov8" title="1">for key, value := range context </span><span class="cov0" title="0">{
                attributeMap[key] = value
        }</span>

        // Get applicable policies
        <span class="cov8" title="1">policies, err := a.GetPolicies(resource, action)
        if err != nil </span><span class="cov0" title="0">{
                return false, fmt.Errorf("failed to get policies: %w", err)
        }</span>

        // Evaluate policies
        <span class="cov8" title="1">for _, policy := range policies </span><span class="cov8" title="1">{
                if !policy.IsActive </span><span class="cov0" title="0">{
                        continue</span>
                }

                <span class="cov8" title="1">allowed, err := a.evaluatePolicy(policy, attributeMap)
                if err != nil </span><span class="cov0" title="0">{
                        continue</span> // Skip invalid policies
                }

                <span class="cov8" title="1">if policy.Effect == "deny" &amp;&amp; allowed </span><span class="cov0" title="0">{
                        return false, nil // Explicit deny
                }</span>

                <span class="cov8" title="1">if policy.Effect == "allow" &amp;&amp; allowed </span><span class="cov8" title="1">{
                        return true, nil // Explicit allow
                }</span>
        }

        <span class="cov8" title="1">return false, nil</span> // Default deny
}

// GetUserAttributes returns all attributes for a user
func (a *ABACService) GetUserAttributes(userID uint) ([]models.Attribute, error) <span class="cov8" title="1">{
        var attributes []models.Attribute
        err := a.db.Where("user_id = ?", userID).Find(&amp;attributes).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get user attributes: %w", err)
        }</span>

        <span class="cov8" title="1">return attributes, nil</span>
}

// SetUserAttribute sets an attribute for a user
func (a *ABACService) SetUserAttribute(userID uint, name, value, attrType string) error <span class="cov8" title="1">{
        var attribute models.Attribute
        err := a.db.Where("user_id = ? AND name = ?", userID, name).First(&amp;attribute).Error

        if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                // Create new attribute
                attribute = models.Attribute{
                        UserID: userID,
                        Name:   name,
                        Value:  value,
                        Type:   attrType,
                }
                return a.db.Create(&amp;attribute).Error
        }</span> else<span class="cov8" title="1"> if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to check existing attribute: %w", err)
        }</span>

        // Update existing attribute
        <span class="cov8" title="1">attribute.Value = value
        attribute.Type = attrType
        return a.db.Save(&amp;attribute).Error</span>
}

// GetPolicies returns policies for a resource and action
func (a *ABACService) GetPolicies(resource, action string) ([]models.Policy, error) <span class="cov8" title="1">{
        var policies []models.Policy
        err := a.db.Where("resource = ? AND action = ? AND is_active = ?", resource, action, true).Find(&amp;policies).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get policies: %w", err)
        }</span>

        <span class="cov8" title="1">return policies, nil</span>
}

// CreatePolicy creates a new ABAC policy
func (a *ABACService) CreatePolicy(name, description, resource, action, effect string, conditions PolicyRule) error <span class="cov8" title="1">{
        conditionsJSON, err := json.Marshal(conditions)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to marshal conditions: %w", err)
        }</span>

        <span class="cov8" title="1">policy := models.Policy{
                Name:        name,
                Description: description,
                Resource:    resource,
                Action:      action,
                Effect:      effect,
                Conditions:  string(conditionsJSON),
                IsActive:    true,
        }

        return a.db.Create(&amp;policy).Error</span>
}

// evaluatePolicy evaluates a single policy against user attributes
func (a *ABACService) evaluatePolicy(policy models.Policy, attributes map[string]any) (bool, error) <span class="cov8" title="1">{
        var rule PolicyRule
        if err := json.Unmarshal([]byte(policy.Conditions), &amp;rule); err != nil </span><span class="cov0" title="0">{
                return false, fmt.Errorf("failed to unmarshal policy conditions: %w", err)
        }</span>

        <span class="cov8" title="1">results := make([]bool, len(rule.Conditions))

        for i, condition := range rule.Conditions </span><span class="cov8" title="1">{
                result, err := a.evaluateCondition(condition, attributes)
                if err != nil </span><span class="cov0" title="0">{
                        return false, err
                }</span>
                <span class="cov8" title="1">results[i] = result</span>
        }

        // Apply logic (AND/OR)
        <span class="cov8" title="1">if rule.Logic == "or" </span><span class="cov0" title="0">{
                for _, result := range results </span><span class="cov0" title="0">{
                        if result </span><span class="cov0" title="0">{
                                return true, nil
                        }</span>
                }
                <span class="cov0" title="0">return false, nil</span>
        } else<span class="cov8" title="1"> { // Default to AND
                for _, result := range results </span><span class="cov8" title="1">{
                        if !result </span><span class="cov0" title="0">{
                                return false, nil
                        }</span>
                }
                <span class="cov8" title="1">return true, nil</span>
        }
}

// evaluateCondition evaluates a single condition
func (a *ABACService) evaluateCondition(condition PolicyCondition, attributes map[string]any) (bool, error) <span class="cov8" title="1">{
        attrValue, exists := attributes[condition.Attribute]
        if !exists </span><span class="cov0" title="0">{
                return false, nil
        }</span>

        <span class="cov8" title="1">switch condition.Operator </span>{
        case "eq":<span class="cov8" title="1">
                return fmt.Sprintf("%v", attrValue) == fmt.Sprintf("%v", condition.Value), nil</span>
        case "ne":<span class="cov0" title="0">
                return fmt.Sprintf("%v", attrValue) != fmt.Sprintf("%v", condition.Value), nil</span>
        case "contains":<span class="cov0" title="0">
                return strings.Contains(fmt.Sprintf("%v", attrValue), fmt.Sprintf("%v", condition.Value)), nil</span>
        default:<span class="cov0" title="0">
                return false, fmt.Errorf("unsupported operator: %s", condition.Operator)</span>
        }
}

// Helper functions for value comparison and conversion
func (a *ABACService) convertAttributeValue(value, attrType string) any <span class="cov8" title="1">{
        switch attrType </span>{
        case "number":<span class="cov0" title="0">
                if num, err := strconv.ParseFloat(value, 64); err == nil </span><span class="cov0" title="0">{
                        return num
                }</span>
        case "boolean":<span class="cov0" title="0">
                if b, err := strconv.ParseBool(value); err == nil </span><span class="cov0" title="0">{
                        return b
                }</span>
        case "date":<span class="cov0" title="0">
                if t, err := time.Parse(time.RFC3339, value); err == nil </span><span class="cov0" title="0">{
                        return t
                }</span>
        }
        <span class="cov8" title="1">return value</span> // Default to string
}
</pre>
		
		<pre class="file" id="file1" style="display: none">package services

import (
        "encoding/json"
        "fmt"
        "time"

        "gorm.io/gorm"
        "xiaoxingcloud.com/admin/internal/models"
)

// AuditServiceInterface defines the interface for audit service
type AuditServiceInterface interface {
        GetAuditLogs(filter AuditLogFilter) ([]models.AuditLog, int64, error)
        GetUserAuditLogs(userID uint, page, limit int) ([]models.AuditLog, int64, error)
        GetAuditStats() (*AuditStats, error)
        CleanupOldLogs(daysToKeep int) (int64, error)
}

type AuditService struct {
        db *gorm.DB
}

// Compile-time interface compliance check
var _ AuditServiceInterface = (*AuditService)(nil)

// LogActionParams contains parameters for logging an action
type LogActionParams struct {
        UserID     *uint
        Username   string
        Action     string
        Resource   string
        ResourceID *uint
        Method     string
        Path       string
        IPAddress  string
        UserAgent  string
        Status     string
        Message    string
        Duration   int64
}

// AuditLogFilter contains filter parameters for querying audit logs
type AuditLogFilter struct {
        Page       int
        Limit      int
        Username   string
        Action     string
        Resource   string
        Status     string
        IPAddress  string
        StartDate  *time.Time
        EndDate    *time.Time
        ResourceID *uint
}

// AuditStats contains audit statistics
type AuditStats struct {
        TotalLogs         int64           `json:"total_logs"`
        LogsLast24h       int64           `json:"logs_last_24h"`
        SuccessfulActions int64           `json:"successful_actions"`
        FailedActions     int64           `json:"failed_actions"`
        UniqueUsers       int64           `json:"unique_users"`
        TopActions        []ActionCount   `json:"top_actions"`
        TopResources      []ResourceCount `json:"top_resources"`
}

type ActionCount struct {
        Action string `json:"action"`
        Count  int64  `json:"count"`
}

type ResourceCount struct {
        Resource string `json:"resource"`
        Count    int64  `json:"count"`
}

// NewAuditService creates a new audit service
func NewAuditService(db *gorm.DB) *AuditService <span class="cov8" title="1">{
        return &amp;AuditService{db: db}
}</span>

// LogAction logs a user action
func (a *AuditService) LogAction(params LogActionParams) error <span class="cov8" title="1">{
        log := &amp;models.AuditLog{
                UserID:     params.UserID,
                Username:   params.Username,
                Action:     params.Action,
                Resource:   params.Resource,
                ResourceID: params.ResourceID,
                Method:     params.Method,
                Path:       params.Path,
                IPAddress:  params.IPAddress,
                UserAgent:  params.UserAgent,
                Status:     params.Status,
                Message:    params.Message,
                Duration:   params.Duration,
                CreatedAt:  time.Now(),
        }

        if err := a.db.Create(log).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to create audit log: %w", err)
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// LogActionWithValues logs a user action with old and new values
func (a *AuditService) LogActionWithValues(params LogActionParams, oldValues, newValues interface{}) error <span class="cov8" title="1">{
        var oldJSON, newJSON string

        if oldValues != nil </span><span class="cov8" title="1">{
                oldBytes, err := json.Marshal(oldValues)
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to marshal old values: %w", err)
                }</span>
                <span class="cov8" title="1">oldJSON = string(oldBytes)</span>
        }

        <span class="cov8" title="1">if newValues != nil </span><span class="cov8" title="1">{
                newBytes, err := json.Marshal(newValues)
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to marshal new values: %w", err)
                }</span>
                <span class="cov8" title="1">newJSON = string(newBytes)</span>
        }

        <span class="cov8" title="1">log := &amp;models.AuditLog{
                UserID:     params.UserID,
                Username:   params.Username,
                Action:     params.Action,
                Resource:   params.Resource,
                ResourceID: params.ResourceID,
                Method:     params.Method,
                Path:       params.Path,
                IPAddress:  params.IPAddress,
                UserAgent:  params.UserAgent,
                Status:     params.Status,
                Message:    params.Message,
                OldValues:  oldJSON,
                NewValues:  newJSON,
                Duration:   params.Duration,
                CreatedAt:  time.Now(),
        }

        if err := a.db.Create(log).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to create audit log: %w", err)
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// LogSystemAction logs a system action (no user)
func (a *AuditService) LogSystemAction(action, resource, message string) error <span class="cov8" title="1">{
        return a.LogAction(LogActionParams{
                Username: "system",
                Action:   action,
                Resource: resource,
                Status:   "success",
                Message:  message,
        })
}</span>

// LogLoginAttempt logs a login attempt
func (a *AuditService) LogLoginAttempt(username, ipAddress, userAgent string, success bool, message string) error <span class="cov8" title="1">{
        status := "success"
        if !success </span><span class="cov8" title="1">{
                status = "failed"
        }</span>

        <span class="cov8" title="1">return a.LogAction(LogActionParams{
                Username:  username,
                Action:    "login",
                Resource:  "auth",
                IPAddress: ipAddress,
                UserAgent: userAgent,
                Status:    status,
                Message:   message,
        })</span>
}

// GetAuditLogs retrieves audit logs with filtering and pagination
func (a *AuditService) GetAuditLogs(filter AuditLogFilter) ([]models.AuditLog, int64, error) <span class="cov8" title="1">{
        var logs []models.AuditLog
        var total int64

        query := a.db.Model(&amp;models.AuditLog{})

        // Apply filters
        if filter.Username != "" </span><span class="cov8" title="1">{
                query = query.Where("username = ?", filter.Username)
        }</span>
        <span class="cov8" title="1">if filter.Action != "" </span><span class="cov8" title="1">{
                query = query.Where("action = ?", filter.Action)
        }</span>
        <span class="cov8" title="1">if filter.Resource != "" </span><span class="cov8" title="1">{
                query = query.Where("resource = ?", filter.Resource)
        }</span>
        <span class="cov8" title="1">if filter.Status != "" </span><span class="cov0" title="0">{
                query = query.Where("status = ?", filter.Status)
        }</span>
        <span class="cov8" title="1">if filter.IPAddress != "" </span><span class="cov0" title="0">{
                query = query.Where("ip_address = ?", filter.IPAddress)
        }</span>
        <span class="cov8" title="1">if filter.ResourceID != nil </span><span class="cov0" title="0">{
                query = query.Where("resource_id = ?", *filter.ResourceID)
        }</span>
        <span class="cov8" title="1">if filter.StartDate != nil </span><span class="cov0" title="0">{
                query = query.Where("created_at &gt;= ?", *filter.StartDate)
        }</span>
        <span class="cov8" title="1">if filter.EndDate != nil </span><span class="cov0" title="0">{
                query = query.Where("created_at &lt;= ?", *filter.EndDate)
        }</span>

        // Count total
        <span class="cov8" title="1">if err := query.Count(&amp;total).Error; err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to count audit logs: %w", err)
        }</span>

        // Get logs with pagination
        <span class="cov8" title="1">offset := (filter.Page - 1) * filter.Limit
        err := query.Preload("User").
                Order("created_at DESC").
                Offset(offset).
                Limit(filter.Limit).
                Find(&amp;logs).Error

        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to get audit logs: %w", err)
        }</span>

        <span class="cov8" title="1">return logs, total, nil</span>
}

// GetUserAuditLogs retrieves audit logs for a specific user
func (a *AuditService) GetUserAuditLogs(userID uint, page, limit int) ([]models.AuditLog, int64, error) <span class="cov8" title="1">{
        var logs []models.AuditLog
        var total int64

        query := a.db.Model(&amp;models.AuditLog{}).Where("user_id = ?", userID)

        // Count total
        if err := query.Count(&amp;total).Error; err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to count user audit logs: %w", err)
        }</span>

        // Get logs with pagination
        <span class="cov8" title="1">offset := (page - 1) * limit
        err := query.Preload("User").
                Order("created_at DESC").
                Offset(offset).
                Limit(limit).
                Find(&amp;logs).Error

        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to get user audit logs: %w", err)
        }</span>

        <span class="cov8" title="1">return logs, total, nil</span>
}

// GetAuditStats retrieves audit statistics
func (a *AuditService) GetAuditStats() (*AuditStats, error) <span class="cov8" title="1">{
        stats := &amp;AuditStats{}

        // Total logs
        if err := a.db.Model(&amp;models.AuditLog{}).Count(&amp;stats.TotalLogs).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to count total logs: %w", err)
        }</span>

        // Logs in last 24 hours
        <span class="cov8" title="1">last24h := time.Now().Add(-24 * time.Hour)
        if err := a.db.Model(&amp;models.AuditLog{}).Where("created_at &gt;= ?", last24h).Count(&amp;stats.LogsLast24h).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to count logs last 24h: %w", err)
        }</span>

        // Successful actions
        <span class="cov8" title="1">if err := a.db.Model(&amp;models.AuditLog{}).Where("status = ?", "success").Count(&amp;stats.SuccessfulActions).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to count successful actions: %w", err)
        }</span>

        // Failed actions
        <span class="cov8" title="1">if err := a.db.Model(&amp;models.AuditLog{}).Where("status = ?", "failed").Count(&amp;stats.FailedActions).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to count failed actions: %w", err)
        }</span>

        // Unique users
        <span class="cov8" title="1">if err := a.db.Model(&amp;models.AuditLog{}).Distinct("username").Count(&amp;stats.UniqueUsers).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to count unique users: %w", err)
        }</span>

        // Top actions
        <span class="cov8" title="1">var topActions []ActionCount
        if err := a.db.Model(&amp;models.AuditLog{}).
                Select("action, COUNT(*) as count").
                Group("action").
                Order("count DESC").
                Limit(10).
                Find(&amp;topActions).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get top actions: %w", err)
        }</span>
        <span class="cov8" title="1">stats.TopActions = topActions

        // Top resources
        var topResources []ResourceCount
        if err := a.db.Model(&amp;models.AuditLog{}).
                Select("resource, COUNT(*) as count").
                Group("resource").
                Order("count DESC").
                Limit(10).
                Find(&amp;topResources).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get top resources: %w", err)
        }</span>
        <span class="cov8" title="1">stats.TopResources = topResources

        return stats, nil</span>
}

// CleanupOldLogs removes audit logs older than the specified number of days
func (a *AuditService) CleanupOldLogs(daysToKeep int) (int64, error) <span class="cov0" title="0">{
        cutoffDate := time.Now().AddDate(0, 0, -daysToKeep)

        result := a.db.Where("created_at &lt; ?", cutoffDate).Delete(&amp;models.AuditLog{})
        if result.Error != nil </span><span class="cov0" title="0">{
                return 0, fmt.Errorf("failed to cleanup old logs: %w", result.Error)
        }</span>

        <span class="cov0" title="0">return result.RowsAffected, nil</span>
}
</pre>
		
		<pre class="file" id="file2" style="display: none">package services

import (
        "errors"
        "fmt"

        "gorm.io/gorm"
        "xiaoxingcloud.com/admin/internal/models"
)

// MenuServiceInterface defines the interface for menu service
type MenuServiceInterface interface {
        CreateMenu(input CreateMenuInput) (*models.Menu, error)
        GetMenuByID(id uint) (*models.Menu, error)
        GetAllMenus() ([]models.Menu, error)
        GetMenuTree() ([]models.Menu, error)
        GetVisibleMenuTree() ([]models.Menu, error)
        GetMenusByPermission(permissions []string) ([]models.Menu, error)
        UpdateMenu(id uint, input UpdateMenuInput) (*models.Menu, error)
        DeleteMenu(id uint) error
        InitializeDefaultMenus() error
}

type MenuService struct {
        db *gorm.DB
}

// Compile-time interface compliance check
var _ MenuServiceInterface = (*MenuService)(nil)

// CreateMenuInput represents input for creating a menu
type CreateMenuInput struct {
        Name        string `json:"name"`
        Title       string `json:"title"`
        Icon        string `json:"icon"`
        Path        string `json:"path"`
        Component   string `json:"component"`
        ParentID    *uint  `json:"parent_id"`
        Sort        int    `json:"sort"`
        IsVisible   bool   `json:"is_visible"`
        IsEnabled   bool   `json:"is_enabled"`
        Permission  string `json:"permission"`
        MenuType    string `json:"menu_type"`
        ExternalURL string `json:"external_url"`
        Target      string `json:"target"`
}

// UpdateMenuInput represents input for updating a menu
type UpdateMenuInput struct {
        Name        string `json:"name"`
        Title       string `json:"title"`
        Icon        string `json:"icon"`
        Path        string `json:"path"`
        Component   string `json:"component"`
        ParentID    *uint  `json:"parent_id"`
        Sort        int    `json:"sort"`
        IsVisible   bool   `json:"is_visible"`
        IsEnabled   bool   `json:"is_enabled"`
        Permission  string `json:"permission"`
        MenuType    string `json:"menu_type"`
        ExternalURL string `json:"external_url"`
        Target      string `json:"target"`
}

// NewMenuService creates a new menu service
func NewMenuService(db *gorm.DB) *MenuService <span class="cov8" title="1">{
        return &amp;MenuService{db: db}
}</span>

// CreateMenu creates a new menu item
func (m *MenuService) CreateMenu(input CreateMenuInput) (*models.Menu, error) <span class="cov8" title="1">{
        // Validate parent menu exists if ParentID is provided
        if input.ParentID != nil </span><span class="cov8" title="1">{
                var parentMenu models.Menu
                err := m.db.First(&amp;parentMenu, *input.ParentID).Error
                if err != nil </span><span class="cov0" title="0">{
                        if err == gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                                return nil, errors.New("parent menu not found")
                        }</span>
                        <span class="cov0" title="0">return nil, fmt.Errorf("failed to check parent menu: %w", err)</span>
                }
        }

        // Check if menu name already exists
        <span class="cov8" title="1">var existingMenu models.Menu
        err := m.db.Where("name = ?", input.Name).First(&amp;existingMenu).Error
        if err == nil </span><span class="cov0" title="0">{
                return nil, errors.New("menu name already exists")
        }</span>
        <span class="cov8" title="1">if err != gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to check existing menu: %w", err)
        }</span>

        // Create new menu
        <span class="cov8" title="1">menu := &amp;models.Menu{
                Name:        input.Name,
                Title:       input.Title,
                Icon:        input.Icon,
                Path:        input.Path,
                Component:   input.Component,
                ParentID:    input.ParentID,
                Sort:        input.Sort,
                IsVisible:   input.IsVisible,
                IsEnabled:   input.IsEnabled,
                Permission:  input.Permission,
                MenuType:    input.MenuType,
                ExternalURL: input.ExternalURL,
                Target:      input.Target,
        }

        if err := m.db.Create(menu).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to create menu: %w", err)
        }</span>

        <span class="cov8" title="1">return menu, nil</span>
}

// GetMenuByID gets a menu by ID
func (m *MenuService) GetMenuByID(id uint) (*models.Menu, error) <span class="cov8" title="1">{
        var menu models.Menu
        err := m.db.Preload("Parent").Preload("Children", func(db *gorm.DB) *gorm.DB </span><span class="cov8" title="1">{
                return db.Order("sort ASC")
        }</span>).First(&amp;menu, id).Error
        <span class="cov8" title="1">if err != nil </span><span class="cov8" title="1">{
                if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        return nil, errors.New("menu not found")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("failed to get menu: %w", err)</span>
        }

        <span class="cov8" title="1">return &amp;menu, nil</span>
}

// GetAllMenus gets all menus ordered by sort
func (m *MenuService) GetAllMenus() ([]models.Menu, error) <span class="cov8" title="1">{
        var menus []models.Menu
        err := m.db.Preload("Parent").Order("sort ASC, id ASC").Find(&amp;menus).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get all menus: %w", err)
        }</span>

        <span class="cov8" title="1">return menus, nil</span>
}

// GetMenuTree gets all menus in tree structure
func (m *MenuService) GetMenuTree() ([]models.Menu, error) <span class="cov8" title="1">{
        var menus []models.Menu
        err := m.db.Where("parent_id IS NULL").
                Preload("Children", func(db *gorm.DB) *gorm.DB </span><span class="cov8" title="1">{
                        return db.Order("sort ASC")
                }</span>).
                Order("sort ASC").
                Find(&amp;menus).Error
        <span class="cov8" title="1">if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get menu tree: %w", err)
        }</span>

        <span class="cov8" title="1">return menus, nil</span>
}

// GetVisibleMenuTree gets visible menus in tree structure
func (m *MenuService) GetVisibleMenuTree() ([]models.Menu, error) <span class="cov0" title="0">{
        var menus []models.Menu
        err := m.db.Where("parent_id IS NULL AND is_visible = ? AND is_enabled = ?", true, true).
                Preload("Children", func(db *gorm.DB) *gorm.DB </span><span class="cov0" title="0">{
                        return db.Where("is_visible = ? AND is_enabled = ?", true, true).Order("sort ASC")
                }</span>).
                Order("sort ASC").
                Find(&amp;menus).Error
        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get visible menu tree: %w", err)
        }</span>

        <span class="cov0" title="0">return menus, nil</span>
}

// GetMenusByPermission gets menus that user has permission to access
func (m *MenuService) GetMenusByPermission(permissions []string) ([]models.Menu, error) <span class="cov0" title="0">{
        var menus []models.Menu
        query := m.db.Where("is_visible = ? AND is_enabled = ?", true, true)

        if len(permissions) &gt; 0 </span><span class="cov0" title="0">{
                // Include menus with no permission requirement or with matching permissions
                query = query.Where("permission = '' OR permission IS NULL OR permission IN ?", permissions)
        }</span> else<span class="cov0" title="0"> {
                // Only include menus with no permission requirement
                query = query.Where("permission = '' OR permission IS NULL")
        }</span>

        <span class="cov0" title="0">err := query.Where("parent_id IS NULL").
                Preload("Children", func(db *gorm.DB) *gorm.DB </span><span class="cov0" title="0">{
                        childQuery := db.Where("is_visible = ? AND is_enabled = ?", true, true)
                        if len(permissions) &gt; 0 </span><span class="cov0" title="0">{
                                childQuery = childQuery.Where("permission = '' OR permission IS NULL OR permission IN ?", permissions)
                        }</span> else<span class="cov0" title="0"> {
                                childQuery = childQuery.Where("permission = '' OR permission IS NULL")
                        }</span>
                        <span class="cov0" title="0">return childQuery.Order("sort ASC")</span>
                }).
                Order("sort ASC").
                Find(&amp;menus).Error

        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get menus by permission: %w", err)
        }</span>

        <span class="cov0" title="0">return menus, nil</span>
}

// UpdateMenu updates a menu
func (m *MenuService) UpdateMenu(id uint, input UpdateMenuInput) (*models.Menu, error) <span class="cov8" title="1">{
        var menu models.Menu
        err := m.db.First(&amp;menu, id).Error
        if err != nil </span><span class="cov8" title="1">{
                if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        return nil, errors.New("menu not found")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("failed to find menu: %w", err)</span>
        }

        // Validate parent menu exists if ParentID is provided
        <span class="cov8" title="1">if input.ParentID != nil </span><span class="cov0" title="0">{
                var parentMenu models.Menu
                err := m.db.First(&amp;parentMenu, *input.ParentID).Error
                if err != nil </span><span class="cov0" title="0">{
                        if err == gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                                return nil, errors.New("parent menu not found")
                        }</span>
                        <span class="cov0" title="0">return nil, fmt.Errorf("failed to check parent menu: %w", err)</span>
                }

                // Prevent circular reference
                <span class="cov0" title="0">if *input.ParentID == id </span><span class="cov0" title="0">{
                        return nil, errors.New("menu cannot be its own parent")
                }</span>
        }

        // Check if new name conflicts with existing menu (if name is being changed)
        <span class="cov8" title="1">if menu.Name != input.Name </span><span class="cov8" title="1">{
                var existingMenu models.Menu
                err := m.db.Where("name = ? AND id != ?", input.Name, id).First(&amp;existingMenu).Error
                if err == nil </span><span class="cov0" title="0">{
                        return nil, errors.New("menu name already exists")
                }</span>
                <span class="cov8" title="1">if err != gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("failed to check existing menu: %w", err)
                }</span>
        }

        // Update menu
        <span class="cov8" title="1">menu.Name = input.Name
        menu.Title = input.Title
        menu.Icon = input.Icon
        menu.Path = input.Path
        menu.Component = input.Component
        menu.ParentID = input.ParentID
        menu.Sort = input.Sort
        menu.IsVisible = input.IsVisible
        menu.IsEnabled = input.IsEnabled
        menu.Permission = input.Permission
        menu.MenuType = input.MenuType
        menu.ExternalURL = input.ExternalURL
        menu.Target = input.Target

        if err := m.db.Save(&amp;menu).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to update menu: %w", err)
        }</span>

        <span class="cov8" title="1">return &amp;menu, nil</span>
}

// DeleteMenu soft deletes a menu
func (m *MenuService) DeleteMenu(id uint) error <span class="cov8" title="1">{
        var menu models.Menu
        err := m.db.First(&amp;menu, id).Error
        if err != nil </span><span class="cov8" title="1">{
                if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        return errors.New("menu not found")
                }</span>
                <span class="cov0" title="0">return fmt.Errorf("failed to find menu: %w", err)</span>
        }

        // Check if menu has children
        <span class="cov8" title="1">var childCount int64
        if err := m.db.Model(&amp;models.Menu{}).Where("parent_id = ?", id).Count(&amp;childCount).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to check menu children: %w", err)
        }</span>

        <span class="cov8" title="1">if childCount &gt; 0 </span><span class="cov8" title="1">{
                return errors.New("cannot delete menu with children")
        }</span>

        // Soft delete the menu
        <span class="cov8" title="1">if err := m.db.Delete(&amp;menu).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to delete menu: %w", err)
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// InitializeDefaultMenus creates default system menus
func (m *MenuService) InitializeDefaultMenus() error <span class="cov8" title="1">{
        defaultMenus := []CreateMenuInput{
                {
                        Name:       "dashboard",
                        Title:      "Dashboard",
                        Icon:       "dashboard",
                        Path:       "/dashboard",
                        Component:  "Dashboard",
                        Sort:       1,
                        IsVisible:  true,
                        IsEnabled:  true,
                        Permission: "",
                        MenuType:   "menu",
                },
                {
                        Name:       "system",
                        Title:      "System Management",
                        Icon:       "settings",
                        Path:       "/system",
                        Component:  "",
                        Sort:       2,
                        IsVisible:  true,
                        IsEnabled:  true,
                        Permission: "system.admin",
                        MenuType:   "menu",
                },
                {
                        Name:       "users",
                        Title:      "User Management",
                        Icon:       "users",
                        Path:       "/users",
                        Component:  "UserList",
                        Sort:       3,
                        IsVisible:  true,
                        IsEnabled:  true,
                        Permission: "users.read",
                        MenuType:   "menu",
                },
                {
                        Name:       "roles",
                        Title:      "Role Management",
                        Icon:       "user-check",
                        Path:       "/roles",
                        Component:  "RoleList",
                        Sort:       4,
                        IsVisible:  true,
                        IsEnabled:  true,
                        Permission: "roles.read",
                        MenuType:   "menu",
                },
                {
                        Name:       "permissions",
                        Title:      "Permission Management",
                        Icon:       "shield",
                        Path:       "/permissions",
                        Component:  "PermissionList",
                        Sort:       5,
                        IsVisible:  true,
                        IsEnabled:  true,
                        Permission: "permissions.read",
                        MenuType:   "menu",
                },
        }

        for _, menuInput := range defaultMenus </span><span class="cov8" title="1">{
                // Only create if doesn't exist
                var existingMenu models.Menu
                err := m.db.Where("name = ?", menuInput.Name).First(&amp;existingMenu).Error
                if err != nil &amp;&amp; err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        _, err = m.CreateMenu(menuInput)
                        if err != nil </span><span class="cov0" title="0">{
                                return fmt.Errorf("failed to create default menu %s: %w", menuInput.Name, err)
                        }</span>
                }
        }

        <span class="cov8" title="1">return nil</span>
}
</pre>
		
		<pre class="file" id="file3" style="display: none">package services

import (
        "errors"
        "fmt"
        "time"

        "gorm.io/gorm"
        "xiaoxingcloud.com/admin/internal/models"
)

// NotificationServiceInterface defines the interface for notification service
type NotificationServiceInterface interface {
        CreateNotification(input CreateNotificationInput) (*models.Notification, error)
        GetNotificationByID(id uint) (*models.Notification, error)
        GetUserNotifications(userID uint, page, limit int) ([]models.Notification, int64, error)
        GetUnreadNotifications(userID uint, page, limit int) ([]models.Notification, int64, error)
        MarkAsRead(notificationID, userID uint) error
        MarkAllAsRead(userID uint) (int64, error)
        DeleteNotification(notificationID, userID uint) error
        GetUserNotificationStats(userID uint) (*NotificationStats, error)
        CleanupExpiredNotifications() (int64, error)
        BroadcastNotification(title, content, notificationType, category string, priority int, actionURL string, expiresAt *time.Time) (*models.Notification, error)
}

type NotificationService struct {
        db *gorm.DB
}

// Compile-time interface compliance check
var _ NotificationServiceInterface = (*NotificationService)(nil)

// CreateNotificationInput represents input for creating a notification
type CreateNotificationInput struct {
        Title      string     `json:"title"`
        Content    string     `json:"content"`
        Type       string     `json:"type"`
        Category   string     `json:"category"`
        Priority   int        `json:"priority"`
        IsGlobal   bool       `json:"is_global"`
        SenderID   *uint      `json:"sender_id"`
        ReceiverID *uint      `json:"receiver_id"`
        ActionURL  string     `json:"action_url"`
        ExpiresAt  *time.Time `json:"expires_at"`
}

// NotificationStats represents notification statistics
type NotificationStats struct {
        TotalNotifications int64            `json:"total_notifications"`
        UnreadCount        int64            `json:"unread_count"`
        ReadCount          int64            `json:"read_count"`
        ByType             map[string]int64 `json:"by_type"`
        ByCategory         map[string]int64 `json:"by_category"`
        ByPriority         map[int]int64    `json:"by_priority"`
}

// NewNotificationService creates a new notification service
func NewNotificationService(db *gorm.DB) *NotificationService <span class="cov8" title="1">{
        return &amp;NotificationService{db: db}
}</span>

// CreateNotification creates a new notification
func (n *NotificationService) CreateNotification(input CreateNotificationInput) (*models.Notification, error) <span class="cov8" title="1">{
        // Validate input
        if input.Title == "" </span><span class="cov0" title="0">{
                return nil, errors.New("title is required")
        }</span>
        <span class="cov8" title="1">if input.Content == "" </span><span class="cov0" title="0">{
                return nil, errors.New("content is required")
        }</span>

        // For non-global notifications, receiver is required
        <span class="cov8" title="1">if !input.IsGlobal &amp;&amp; input.ReceiverID == nil </span><span class="cov0" title="0">{
                return nil, errors.New("receiver_id is required for non-global notifications")
        }</span>

        // Validate sender exists if provided
        <span class="cov8" title="1">if input.SenderID != nil </span><span class="cov8" title="1">{
                var sender models.User
                err := n.db.First(&amp;sender, *input.SenderID).Error
                if err != nil </span><span class="cov0" title="0">{
                        if err == gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                                return nil, errors.New("sender not found")
                        }</span>
                        <span class="cov0" title="0">return nil, fmt.Errorf("failed to check sender: %w", err)</span>
                }
        }

        // Validate receiver exists if provided
        <span class="cov8" title="1">if input.ReceiverID != nil </span><span class="cov8" title="1">{
                var receiver models.User
                err := n.db.First(&amp;receiver, *input.ReceiverID).Error
                if err != nil </span><span class="cov0" title="0">{
                        if err == gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                                return nil, errors.New("receiver not found")
                        }</span>
                        <span class="cov0" title="0">return nil, fmt.Errorf("failed to check receiver: %w", err)</span>
                }
        }

        // Set default values
        <span class="cov8" title="1">if input.Type == "" </span><span class="cov0" title="0">{
                input.Type = "info"
        }</span>
        <span class="cov8" title="1">if input.Category == "" </span><span class="cov0" title="0">{
                input.Category = "general"
        }</span>

        // Create notification
        <span class="cov8" title="1">notification := &amp;models.Notification{
                Title:      input.Title,
                Content:    input.Content,
                Type:       input.Type,
                Category:   input.Category,
                Priority:   input.Priority,
                IsGlobal:   input.IsGlobal,
                SenderID:   input.SenderID,
                ReceiverID: input.ReceiverID,
                ActionURL:  input.ActionURL,
                ExpiresAt:  input.ExpiresAt,
                IsRead:     false,
        }

        if err := n.db.Create(notification).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to create notification: %w", err)
        }</span>

        <span class="cov8" title="1">return notification, nil</span>
}

// GetNotificationByID gets a notification by ID
func (n *NotificationService) GetNotificationByID(id uint) (*models.Notification, error) <span class="cov8" title="1">{
        var notification models.Notification
        err := n.db.Preload("Sender").Preload("Receiver").First(&amp;notification, id).Error
        if err != nil </span><span class="cov8" title="1">{
                if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        return nil, errors.New("notification not found")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("failed to get notification: %w", err)</span>
        }

        <span class="cov8" title="1">return &amp;notification, nil</span>
}

// GetUserNotifications gets notifications for a specific user (including global notifications)
func (n *NotificationService) GetUserNotifications(userID uint, page, limit int) ([]models.Notification, int64, error) <span class="cov8" title="1">{
        var notifications []models.Notification
        var total int64

        // Build query for user notifications and global notifications
        query := n.db.Model(&amp;models.Notification{}).Where("receiver_id = ? OR is_global = ?", userID, true)

        // Count total
        if err := query.Count(&amp;total).Error; err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to count notifications: %w", err)
        }</span>

        // Get notifications with pagination
        <span class="cov8" title="1">offset := (page - 1) * limit
        err := query.Preload("Sender").Preload("Receiver").
                Order("priority DESC, created_at DESC").
                Offset(offset).
                Limit(limit).
                Find(&amp;notifications).Error

        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to get user notifications: %w", err)
        }</span>

        <span class="cov8" title="1">return notifications, total, nil</span>
}

// GetUnreadNotifications gets unread notifications for a user
func (n *NotificationService) GetUnreadNotifications(userID uint, page, limit int) ([]models.Notification, int64, error) <span class="cov8" title="1">{
        var notifications []models.Notification
        var total int64

        // Build query for unread notifications
        query := n.db.Model(&amp;models.Notification{}).Where("(receiver_id = ? OR is_global = ?) AND is_read = ?", userID, true, false)

        // Count total
        if err := query.Count(&amp;total).Error; err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to count unread notifications: %w", err)
        }</span>

        // Get notifications with pagination
        <span class="cov8" title="1">offset := (page - 1) * limit
        err := query.Preload("Sender").Preload("Receiver").
                Order("priority DESC, created_at DESC").
                Offset(offset).
                Limit(limit).
                Find(&amp;notifications).Error

        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to get unread notifications: %w", err)
        }</span>

        <span class="cov8" title="1">return notifications, total, nil</span>
}

// MarkAsRead marks a notification as read
func (n *NotificationService) MarkAsRead(notificationID, userID uint) error <span class="cov8" title="1">{
        var notification models.Notification
        err := n.db.First(&amp;notification, notificationID).Error
        if err != nil </span><span class="cov0" title="0">{
                if err == gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                        return errors.New("notification not found")
                }</span>
                <span class="cov0" title="0">return fmt.Errorf("failed to find notification: %w", err)</span>
        }

        // Check permission: user can only mark their own notifications or global notifications as read
        <span class="cov8" title="1">if !notification.IsGlobal &amp;&amp; (notification.ReceiverID == nil || *notification.ReceiverID != userID) </span><span class="cov8" title="1">{
                return errors.New("permission denied")
        }</span>

        // Mark as read
        <span class="cov8" title="1">notification.IsRead = true
        if err := n.db.Save(&amp;notification).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to mark notification as read: %w", err)
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// MarkAllAsRead marks all notifications for a user as read
func (n *NotificationService) MarkAllAsRead(userID uint) (int64, error) <span class="cov8" title="1">{
        result := n.db.Model(&amp;models.Notification{}).
                Where("(receiver_id = ? OR is_global = ?) AND is_read = ?", userID, true, false).
                Update("is_read", true)

        if result.Error != nil </span><span class="cov0" title="0">{
                return 0, fmt.Errorf("failed to mark all notifications as read: %w", result.Error)
        }</span>

        <span class="cov8" title="1">return result.RowsAffected, nil</span>
}

// DeleteNotification soft deletes a notification
func (n *NotificationService) DeleteNotification(notificationID, userID uint) error <span class="cov8" title="1">{
        var notification models.Notification
        err := n.db.First(&amp;notification, notificationID).Error
        if err != nil </span><span class="cov0" title="0">{
                if err == gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                        return errors.New("notification not found")
                }</span>
                <span class="cov0" title="0">return fmt.Errorf("failed to find notification: %w", err)</span>
        }

        // Check permission: user can only delete their own notifications
        <span class="cov8" title="1">if notification.ReceiverID == nil || *notification.ReceiverID != userID </span><span class="cov8" title="1">{
                return errors.New("permission denied")
        }</span>

        // Soft delete the notification
        <span class="cov8" title="1">if err := n.db.Delete(&amp;notification).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to delete notification: %w", err)
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// GetUserNotificationStats gets notification statistics for a user
func (n *NotificationService) GetUserNotificationStats(userID uint) (*NotificationStats, error) <span class="cov8" title="1">{
        stats := &amp;NotificationStats{
                ByType:     make(map[string]int64),
                ByCategory: make(map[string]int64),
                ByPriority: make(map[int]int64),
        }

        // Total notifications
        if err := n.db.Model(&amp;models.Notification{}).
                Where("receiver_id = ? OR is_global = ?", userID, true).
                Count(&amp;stats.TotalNotifications).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to count total notifications: %w", err)
        }</span>

        // Unread count
        <span class="cov8" title="1">if err := n.db.Model(&amp;models.Notification{}).
                Where("(receiver_id = ? OR is_global = ?) AND is_read = ?", userID, true, false).
                Count(&amp;stats.UnreadCount).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to count unread notifications: %w", err)
        }</span>

        // Read count
        <span class="cov8" title="1">stats.ReadCount = stats.TotalNotifications - stats.UnreadCount

        // By type
        var typeStats []struct {
                Type  string `json:"type"`
                Count int64  `json:"count"`
        }
        if err := n.db.Model(&amp;models.Notification{}).
                Select("type, COUNT(*) as count").
                Where("receiver_id = ? OR is_global = ?", userID, true).
                Group("type").
                Find(&amp;typeStats).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get type stats: %w", err)
        }</span>
        <span class="cov8" title="1">for _, stat := range typeStats </span><span class="cov8" title="1">{
                stats.ByType[stat.Type] = stat.Count
        }</span>

        // By category
        <span class="cov8" title="1">var categoryStats []struct {
                Category string `json:"category"`
                Count    int64  `json:"count"`
        }
        if err := n.db.Model(&amp;models.Notification{}).
                Select("category, COUNT(*) as count").
                Where("receiver_id = ? OR is_global = ?", userID, true).
                Group("category").
                Find(&amp;categoryStats).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get category stats: %w", err)
        }</span>
        <span class="cov8" title="1">for _, stat := range categoryStats </span><span class="cov8" title="1">{
                stats.ByCategory[stat.Category] = stat.Count
        }</span>

        // By priority
        <span class="cov8" title="1">var priorityStats []struct {
                Priority int   `json:"priority"`
                Count    int64 `json:"count"`
        }
        if err := n.db.Model(&amp;models.Notification{}).
                Select("priority, COUNT(*) as count").
                Where("receiver_id = ? OR is_global = ?", userID, true).
                Group("priority").
                Find(&amp;priorityStats).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get priority stats: %w", err)
        }</span>
        <span class="cov8" title="1">for _, stat := range priorityStats </span><span class="cov8" title="1">{
                stats.ByPriority[stat.Priority] = stat.Count
        }</span>

        <span class="cov8" title="1">return stats, nil</span>
}

// CleanupExpiredNotifications removes expired notifications
func (n *NotificationService) CleanupExpiredNotifications() (int64, error) <span class="cov8" title="1">{
        now := time.Now()
        result := n.db.Where("expires_at IS NOT NULL AND expires_at &lt; ?", now).Delete(&amp;models.Notification{})
        if result.Error != nil </span><span class="cov0" title="0">{
                return 0, fmt.Errorf("failed to cleanup expired notifications: %w", result.Error)
        }</span>

        <span class="cov8" title="1">return result.RowsAffected, nil</span>
}

// BroadcastNotification creates a global notification for all users
func (n *NotificationService) BroadcastNotification(title, content, notificationType, category string, priority int, actionURL string, expiresAt *time.Time) (*models.Notification, error) <span class="cov0" title="0">{
        return n.CreateNotification(CreateNotificationInput{
                Title:     title,
                Content:   content,
                Type:      notificationType,
                Category:  category,
                Priority:  priority,
                IsGlobal:  true,
                ActionURL: actionURL,
                ExpiresAt: expiresAt,
        })
}</span>
</pre>
		
		<pre class="file" id="file4" style="display: none">package services

import (
        "errors"
        "fmt"

        "gorm.io/gorm"
        "xiaoxingcloud.com/admin/internal/models"
)

// PermissionServiceInterface defines the interface for permission service
type PermissionServiceInterface interface {
        CreatePermission(name, resource, action, description string) (*models.Permission, error)
        GetPermissionByID(id uint) (*models.Permission, error)
        GetAllPermissions(page, limit int) ([]models.Permission, int64, error)
        GetPermissionsByResource(resource string) ([]models.Permission, error)
        UpdatePermission(id uint, name, resource, action, description string) (*models.Permission, error)
        DeletePermission(id uint) error
        GetResourceList() ([]string, error)
        GetPermissionsByResourceGrouped() (map[string][]models.Permission, error)
        SearchPermissions(query string, page, limit int) ([]models.Permission, int64, error)
}

type PermissionService struct {
        db *gorm.DB
}

// Compile-time interface compliance check
var _ PermissionServiceInterface = (*PermissionService)(nil)

// NewPermissionService creates a new permission service
func NewPermissionService(db *gorm.DB) *PermissionService <span class="cov8" title="1">{
        return &amp;PermissionService{db: db}
}</span>

// CreatePermission creates a new permission
func (p *PermissionService) CreatePermission(name, resource, action, description string) (*models.Permission, error) <span class="cov8" title="1">{
        // Check if permission name already exists
        var existingPermission models.Permission
        err := p.db.Where("name = ?", name).First(&amp;existingPermission).Error
        if err == nil </span><span class="cov8" title="1">{
                return nil, errors.New("permission name already exists")
        }</span>
        <span class="cov8" title="1">if err != gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to check existing permission: %w", err)
        }</span>

        // Create new permission
        <span class="cov8" title="1">permission := &amp;models.Permission{
                Name:        name,
                Resource:    resource,
                Action:      action,
                Description: description,
        }

        if err := p.db.Create(permission).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to create permission: %w", err)
        }</span>

        <span class="cov8" title="1">return permission, nil</span>
}

// GetPermissionByID gets a permission by ID
func (p *PermissionService) GetPermissionByID(id uint) (*models.Permission, error) <span class="cov8" title="1">{
        var permission models.Permission
        err := p.db.First(&amp;permission, id).Error
        if err != nil </span><span class="cov8" title="1">{
                if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        return nil, errors.New("permission not found")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("failed to get permission: %w", err)</span>
        }

        <span class="cov8" title="1">return &amp;permission, nil</span>
}

// GetAllPermissions gets all permissions with pagination
func (p *PermissionService) GetAllPermissions(page, limit int) ([]models.Permission, int64, error) <span class="cov8" title="1">{
        var permissions []models.Permission
        var total int64

        // Count total permissions
        if err := p.db.Model(&amp;models.Permission{}).Count(&amp;total).Error; err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to count permissions: %w", err)
        }</span>

        // Get permissions with pagination
        <span class="cov8" title="1">offset := (page - 1) * limit
        err := p.db.Offset(offset).Limit(limit).Find(&amp;permissions).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to get permissions: %w", err)
        }</span>

        <span class="cov8" title="1">return permissions, total, nil</span>
}

// GetPermissionsByResource gets permissions by resource
func (p *PermissionService) GetPermissionsByResource(resource string) ([]models.Permission, error) <span class="cov8" title="1">{
        var permissions []models.Permission
        err := p.db.Where("resource = ?", resource).Find(&amp;permissions).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get permissions by resource: %w", err)
        }</span>

        <span class="cov8" title="1">return permissions, nil</span>
}

// UpdatePermission updates a permission
func (p *PermissionService) UpdatePermission(id uint, name, resource, action, description string) (*models.Permission, error) <span class="cov8" title="1">{
        var permission models.Permission
        err := p.db.First(&amp;permission, id).Error
        if err != nil </span><span class="cov8" title="1">{
                if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        return nil, errors.New("permission not found")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("failed to find permission: %w", err)</span>
        }

        // Check if new name conflicts with existing permission (if name is being changed)
        <span class="cov8" title="1">if permission.Name != name </span><span class="cov8" title="1">{
                var existingPermission models.Permission
                err := p.db.Where("name = ? AND id != ?", name, id).First(&amp;existingPermission).Error
                if err == nil </span><span class="cov0" title="0">{
                        return nil, errors.New("permission name already exists")
                }</span>
                <span class="cov8" title="1">if err != gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("failed to check existing permission: %w", err)
                }</span>
        }

        // Update permission
        <span class="cov8" title="1">permission.Name = name
        permission.Resource = resource
        permission.Action = action
        permission.Description = description

        if err := p.db.Save(&amp;permission).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to update permission: %w", err)
        }</span>

        <span class="cov8" title="1">return &amp;permission, nil</span>
}

// DeletePermission soft deletes a permission
func (p *PermissionService) DeletePermission(id uint) error <span class="cov8" title="1">{
        var permission models.Permission
        err := p.db.First(&amp;permission, id).Error
        if err != nil </span><span class="cov8" title="1">{
                if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        return errors.New("permission not found")
                }</span>
                <span class="cov0" title="0">return fmt.Errorf("failed to find permission: %w", err)</span>
        }

        // Check if permission is assigned to any roles
        <span class="cov8" title="1">var rolePermissionCount int64
        if err := p.db.Model(&amp;models.RolePermission{}).Where("permission_id = ?", id).Count(&amp;rolePermissionCount).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to check permission assignments: %w", err)
        }</span>

        <span class="cov8" title="1">if rolePermissionCount &gt; 0 </span><span class="cov8" title="1">{
                return errors.New("cannot delete permission that is assigned to roles")
        }</span>

        // Soft delete the permission
        <span class="cov8" title="1">if err := p.db.Delete(&amp;permission).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to delete permission: %w", err)
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// GetResourceList gets a list of all unique resources
func (p *PermissionService) GetResourceList() ([]string, error) <span class="cov8" title="1">{
        var resources []string
        err := p.db.Model(&amp;models.Permission{}).Distinct("resource").Pluck("resource", &amp;resources).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get resource list: %w", err)
        }</span>

        <span class="cov8" title="1">return resources, nil</span>
}

// GetPermissionsByResourceGrouped gets permissions grouped by resource
func (p *PermissionService) GetPermissionsByResourceGrouped() (map[string][]models.Permission, error) <span class="cov0" title="0">{
        var permissions []models.Permission
        err := p.db.Order("resource, action").Find(&amp;permissions).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get permissions: %w", err)
        }</span>

        // Group permissions by resource
        <span class="cov0" title="0">grouped := make(map[string][]models.Permission)
        for _, permission := range permissions </span><span class="cov0" title="0">{
                grouped[permission.Resource] = append(grouped[permission.Resource], permission)
        }</span>

        <span class="cov0" title="0">return grouped, nil</span>
}

// SearchPermissions searches permissions by name, resource, or action
func (p *PermissionService) SearchPermissions(query string, page, limit int) ([]models.Permission, int64, error) <span class="cov0" title="0">{
        var permissions []models.Permission
        var total int64

        // Build search query
        searchQuery := "%" + query + "%"
        whereClause := "name LIKE ? OR resource LIKE ? OR action LIKE ? OR description LIKE ?"

        // Count total matching permissions
        if err := p.db.Model(&amp;models.Permission{}).Where(whereClause, searchQuery, searchQuery, searchQuery, searchQuery).Count(&amp;total).Error; err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to count permissions: %w", err)
        }</span>

        // Get permissions with pagination
        <span class="cov0" title="0">offset := (page - 1) * limit
        err := p.db.Where(whereClause, searchQuery, searchQuery, searchQuery, searchQuery).
                Offset(offset).Limit(limit).Find(&amp;permissions).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to search permissions: %w", err)
        }</span>

        <span class="cov0" title="0">return permissions, total, nil</span>
}
</pre>
		
		<pre class="file" id="file5" style="display: none">package services

import (
        "errors"
        "fmt"

        "gorm.io/gorm"
        "xiaoxingcloud.com/admin/internal/models"
)

// RBACServiceInterface defines the interface for RBAC service
type RBACServiceInterface interface {
        CheckPermission(userID uint, resource, action string) (bool, error)
        GetUserPermissions(userID uint) ([]models.Permission, error)
        GetUserRoles(userID uint) ([]models.Role, error)
        AssignRoleToUser(userID, roleID uint) error
        RemoveRoleFromUser(userID, roleID uint) error
        AssignPermissionToRole(roleID, permissionID uint) error
        RemovePermissionFromRole(roleID, permissionID uint) error
}

type RBACService struct {
        db *gorm.DB
}

// Compile-time interface compliance check
var _ RBACServiceInterface = (*RBACService)(nil)

func NewRBACService(db *gorm.DB) *RBACService <span class="cov8" title="1">{
        return &amp;RBACService{db: db}
}</span>

// CheckPermission checks if a user has a specific permission
func (r *RBACService) CheckPermission(userID uint, resource, action string) (bool, error) <span class="cov8" title="1">{
        var count int64

        // Query to check if user has the permission through any of their roles
        err := r.db.Table("users").
                Joins("JOIN user_roles ON users.id = user_roles.user_id").
                Joins("JOIN roles ON user_roles.role_id = roles.id").
                Joins("JOIN role_permissions ON roles.id = role_permissions.role_id").
                Joins("JOIN permissions ON role_permissions.permission_id = permissions.id").
                Where("users.id = ? AND users.is_active = ? AND roles.is_active = ?", userID, true, true).
                Where("permissions.resource = ? AND permissions.action = ?", resource, action).
                Count(&amp;count).Error

        if err != nil </span><span class="cov0" title="0">{
                return false, fmt.Errorf("failed to check permission: %w", err)
        }</span>

        <span class="cov8" title="1">return count &gt; 0, nil</span>
}

// GetUserPermissions returns all permissions for a user
func (r *RBACService) GetUserPermissions(userID uint) ([]models.Permission, error) <span class="cov8" title="1">{
        var permissions []models.Permission

        err := r.db.Table("permissions").
                Select("DISTINCT permissions.*").
                Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
                Joins("JOIN roles ON role_permissions.role_id = roles.id").
                Joins("JOIN user_roles ON roles.id = user_roles.role_id").
                Joins("JOIN users ON user_roles.user_id = users.id").
                Where("users.id = ? AND users.is_active = ? AND roles.is_active = ?", userID, true, true).
                Find(&amp;permissions).Error

        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get user permissions: %w", err)
        }</span>

        <span class="cov8" title="1">return permissions, nil</span>
}

// GetUserRoles returns all roles for a user
func (r *RBACService) GetUserRoles(userID uint) ([]models.Role, error) <span class="cov8" title="1">{
        var user models.User
        err := r.db.Preload("Roles").Where("id = ?", userID).First(&amp;user).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get user roles: %w", err)
        }</span>

        <span class="cov8" title="1">return user.Roles, nil</span>
}

// AssignRoleToUser assigns a role to a user
func (r *RBACService) AssignRoleToUser(userID, roleID uint) error <span class="cov8" title="1">{
        // Check if user exists
        var user models.User
        if err := r.db.First(&amp;user, userID).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("user not found: %w", err)
        }</span>

        // Check if role exists
        <span class="cov8" title="1">var role models.Role
        if err := r.db.First(&amp;role, roleID).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("role not found: %w", err)
        }</span>

        // Check if assignment already exists
        <span class="cov8" title="1">var userRole models.UserRole
        err := r.db.Where("user_id = ? AND role_id = ?", userID, roleID).First(&amp;userRole).Error
        if err == nil </span><span class="cov8" title="1">{
                return errors.New("user already has this role")
        }</span>
        <span class="cov8" title="1">if err != gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to check existing role assignment: %w", err)
        }</span>

        // Create the assignment
        <span class="cov8" title="1">userRole = models.UserRole{
                UserID: userID,
                RoleID: roleID,
        }

        if err := r.db.Create(&amp;userRole).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to assign role to user: %w", err)
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// RemoveRoleFromUser removes a role from a user
func (r *RBACService) RemoveRoleFromUser(userID, roleID uint) error <span class="cov8" title="1">{
        result := r.db.Where("user_id = ? AND role_id = ?", userID, roleID).Delete(&amp;models.UserRole{})
        if result.Error != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to remove role from user: %w", result.Error)
        }</span>

        <span class="cov8" title="1">if result.RowsAffected == 0 </span><span class="cov0" title="0">{
                return errors.New("role assignment not found")
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// AssignPermissionToRole assigns a permission to a role
func (r *RBACService) AssignPermissionToRole(roleID, permissionID uint) error <span class="cov8" title="1">{
        // Check if role exists
        var role models.Role
        if err := r.db.First(&amp;role, roleID).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("role not found: %w", err)
        }</span>

        // Check if permission exists
        <span class="cov8" title="1">var permission models.Permission
        if err := r.db.First(&amp;permission, permissionID).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("permission not found: %w", err)
        }</span>

        // Check if assignment already exists
        <span class="cov8" title="1">var rolePermission models.RolePermission
        err := r.db.Where("role_id = ? AND permission_id = ?", roleID, permissionID).First(&amp;rolePermission).Error
        if err == nil </span><span class="cov8" title="1">{
                return errors.New("role already has this permission")
        }</span>
        <span class="cov8" title="1">if err != gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to check existing permission assignment: %w", err)
        }</span>

        // Create the assignment
        <span class="cov8" title="1">rolePermission = models.RolePermission{
                RoleID:       roleID,
                PermissionID: permissionID,
        }

        if err := r.db.Create(&amp;rolePermission).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to assign permission to role: %w", err)
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// RemovePermissionFromRole removes a permission from a role
func (r *RBACService) RemovePermissionFromRole(roleID, permissionID uint) error <span class="cov8" title="1">{
        result := r.db.Where("role_id = ? AND permission_id = ?", roleID, permissionID).Delete(&amp;models.RolePermission{})
        if result.Error != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to remove permission from role: %w", result.Error)
        }</span>

        <span class="cov8" title="1">if result.RowsAffected == 0 </span><span class="cov0" title="0">{
                return errors.New("permission assignment not found")
        }</span>

        <span class="cov8" title="1">return nil</span>
}
</pre>
		
		<pre class="file" id="file6" style="display: none">package services

import (
        "errors"
        "fmt"

        "gorm.io/gorm"
        "xiaoxingcloud.com/admin/internal/models"
)

// RoleServiceInterface defines the interface for role service
type RoleServiceInterface interface {
        CreateRole(name, description string) (*models.Role, error)
        GetRoleByID(id uint) (*models.Role, error)
        GetAllRoles(page, limit int) ([]models.Role, int64, error)
        UpdateRole(id uint, name, description string, isActive bool) (*models.Role, error)
        DeleteRole(id uint) error
        AssignPermissionToRole(roleID, permissionID uint) error
        RemovePermissionFromRole(roleID, permissionID uint) error
        GetRolePermissions(roleID uint) ([]models.Permission, error)
}

type RoleService struct {
        db *gorm.DB
}

// Compile-time interface compliance check
var _ RoleServiceInterface = (*RoleService)(nil)

// NewRoleService creates a new role service
func NewRoleService(db *gorm.DB) *RoleService <span class="cov8" title="1">{
        return &amp;RoleService{db: db}
}</span>

// CreateRole creates a new role
func (r *RoleService) CreateRole(name, description string) (*models.Role, error) <span class="cov8" title="1">{
        // Check if role name already exists
        var existingRole models.Role
        err := r.db.Where("name = ?", name).First(&amp;existingRole).Error
        if err == nil </span><span class="cov8" title="1">{
                return nil, errors.New("role name already exists")
        }</span>
        <span class="cov8" title="1">if err != gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to check existing role: %w", err)
        }</span>

        // Create new role
        <span class="cov8" title="1">role := &amp;models.Role{
                Name:        name,
                Description: description,
                IsActive:    true,
        }

        if err := r.db.Create(role).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to create role: %w", err)
        }</span>

        <span class="cov8" title="1">return role, nil</span>
}

// GetRoleByID gets a role by ID with permissions
func (r *RoleService) GetRoleByID(id uint) (*models.Role, error) <span class="cov8" title="1">{
        var role models.Role
        err := r.db.Preload("Permissions").First(&amp;role, id).Error
        if err != nil </span><span class="cov8" title="1">{
                if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        return nil, errors.New("role not found")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("failed to get role: %w", err)</span>
        }

        <span class="cov8" title="1">return &amp;role, nil</span>
}

// GetAllRoles gets all roles with pagination
func (r *RoleService) GetAllRoles(page, limit int) ([]models.Role, int64, error) <span class="cov8" title="1">{
        var roles []models.Role
        var total int64

        // Count total roles
        if err := r.db.Model(&amp;models.Role{}).Count(&amp;total).Error; err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to count roles: %w", err)
        }</span>

        // Get roles with pagination
        <span class="cov8" title="1">offset := (page - 1) * limit
        err := r.db.Preload("Permissions").Offset(offset).Limit(limit).Find(&amp;roles).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to get roles: %w", err)
        }</span>

        <span class="cov8" title="1">return roles, total, nil</span>
}

// UpdateRole updates a role
func (r *RoleService) UpdateRole(id uint, name, description string, isActive bool) (*models.Role, error) <span class="cov8" title="1">{
        var role models.Role
        err := r.db.First(&amp;role, id).Error
        if err != nil </span><span class="cov8" title="1">{
                if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        return nil, errors.New("role not found")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("failed to find role: %w", err)</span>
        }

        // Check if new name conflicts with existing role (if name is being changed)
        <span class="cov8" title="1">if role.Name != name </span><span class="cov8" title="1">{
                var existingRole models.Role
                err := r.db.Where("name = ? AND id != ?", name, id).First(&amp;existingRole).Error
                if err == nil </span><span class="cov0" title="0">{
                        return nil, errors.New("role name already exists")
                }</span>
                <span class="cov8" title="1">if err != gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("failed to check existing role: %w", err)
                }</span>
        }

        // Update role
        <span class="cov8" title="1">role.Name = name
        role.Description = description
        role.IsActive = isActive

        if err := r.db.Save(&amp;role).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to update role: %w", err)
        }</span>

        <span class="cov8" title="1">return &amp;role, nil</span>
}

// DeleteRole soft deletes a role
func (r *RoleService) DeleteRole(id uint) error <span class="cov8" title="1">{
        var role models.Role
        err := r.db.First(&amp;role, id).Error
        if err != nil </span><span class="cov8" title="1">{
                if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        return errors.New("role not found")
                }</span>
                <span class="cov0" title="0">return fmt.Errorf("failed to find role: %w", err)</span>
        }

        // Check if role is assigned to any users
        <span class="cov8" title="1">var userRoleCount int64
        if err := r.db.Model(&amp;models.UserRole{}).Where("role_id = ?", id).Count(&amp;userRoleCount).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to check role assignments: %w", err)
        }</span>

        <span class="cov8" title="1">if userRoleCount &gt; 0 </span><span class="cov0" title="0">{
                return errors.New("cannot delete role that is assigned to users")
        }</span>

        // Soft delete the role
        <span class="cov8" title="1">if err := r.db.Delete(&amp;role).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to delete role: %w", err)
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// AssignPermissionToRole assigns a permission to a role
func (r *RoleService) AssignPermissionToRole(roleID, permissionID uint) error <span class="cov8" title="1">{
        // Check if role exists
        var role models.Role
        if err := r.db.First(&amp;role, roleID).Error; err != nil </span><span class="cov0" title="0">{
                if err == gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                        return errors.New("role not found")
                }</span>
                <span class="cov0" title="0">return fmt.Errorf("failed to find role: %w", err)</span>
        }

        // Check if permission exists
        <span class="cov8" title="1">var permission models.Permission
        if err := r.db.First(&amp;permission, permissionID).Error; err != nil </span><span class="cov0" title="0">{
                if err == gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                        return errors.New("permission not found")
                }</span>
                <span class="cov0" title="0">return fmt.Errorf("failed to find permission: %w", err)</span>
        }

        // Check if assignment already exists
        <span class="cov8" title="1">var rolePermission models.RolePermission
        err := r.db.Where("role_id = ? AND permission_id = ?", roleID, permissionID).First(&amp;rolePermission).Error
        if err == nil </span><span class="cov8" title="1">{
                return errors.New("permission already assigned to role")
        }</span>
        <span class="cov8" title="1">if err != gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to check existing assignment: %w", err)
        }</span>

        // Create the assignment
        <span class="cov8" title="1">rolePermission = models.RolePermission{
                RoleID:       roleID,
                PermissionID: permissionID,
        }

        if err := r.db.Create(&amp;rolePermission).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to assign permission to role: %w", err)
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// RemovePermissionFromRole removes a permission from a role
func (r *RoleService) RemovePermissionFromRole(roleID, permissionID uint) error <span class="cov8" title="1">{
        result := r.db.Where("role_id = ? AND permission_id = ?", roleID, permissionID).Delete(&amp;models.RolePermission{})
        if result.Error != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to remove permission from role: %w", result.Error)
        }</span>

        <span class="cov8" title="1">if result.RowsAffected == 0 </span><span class="cov8" title="1">{
                return errors.New("permission assignment not found")
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// GetRolePermissions gets all permissions for a role
func (r *RoleService) GetRolePermissions(roleID uint) ([]models.Permission, error) <span class="cov0" title="0">{
        var permissions []models.Permission
        err := r.db.Table("permissions").
                Select("permissions.*").
                Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
                Where("role_permissions.role_id = ?", roleID).
                Find(&amp;permissions).Error

        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get role permissions: %w", err)
        }</span>

        <span class="cov0" title="0">return permissions, nil</span>
}
</pre>
		
		<pre class="file" id="file7" style="display: none">package services

import (
        "errors"
        "fmt"

        "gorm.io/gorm"
        "xiaoxingcloud.com/admin/internal/models"
)

// SettingServiceInterface defines the interface for setting service
type SettingServiceInterface interface {
        SetSetting(key, value, settingType, category, description string, isPublic, isEditable bool) error
        GetSetting(key string) (*models.SystemSetting, error)
        GetSettingValue(key string) (string, error)
        GetSettingsByCategory(category string) ([]models.SystemSetting, error)
        GetAllSettings(isAdmin bool) ([]models.SystemSetting, error)
        DeleteSetting(key string) error
        GetCategories() ([]string, error)
        BulkSetSettings(settings map[string]SettingInput) error
        InitializeDefaultSettings() error
}

type SettingService struct {
        db *gorm.DB
}

// Compile-time interface compliance check
var _ SettingServiceInterface = (*SettingService)(nil)

// SettingInput represents input for setting operations
type SettingInput struct {
        Value       string `json:"value"`
        Type        string `json:"type"`
        Category    string `json:"category"`
        Description string `json:"description"`
        IsPublic    bool   `json:"is_public"`
        IsEditable  bool   `json:"is_editable"`
}

// NewSettingService creates a new setting service
func NewSettingService(db *gorm.DB) *SettingService <span class="cov8" title="1">{
        return &amp;SettingService{db: db}
}</span>

// SetSetting creates or updates a system setting
func (s *SettingService) SetSetting(key, value, settingType, category, description string, isPublic, isEditable bool) error <span class="cov8" title="1">{
        var setting models.SystemSetting
        err := s.db.Where("key = ?", key).First(&amp;setting).Error

        if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                // Create new setting
                setting = models.SystemSetting{
                        Key:         key,
                        Value:       value,
                        Type:        settingType,
                        Category:    category,
                        Description: description,
                        IsPublic:    isPublic,
                        IsEditable:  isEditable,
                }
                if err := s.db.Create(&amp;setting).Error; err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to create setting: %w", err)
                }</span>
        } else<span class="cov8" title="1"> if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to check existing setting: %w", err)
        }</span> else<span class="cov8" title="1"> {
                // Update existing setting
                if !setting.IsEditable </span><span class="cov0" title="0">{
                        return errors.New("setting is not editable")
                }</span>

                <span class="cov8" title="1">setting.Value = value
                setting.Type = settingType
                setting.Category = category
                setting.Description = description
                setting.IsPublic = isPublic
                setting.IsEditable = isEditable

                if err := s.db.Save(&amp;setting).Error; err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to update setting: %w", err)
                }</span>
        }

        <span class="cov8" title="1">return nil</span>
}

// GetSetting retrieves a system setting by key
func (s *SettingService) GetSetting(key string) (*models.SystemSetting, error) <span class="cov8" title="1">{
        var setting models.SystemSetting
        err := s.db.Where("key = ?", key).First(&amp;setting).Error
        if err != nil </span><span class="cov8" title="1">{
                if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        return nil, errors.New("setting not found")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("failed to get setting: %w", err)</span>
        }

        <span class="cov8" title="1">return &amp;setting, nil</span>
}

// GetSettingValue retrieves only the value of a system setting
func (s *SettingService) GetSettingValue(key string) (string, error) <span class="cov8" title="1">{
        setting, err := s.GetSetting(key)
        if err != nil </span><span class="cov8" title="1">{
                return "", err
        }</span>
        <span class="cov8" title="1">return setting.Value, nil</span>
}

// GetSettingsByCategory retrieves all settings in a specific category
func (s *SettingService) GetSettingsByCategory(category string) ([]models.SystemSetting, error) <span class="cov8" title="1">{
        var settings []models.SystemSetting
        err := s.db.Where("category = ?", category).Order("key").Find(&amp;settings).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get settings by category: %w", err)
        }</span>

        <span class="cov8" title="1">return settings, nil</span>
}

// GetAllSettings retrieves all system settings
func (s *SettingService) GetAllSettings(isAdmin bool) ([]models.SystemSetting, error) <span class="cov8" title="1">{
        var settings []models.SystemSetting
        query := s.db.Order("category, key")

        // Non-admin users can only see public settings
        if !isAdmin </span><span class="cov8" title="1">{
                query = query.Where("is_public = ?", true)
        }</span>

        <span class="cov8" title="1">err := query.Find(&amp;settings).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get all settings: %w", err)
        }</span>

        <span class="cov8" title="1">return settings, nil</span>
}

// DeleteSetting deletes a system setting
func (s *SettingService) DeleteSetting(key string) error <span class="cov8" title="1">{
        var setting models.SystemSetting
        err := s.db.Where("key = ?", key).First(&amp;setting).Error
        if err != nil </span><span class="cov0" title="0">{
                if err == gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                        return errors.New("setting not found")
                }</span>
                <span class="cov0" title="0">return fmt.Errorf("failed to find setting: %w", err)</span>
        }

        <span class="cov8" title="1">if !setting.IsEditable </span><span class="cov0" title="0">{
                return errors.New("setting is not editable")
        }</span>

        <span class="cov8" title="1">if err := s.db.Delete(&amp;setting).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to delete setting: %w", err)
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// GetCategories retrieves all unique setting categories
func (s *SettingService) GetCategories() ([]string, error) <span class="cov8" title="1">{
        var categories []string
        err := s.db.Model(&amp;models.SystemSetting{}).Distinct("category").Pluck("category", &amp;categories).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get categories: %w", err)
        }</span>

        <span class="cov8" title="1">return categories, nil</span>
}

// BulkSetSettings sets multiple settings at once
func (s *SettingService) BulkSetSettings(settings map[string]SettingInput) error <span class="cov8" title="1">{
        tx := s.db.Begin()
        defer func() </span><span class="cov8" title="1">{
                if r := recover(); r != nil </span><span class="cov0" title="0">{
                        tx.Rollback()
                }</span>
        }()

        <span class="cov8" title="1">for key, input := range settings </span><span class="cov8" title="1">{
                err := s.setSettingInTx(tx, key, input.Value, input.Type, input.Category, input.Description, input.IsPublic, input.IsEditable)
                if err != nil </span><span class="cov0" title="0">{
                        tx.Rollback()
                        return fmt.Errorf("failed to set setting %s: %w", key, err)
                }</span>
        }

        <span class="cov8" title="1">if err := tx.Commit().Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to commit bulk settings: %w", err)
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// setSettingInTx is a helper method to set a setting within a transaction
func (s *SettingService) setSettingInTx(tx *gorm.DB, key, value, settingType, category, description string, isPublic, isEditable bool) error <span class="cov8" title="1">{
        var setting models.SystemSetting
        err := tx.Where("key = ?", key).First(&amp;setting).Error

        if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                // Create new setting
                setting = models.SystemSetting{
                        Key:         key,
                        Value:       value,
                        Type:        settingType,
                        Category:    category,
                        Description: description,
                        IsPublic:    isPublic,
                        IsEditable:  isEditable,
                }
                if err := tx.Create(&amp;setting).Error; err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to create setting: %w", err)
                }</span>
        } else<span class="cov0" title="0"> if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to check existing setting: %w", err)
        }</span> else<span class="cov0" title="0"> {
                // Update existing setting
                if !setting.IsEditable </span><span class="cov0" title="0">{
                        return errors.New("setting is not editable")
                }</span>

                <span class="cov0" title="0">setting.Value = value
                setting.Type = settingType
                setting.Category = category
                setting.Description = description
                setting.IsPublic = isPublic
                setting.IsEditable = isEditable

                if err := tx.Save(&amp;setting).Error; err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to update setting: %w", err)
                }</span>
        }

        <span class="cov8" title="1">return nil</span>
}

// InitializeDefaultSettings creates default system settings if they don't exist
func (s *SettingService) InitializeDefaultSettings() error <span class="cov8" title="1">{
        defaultSettings := map[string]SettingInput{
                "app.name": {
                        Value:       "Admin System",
                        Type:        "string",
                        Category:    "general",
                        Description: "Application name",
                        IsPublic:    true,
                        IsEditable:  true,
                },
                "app.version": {
                        Value:       "1.0.0",
                        Type:        "string",
                        Category:    "general",
                        Description: "Application version",
                        IsPublic:    true,
                        IsEditable:  false,
                },
                "app.description": {
                        Value:       "A modern admin system built with Go and Vue.js",
                        Type:        "string",
                        Category:    "general",
                        Description: "Application description",
                        IsPublic:    true,
                        IsEditable:  true,
                },
                "security.session_timeout": {
                        Value:       "3600",
                        Type:        "number",
                        Category:    "security",
                        Description: "Session timeout in seconds",
                        IsPublic:    false,
                        IsEditable:  true,
                },
                "security.max_login_attempts": {
                        Value:       "5",
                        Type:        "number",
                        Category:    "security",
                        Description: "Maximum login attempts before lockout",
                        IsPublic:    false,
                        IsEditable:  true,
                },
                "email.enabled": {
                        Value:       "false",
                        Type:        "boolean",
                        Category:    "email",
                        Description: "Enable email notifications",
                        IsPublic:    false,
                        IsEditable:  true,
                },
                "email.smtp_host": {
                        Value:       "",
                        Type:        "string",
                        Category:    "email",
                        Description: "SMTP server host",
                        IsPublic:    false,
                        IsEditable:  true,
                },
                "email.smtp_port": {
                        Value:       "587",
                        Type:        "number",
                        Category:    "email",
                        Description: "SMTP server port",
                        IsPublic:    false,
                        IsEditable:  true,
                },
        }

        for key, input := range defaultSettings </span><span class="cov8" title="1">{
                // Only create if doesn't exist
                _, err := s.GetSetting(key)
                if err != nil &amp;&amp; err.Error() == "setting not found" </span><span class="cov8" title="1">{
                        err = s.SetSetting(key, input.Value, input.Type, input.Category, input.Description, input.IsPublic, input.IsEditable)
                        if err != nil </span><span class="cov0" title="0">{
                                return fmt.Errorf("failed to initialize default setting %s: %w", key, err)
                        }</span>
                }
        }

        <span class="cov8" title="1">return nil</span>
}
</pre>
		
		<pre class="file" id="file8" style="display: none">package services

import (
        "errors"
        "fmt"

        "gorm.io/gorm"
        "xiaoxingcloud.com/admin/internal/auth"
        "xiaoxingcloud.com/admin/internal/models"
)

// UserServiceInterface defines the interface for user service
type UserServiceInterface interface {
        CreateUser(username, email, password, firstName, lastName string) (*models.User, error)
        GetUserByID(id uint) (*models.User, error)
        GetUserByUsername(username string) (*models.User, error)
        GetUserByEmail(email string) (*models.User, error)
        GetAllUsers(page, limit int) ([]models.User, int64, error)
        UpdateUser(id uint, updates map[string]any) (*models.User, error)
        DeleteUser(id uint) error
        AuthenticateUser(identifier, password string) (*models.User, error)
        ActivateUser(id uint) error
        DeactivateUser(id uint) error
}

type UserService struct {
        db *gorm.DB
}

// Compile-time interface compliance check
var _ UserServiceInterface = (*UserService)(nil)

func NewUserService(db *gorm.DB) *UserService <span class="cov8" title="1">{
        return &amp;UserService{db: db}
}</span>

// CreateUser creates a new user
func (u *UserService) CreateUser(username, email, password, firstName, lastName string) (*models.User, error) <span class="cov8" title="1">{
        // Check if username already exists
        var existingUser models.User
        if err := u.db.Where("username = ?", username).First(&amp;existingUser).Error; err == nil </span><span class="cov8" title="1">{
                return nil, errors.New("username already exists")
        }</span>

        // Check if email already exists
        <span class="cov8" title="1">if err := u.db.Where("email = ?", email).First(&amp;existingUser).Error; err == nil </span><span class="cov8" title="1">{
                return nil, errors.New("email already exists")
        }</span>

        // Hash password
        <span class="cov8" title="1">hashedPassword, err := auth.HashPassword(password)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to hash password: %w", err)
        }</span>

        // Create user
        <span class="cov8" title="1">user := models.User{
                Username:  username,
                Email:     email,
                Password:  hashedPassword,
                FirstName: firstName,
                LastName:  lastName,
                IsActive:  true,
        }

        if err := u.db.Create(&amp;user).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to create user: %w", err)
        }</span>

        // Reload user with roles to return complete information
        <span class="cov8" title="1">if err := u.db.Preload("Roles.Permissions").Where("id = ?", user.ID).First(&amp;user).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to reload user with roles: %w", err)
        }</span>

        <span class="cov8" title="1">return &amp;user, nil</span>
}

// GetUserByID gets a user by ID
func (u *UserService) GetUserByID(id uint) (*models.User, error) <span class="cov8" title="1">{
        var user models.User
        err := u.db.Preload("Roles.Permissions").Preload("Attributes").Where("id = ?", id).First(&amp;user).Error
        if err != nil </span><span class="cov8" title="1">{
                if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        return nil, errors.New("user not found")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("failed to get user: %w", err)</span>
        }

        <span class="cov8" title="1">return &amp;user, nil</span>
}

// GetUserByUsername gets a user by username
func (u *UserService) GetUserByUsername(username string) (*models.User, error) <span class="cov8" title="1">{
        var user models.User
        err := u.db.Where("username = ?", username).First(&amp;user).Error
        if err != nil </span><span class="cov8" title="1">{
                if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        return nil, errors.New("user not found")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("failed to get user: %w", err)</span>
        }

        <span class="cov8" title="1">return &amp;user, nil</span>
}

// GetUserByEmail gets a user by email
func (u *UserService) GetUserByEmail(email string) (*models.User, error) <span class="cov8" title="1">{
        var user models.User
        err := u.db.Where("email = ?", email).First(&amp;user).Error
        if err != nil </span><span class="cov0" title="0">{
                if err == gorm.ErrRecordNotFound </span><span class="cov0" title="0">{
                        return nil, errors.New("user not found")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("failed to get user: %w", err)</span>
        }

        <span class="cov8" title="1">return &amp;user, nil</span>
}

// GetAllUsers gets all users with pagination
func (u *UserService) GetAllUsers(page, limit int) ([]models.User, int64, error) <span class="cov8" title="1">{
        var users []models.User
        var total int64

        // Count total users
        if err := u.db.Model(&amp;models.User{}).Count(&amp;total).Error; err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to count users: %w", err)
        }</span>

        // Get users with pagination
        <span class="cov8" title="1">offset := (page - 1) * limit
        err := u.db.Preload("Roles.Permissions").Offset(offset).Limit(limit).Find(&amp;users).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, fmt.Errorf("failed to get users: %w", err)
        }</span>

        <span class="cov8" title="1">return users, total, nil</span>
}

// UpdateUser updates a user
func (u *UserService) UpdateUser(id uint, updates map[string]any) (*models.User, error) <span class="cov8" title="1">{
        var user models.User
        if err := u.db.First(&amp;user, id).Error; err != nil </span><span class="cov8" title="1">{
                if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        return nil, errors.New("user not found")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("failed to find user: %w", err)</span>
        }

        // If password is being updated, hash it
        <span class="cov8" title="1">if password, ok := updates["password"].(string); ok </span><span class="cov0" title="0">{
                hashedPassword, err := auth.HashPassword(password)
                if err != nil </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("failed to hash password: %w", err)
                }</span>
                <span class="cov0" title="0">updates["password"] = hashedPassword</span>
        }

        <span class="cov8" title="1">if err := u.db.Model(&amp;user).Updates(updates).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to update user: %w", err)
        }</span>

        <span class="cov8" title="1">return &amp;user, nil</span>
}

// DeleteUser soft deletes a user
func (u *UserService) DeleteUser(id uint) error <span class="cov8" title="1">{
        result := u.db.Delete(&amp;models.User{}, id)
        if result.Error != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to delete user: %w", result.Error)
        }</span>

        <span class="cov8" title="1">if result.RowsAffected == 0 </span><span class="cov0" title="0">{
                return errors.New("user not found")
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// AuthenticateUser authenticates a user with username/email and password
func (u *UserService) AuthenticateUser(identifier, password string) (*models.User, error) <span class="cov8" title="1">{
        var user models.User

        // Try to find user by username or email
        err := u.db.Where("username = ? OR email = ?", identifier, identifier).First(&amp;user).Error
        if err != nil </span><span class="cov8" title="1">{
                if err == gorm.ErrRecordNotFound </span><span class="cov8" title="1">{
                        return nil, errors.New("invalid credentials")
                }</span>
                <span class="cov0" title="0">return nil, fmt.Errorf("failed to find user: %w", err)</span>
        }

        // Check if user is active
        <span class="cov8" title="1">if !user.IsActive </span><span class="cov0" title="0">{
                return nil, errors.New("user account is disabled")
        }</span>

        // Check password
        <span class="cov8" title="1">if !auth.CheckPassword(password, user.Password) </span><span class="cov8" title="1">{
                return nil, errors.New("invalid credentials")
        }</span>

        <span class="cov8" title="1">return &amp;user, nil</span>
}

// ActivateUser activates a user account
func (u *UserService) ActivateUser(id uint) error <span class="cov8" title="1">{
        result := u.db.Model(&amp;models.User{}).Where("id = ?", id).Update("is_active", true)
        if result.Error != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to activate user: %w", result.Error)
        }</span>

        <span class="cov8" title="1">if result.RowsAffected == 0 </span><span class="cov0" title="0">{
                return errors.New("user not found")
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// DeactivateUser deactivates a user account
func (u *UserService) DeactivateUser(id uint) error <span class="cov8" title="1">{
        result := u.db.Model(&amp;models.User{}).Where("id = ?", id).Update("is_active", false)
        if result.Error != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to deactivate user: %w", result.Error)
        }</span>

        <span class="cov8" title="1">if result.RowsAffected == 0 </span><span class="cov0" title="0">{
                return errors.New("user not found")
        }</span>

        <span class="cov8" title="1">return nil</span>
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
