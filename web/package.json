{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@nuxt/ui": "^3.3.0", "@tailwindcss/vite": "^4.1.11", "@unhead/vue": "^2.0.14", "axios": "^1.11.0", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1", "zod": "^4.0.17"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "vite": "^7.1.2", "vue-tsc": "^3.0.5"}}