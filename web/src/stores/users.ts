import axios from 'axios'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { useNotifications } from '../composables/useNotifications'

interface User {
  id: number
  username: string
  email: string
  first_name: string
  last_name: string
  is_active: boolean
  roles?: Role[]
  created_at: string
  updated_at: string
}

interface Role {
  id: number
  name: string
  description: string
  is_active: boolean
}

interface Pagination {
  page: number
  limit: number
  total: number
}

export const useUsersStore = defineStore('users', () => {
  const users = ref<User[]>([])
  const currentUser = ref<User | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const pagination = ref<Pagination>({
    page: 1,
    limit: 10,
    total: 0
  })

  // 通知系统
  const { success, error: showError } = useNotifications()

  const fetchUsers = async (page = 1, limit = 10) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.get('/api/v1/users', {
        params: { page, limit }
      })
      
      users.value = response.data.users
      pagination.value = response.data.pagination
      
      return { success: true }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch users'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const fetchUser = async (id: number) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.get(`/api/v1/users/${id}`)
      currentUser.value = response.data
      
      return { success: true, user: response.data }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch user'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const createUser = async (userData: {
    username: string
    email: string
    password: string
    first_name: string
    last_name: string
  }) => {
    loading.value = true
    error.value = null

    try {
      const response = await axios.post('/api/v1/users', userData)

      // Add new user to the list
      users.value.unshift(response.data)

      // 显示成功通知
      success('用户创建成功', `用户 ${userData.first_name} ${userData.last_name} 已成功创建`)

      return { success: true, user: response.data }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to create user'
      error.value = errorMessage

      // 显示错误通知
      showError('创建用户失败', errorMessage)

      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const updateUser = async (id: number, userData: Partial<User>) => {
    loading.value = true
    error.value = null

    try {
      const response = await axios.put(`/api/v1/users/${id}`, userData)

      // Update user in the list
      const index = users.value.findIndex(user => user.id === id)
      if (index !== -1) {
        users.value[index] = { ...users.value[index], ...response.data }
      }

      // 显示成功通知
      success('用户更新成功', `用户信息已成功更新`)

      return { success: true, user: response.data }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to update user'
      error.value = errorMessage

      // 显示错误通知
      showError('更新用户失败', errorMessage)

      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const deleteUser = async (id: number) => {
    loading.value = true
    error.value = null

    try {
      await axios.delete(`/api/v1/users/${id}`)

      // Remove user from the list
      users.value = users.value.filter(user => user.id !== id)

      // 显示成功通知
      success('用户删除成功', '用户已成功删除')

      return { success: true }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to delete user'
      error.value = errorMessage

      // 显示错误通知
      showError('删除用户失败', errorMessage)

      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const assignRole = async (userId: number, roleId: number) => {
    loading.value = true
    error.value = null
    
    try {
      await axios.post(`/api/v1/users/${userId}/roles`, { role_id: roleId })
      
      return { success: true }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to assign role'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const removeRole = async (userId: number, roleId: number) => {
    loading.value = true
    error.value = null
    
    try {
      await axios.delete(`/api/v1/users/${userId}/roles/${roleId}`)
      
      return { success: true }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to remove role'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  // Computed properties for pagination
  const currentPage = computed(() => pagination.value.page)
  const pageSize = computed(() => pagination.value.limit)
  const total = computed(() => pagination.value.total)
  const hasNextPage = computed(() => currentPage.value * pageSize.value < total.value)
  const hasPreviousPage = computed(() => currentPage.value > 1)

  // Pagination methods
  const nextPage = async () => {
    if (hasNextPage.value) {
      await fetchUsers(currentPage.value + 1, pageSize.value)
    }
  }

  const previousPage = async () => {
    if (hasPreviousPage.value) {
      await fetchUsers(currentPage.value - 1, pageSize.value)
    }
  }

  const goToPage = async (page: number) => {
    await fetchUsers(page, pageSize.value)
  }

  return {
    users,
    currentUser,
    loading,
    error,
    pagination,
    currentPage,
    pageSize,
    total,
    hasNextPage,
    hasPreviousPage,
    fetchUsers,
    fetchUser,
    createUser,
    updateUser,
    deleteUser,
    assignRole,
    removeRole,
    nextPage,
    previousPage,
    goToPage
  }
})
