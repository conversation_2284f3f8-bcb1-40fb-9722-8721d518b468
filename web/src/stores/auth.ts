import axios from 'axios'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

interface User {
  id: number
  username: string
  email: string
  first_name: string
  last_name: string
  is_active: boolean
  roles?: Role[]
  attributes?: Attribute[]
  created_at: string
  updated_at: string
}

interface Role {
  id: number
  name: string
  description: string
  is_active: boolean
  permissions?: Permission[]
}

interface Permission {
  id: number
  name: string
  resource: string
  action: string
  description: string
}

interface Attribute {
  id: number
  user_id: number
  name: string
  value: string
  type: string
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const loading = ref(false)
  const error = ref<string | null>(null)

  const isAuthenticated = computed(() => !!token.value)
  
  const hasPermission = computed(() => (permission: string) => {
    if (!user.value || !user.value.roles) return false
    
    for (const role of user.value.roles) {
      if (role.permissions && role.permissions.some(p => p.name === permission)) {
        return true
      }
    }
    return false
  })

  const login = async (credentials: { identifier: string; password: string }) => {
    loading.value = true
    error.value = null

    try {
      const response = await axios.post('/api/v1/auth/login', credentials)
      const { token: newToken, user: userData } = response.data

      token.value = newToken
      user.value = userData
      localStorage.setItem('token', newToken)

      // Set default authorization header
      axios.defaults.headers.common['Authorization'] = `Bearer ${newToken}`

      // Fetch complete user profile with roles and permissions
      await fetchProfile()

      return { success: true }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Login failed'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const register = async (userData: {
    username: string
    email: string
    password: string
    first_name: string
    last_name: string
  }) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.post('/api/v1/auth/register', userData)
      const { token: newToken, user: newUser } = response.data
      
      token.value = newToken
      user.value = newUser
      localStorage.setItem('token', newToken)
      
      // Set default authorization header
      axios.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
      
      return { success: true }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Registration failed'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const fetchProfile = async () => {
    if (!token.value) return
    
    try {
      const response = await axios.get('/api/v1/auth/profile')
      user.value = response.data
    } catch (err) {
      console.error('Failed to fetch profile:', err)
      logout()
    }
  }

  const logout = () => {
    user.value = null
    token.value = null
    error.value = null
    localStorage.removeItem('token')
    delete axios.defaults.headers.common['Authorization']
  }

  const initializeAuth = () => {
    if (token.value) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token.value}`
      fetchProfile()
    }
  }

  return {
    user,
    token,
    loading,
    error,
    isAuthenticated,
    hasPermission,
    login,
    register,
    fetchProfile,
    logout,
    initializeAuth
  }
})
