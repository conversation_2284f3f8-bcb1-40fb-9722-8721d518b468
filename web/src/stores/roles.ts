import axios from 'axios'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useNotifications } from '../composables/useNotifications'

interface Role {
  id: number
  name: string
  description: string
  is_active: boolean
  permissions?: Permission[]
  created_at: string
  updated_at: string
}

interface Permission {
  id: number
  name: string
  resource: string
  action: string
  description: string
}

interface CreateRoleData {
  name: string
  description?: string
  is_active?: boolean
}

export const useRolesStore = defineStore('roles', () => {
  const roles = ref<Role[]>([])
  const currentRole = ref<Role | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 通知系统
  const { success, error: showError } = useNotifications()

  const fetchRoles = async () => {
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.get('/api/v1/roles')
      roles.value = response.data
      
      return { success: true }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch roles'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const fetchRole = async (id: number) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.get(`/api/v1/roles/${id}`)
      currentRole.value = response.data
      
      return { success: true, role: response.data }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch role'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const createRole = async (roleData: CreateRoleData) => {
    loading.value = true
    error.value = null

    try {
      const response = await axios.post('/api/v1/roles', roleData)

      // Add new role to the list
      roles.value.unshift(response.data)

      // 显示成功通知
      success('角色创建成功', `角色 ${roleData.name} 已成功创建`)

      return { success: true, role: response.data }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to create role'
      error.value = errorMessage

      // 显示错误通知
      showError('创建角色失败', errorMessage)

      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const updateRole = async (id: number, roleData: Partial<Role>) => {
    loading.value = true
    error.value = null

    try {
      const response = await axios.put(`/api/v1/roles/${id}`, roleData)

      // Update role in the list
      const index = roles.value.findIndex(role => role.id === id)
      if (index !== -1) {
        roles.value[index] = { ...roles.value[index], ...response.data }
      }

      // 显示成功通知
      success('角色更新成功', `角色信息已成功更新`)

      return { success: true, role: response.data }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to update role'
      error.value = errorMessage

      // 显示错误通知
      showError('更新角色失败', errorMessage)

      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const deleteRole = async (id: number) => {
    loading.value = true
    error.value = null

    try {
      await axios.delete(`/api/v1/roles/${id}`)

      // Remove role from the list
      roles.value = roles.value.filter(role => role.id !== id)

      // 显示成功通知
      success('角色删除成功', '角色已成功删除')

      return { success: true }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to delete role'
      error.value = errorMessage

      // 显示错误通知
      showError('删除角色失败', errorMessage)

      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const assignPermission = async (roleId: number, permissionId: number) => {
    loading.value = true
    error.value = null
    
    try {
      await axios.post(`/api/v1/roles/${roleId}/permissions`, { permission_id: permissionId })
      
      return { success: true }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to assign permission'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const removePermission = async (roleId: number, permissionId: number) => {
    loading.value = true
    error.value = null
    
    try {
      await axios.delete(`/api/v1/roles/${roleId}/permissions/${permissionId}`)
      
      return { success: true }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to remove permission'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  return {
    roles,
    currentRole,
    loading,
    error,
    fetchRoles,
    fetchRole,
    createRole,
    updateRole,
    deleteRole,
    assignPermission,
    removePermission
  }
})
