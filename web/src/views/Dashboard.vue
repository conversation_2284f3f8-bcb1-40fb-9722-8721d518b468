<template>
  <Layout>
    <div>
      <div class="bg-white rounded-lg shadow-sm p-8">
        <div class="text-center">
          <h1 class="text-3xl font-bold text-gray-900 mb-4">
            欢迎使用管理系统
          </h1>
          <p class="text-lg text-gray-600 mb-8">
            您好，{{ authStore.user?.first_name }} {{ authStore.user?.last_name }}！
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <!-- Users Card -->
            <div v-if="authStore.hasPermission('users.read')" class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">
                        用户管理
                      </dt>
                      <dd>
                        <div class="text-lg font-medium text-gray-900">
                          管理用户
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                  <router-link to="/users" class="font-medium text-blue-700 hover:text-blue-900">
                    查看所有用户
                  </router-link>
                </div>
              </div>
            </div>

            <!-- Roles Card -->
            <div v-if="authStore.hasPermission('roles.read')" class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">
                        角色管理
                      </dt>
                      <dd>
                        <div class="text-lg font-medium text-gray-900">
                          管理角色
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                  <router-link to="/roles" class="font-medium text-blue-700 hover:text-blue-900">
                    查看所有角色
                  </router-link>
                </div>
              </div>
            </div>

            <!-- Profile Card -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">
                        您的资料
                      </dt>
                      <dd>
                        <div class="text-lg font-medium text-gray-900">
                          个人设置
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                  <router-link to="/profile" class="font-medium text-blue-700 hover:text-blue-900">
                    查看资料
                  </router-link>
                </div>
              </div>
            </div>
          </div>

          <!-- User Info -->
          <div class="mt-8 bg-white shadow rounded-lg p-6 max-w-2xl mx-auto">
            <h2 class="text-lg font-medium text-gray-900 mb-4">您的账户信息</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
              <div>
                <dt class="text-sm font-medium text-gray-500">用户名</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ authStore.user?.username }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">邮箱</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ authStore.user?.email }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">角色</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <span v-if="authStore.user?.roles?.length" class="space-x-2">
                    <span
                      v-for="role in authStore.user.roles"
                      :key="role.id"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {{ role.name }}
                    </span>
                  </span>
                  <span v-else class="text-gray-400">未分配角色</span>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">账户状态</dt>
                <dd class="mt-1 text-sm">
                  <span
                    :class="authStore.user?.is_active ? 'text-green-800 bg-green-100' : 'text-red-800 bg-red-100'"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  >
                    {{ authStore.user?.is_active ? '激活' : '未激活' }}
                  </span>
                </dd>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import Layout from '../components/Layout.vue'
import { useAuthStore } from '../stores/auth'

const authStore = useAuthStore()
</script>
