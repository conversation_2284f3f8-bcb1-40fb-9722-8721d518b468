<template>
  <Layout>
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-xl font-semibold text-gray-900">角色</h1>
          <p class="mt-2 text-sm text-gray-700">
            管理系统中的角色和权限。
          </p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <UButton
            @click="openCreateModal"
            color="primary"
            size="lg"
          >
            添加角色
          </UButton>
        </div>
      </div>

      <!-- Roles Grid -->
      <div class="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <div
          v-for="role in rolesStore.roles"
          :key="role.id"
          class="bg-white overflow-hidden shadow rounded-lg"
        >
          <div class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    {{ role.name }}
                  </dt>
                  <dd>
                    <div class="text-lg font-medium text-gray-900">
                      {{ role.description }}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
            <div class="mt-4">
              <div class="text-sm text-gray-500">
                <span
                  :class="role.is_active ? 'text-green-800 bg-green-100' : 'text-red-800 bg-red-100'"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                >
                  {{ role.is_active ? '激活' : '未激活' }}
                </span>
              </div>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  {{ role.permissions?.length || 0 }} 个权限
                </p>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-6 py-3">
            <div class="text-sm">
              <button
                v-if="authStore.hasPermission('roles.update')"
                @click="editRole(role)"
                class="font-medium text-blue-600 hover:text-blue-500 mr-4"
              >
                编辑
              </button>
              <button
                v-if="authStore.hasPermission('roles.delete')"
                @click="deleteRole(role)"
                class="font-medium text-red-600 hover:text-red-500"
              >
                删除
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Role Modal -->
    <UModal v-model="showCreateModal" title="title" description="description">
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              {{ editingRole ? '编辑角色' : '创建角色' }}
            </h3>
          </div>
        </template>

        <UForm
          :state="roleForm"
          @submit="submitRole"
          class="space-y-4"
        >
          <UFormField label="角色名称" name="name" required>
            <UInput
              v-model="roleForm.name"
              placeholder="请输入角色名称"
            />
          </UFormField>

          <UFormField label="描述" name="description">
            <UTextarea
              v-model="roleForm.description"
              placeholder="请输入角色描述"
              :rows="3"
            />
          </UFormField>

          <UFormField label="状态" name="is_active">
            <UCheckbox
              v-model="roleForm.is_active"
              label="激活角色"
            />
          </UFormField>
        </UForm>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton
              color="neutral"
              variant="ghost"
              @click="closeModal"
            >
              取消
            </UButton>
            <UButton
              :loading="rolesStore.loading"
              @click="submitRole"
            >
              {{ editingRole ? '更新' : '创建' }}
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import Layout from '../components/Layout.vue'
import { useAuthStore } from '../stores/auth'
import { useRolesStore } from '../stores/roles'
import { type CreateRoleForm } from '../schemas/user'

const authStore = useAuthStore()
const rolesStore = useRolesStore()
const showCreateModal = ref(false)
const editingRole = ref<any>(null)

const roleForm = reactive<CreateRoleForm>({
  name: '',
  description: '',
  is_active: true
})

const openCreateModal = () => {
  editingRole.value = null
  resetForm()
  showCreateModal.value = true
}

const editRole = (role: any) => {
  editingRole.value = role
  Object.assign(roleForm, {
    name: role.name,
    description: role.description || '',
    is_active: role.is_active
  })
  showCreateModal.value = true
}

const closeModal = () => {
  showCreateModal.value = false
  editingRole.value = null
  resetForm()
}

const resetForm = () => {
  Object.assign(roleForm, {
    name: '',
    description: '',
    is_active: true
  })
}

const submitRole = async () => {
  try {
    if (editingRole.value) {
      await rolesStore.updateRole(editingRole.value.id, roleForm)
    } else {
      await rolesStore.createRole(roleForm)
    }
    closeModal()
    await rolesStore.fetchRoles()
  } catch (error) {
    console.error('Error saving role:', error)
  }
}

const deleteRole = async (role: any) => {
  if (confirm(`确定要删除角色 ${role.name} 吗？`)) {
    try {
      await rolesStore.deleteRole(role.id)
      await rolesStore.fetchRoles()
    } catch (error) {
      console.error('Error deleting role:', error)
    }
  }
}

onMounted(() => {
  rolesStore.fetchRoles()
})
</script>
