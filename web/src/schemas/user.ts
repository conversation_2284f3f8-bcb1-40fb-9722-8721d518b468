import { z } from 'zod'

// 用户创建表单验证模式
export const createUserSchema = z.object({
  username: z
    .string()
    .min(3, '用户名至少需要3个字符')
    .max(50, '用户名不能超过50个字符')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
  
  email: z
    .string()
    .email('请输入有效的邮箱地址'),
  
  first_name: z
    .string()
    .min(1, '名字不能为空')
    .max(50, '名字不能超过50个字符'),
  
  last_name: z
    .string()
    .min(1, '姓氏不能为空')
    .max(50, '姓氏不能超过50个字符'),
  
  password: z
    .string()
    .min(8, '密码至少需要8个字符')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      '密码必须包含至少一个大写字母、一个小写字母、一个数字和一个特殊字符'
    ),
  
  is_active: z.boolean().default(true)
})

// 用户更新表单验证模式（密码可选）
export const updateUserSchema = z.object({
  id: z.number().optional(),
  username: z
    .string()
    .min(3, '用户名至少需要3个字符')
    .max(50, '用户名不能超过50个字符')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
  
  email: z
    .string()
    .email('请输入有效的邮箱地址'),
  
  first_name: z
    .string()
    .min(1, '名字不能为空')
    .max(50, '名字不能超过50个字符'),
  
  last_name: z
    .string()
    .min(1, '姓氏不能为空')
    .max(50, '姓氏不能超过50个字符'),
  
  password: z
    .string()
    .min(8, '密码至少需要8个字符')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      '密码必须包含至少一个大写字母、一个小写字母、一个数字和一个特殊字符'
    )
    .optional(),
  
  is_active: z.boolean().default(true)
})

// 登录表单验证模式
export const loginSchema = z.object({
  identifier: z
    .string()
    .min(1, '请输入用户名或邮箱'),
  
  password: z
    .string()
    .min(1, '请输入密码')
})

// 角色创建表单验证模式
export const createRoleSchema = z.object({
  name: z
    .string()
    .min(1, '角色名称不能为空')
    .max(50, '角色名称不能超过50个字符')
    .regex(/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, '角色名称只能包含字母、数字、下划线和中文'),
  
  description: z
    .string()
    .max(255, '描述不能超过255个字符')
    .optional(),
  
  is_active: z.boolean().default(true)
})

// TypeScript 类型推导
export type CreateUserForm = z.infer<typeof createUserSchema>
export type UpdateUserForm = z.infer<typeof updateUserSchema>
export type LoginForm = z.infer<typeof loginSchema>
export type CreateRoleForm = z.infer<typeof createRoleSchema>

// 用户数据类型
export interface User {
  id: number
  username: string
  email: string
  first_name: string
  last_name: string
  is_active: boolean
  created_at: string
  updated_at: string
  roles?: Role[]
}

// 角色数据类型
export interface Role {
  id: number
  name: string
  description?: string
  is_active: boolean
  created_at: string
  updated_at: string
  permissions?: Permission[]
}

// 权限数据类型
export interface Permission {
  id: number
  name: string
  resource: string
  action: string
  description?: string
}
