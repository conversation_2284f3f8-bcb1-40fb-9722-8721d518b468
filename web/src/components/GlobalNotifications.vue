<template>
  <div class="fixed top-4 right-4 z-50 space-y-2">
    <UNotification
      v-for="notification in notifications"
      :key="notification.id"
      :title="notification.title"
      :description="notification.description"
      :color="notification.color"
      :actions="notification.actions"
      @close="remove(notification.id)"
    />
  </div>
</template>

<script setup lang="ts">
import { useNotifications } from '../composables/useNotifications'

const { notifications, remove } = useNotifications()
</script>
