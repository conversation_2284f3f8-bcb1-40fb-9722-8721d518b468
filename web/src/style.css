@import "tailwindcss";
@import "@nuxt/ui";

@layer base {
  html {
    font-family: system-ui, sans-serif;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
  }

  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  /* Sidebar styles */
  .sidebar-nav-item {
    @apply flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  }

  .sidebar-nav-item.active {
    @apply bg-gray-700 text-white;
  }

  .sidebar-nav-item:not(.active) {
    @apply text-gray-300 hover:bg-gray-700 hover:text-white;
  }

  /* Smooth scrollbar for main content */
  .main-content {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f7fafc;
  }

  .main-content::-webkit-scrollbar {
    width: 6px;
  }

  .main-content::-webkit-scrollbar-track {
    background: #f7fafc;
  }

  .main-content::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
  }

  .main-content::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
  }
}