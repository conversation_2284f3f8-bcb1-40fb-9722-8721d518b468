package main

import (
	"context"
	"embed"
	"io/fs"
	"log"
	"net/http"
	"os"
	"os/signal"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	echoSwagger "github.com/swaggo/echo-swagger"

	_ "xiaoxingcloud.com/admin/docs" // Import docs for swagger
	"xiaoxingcloud.com/admin/internal/auth"
	"xiaoxingcloud.com/admin/internal/config"
	"xiaoxingcloud.com/admin/internal/database"
	"xiaoxingcloud.com/admin/internal/handlers"
	authMiddleware "xiaoxingcloud.com/admin/internal/middleware"
	"xiaoxingcloud.com/admin/internal/services"
	"xiaoxingcloud.com/admin/pkg"
)

//go:embed web/dist
var frontendFS embed.FS

// @title Admin System API
// @version 1.0
// @description This is a comprehensive admin system API with RBAC/ABAC authorization, user management, notifications, and more.
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load configuration:", err)
	}

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		log.Fatal("Configuration validation failed:", err)
	}

	// Print configuration
	cfg.PrintConfig()

	// Connect to database
	if err := database.Connect(cfg); err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Run migrations
	if err := database.Migrate(); err != nil {
		log.Fatal("Failed to run migrations:", err)
	}

	// Seed database
	if err := database.Seed(); err != nil {
		log.Fatal("Failed to seed database:", err)
	}

	// Initialize services
	db := database.GetDB()
	userService := services.NewUserService(db)
	rbacService := services.NewRBACService(db)
	abacService := services.NewABACService(db)
	roleService := services.NewRoleService(db)
	permissionService := services.NewPermissionService(db)
	auditService := services.NewAuditService(db)
	settingService := services.NewSettingService(db)
	menuService := services.NewMenuService(db)
	notificationService := services.NewNotificationService(db)
	jwtService := auth.NewJWTService(cfg)

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(userService, jwtService)
	userHandler := handlers.NewUserHandler(userService, rbacService)
	roleHandler := handlers.NewRoleHandler(roleService)
	permissionHandler := handlers.NewPermissionHandler(permissionService)
	auditHandler := handlers.NewAuditHandler(auditService)
	settingHandler := handlers.NewSettingHandler(settingService)
	menuHandler := handlers.NewMenuHandler(menuService)
	notificationHandler := handlers.NewNotificationHandler(notificationService)

	// Initialize middleware
	authMW := authMiddleware.NewAuthMiddleware(jwtService, rbacService, abacService)

	// Initialize Echo
	e := echo.New()

	// Set validator
	e.Validator = pkg.NewValidator()

	// Middleware
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	e.Use(middleware.CORS())
	e.Use(middleware.RequestID())

	// Serve embedded frontend
	frontendSubFS, err := fs.Sub(frontendFS, "web/dist")
	if err != nil {
		log.Fatal("Failed to create frontend sub filesystem:", err)
	}
	e.Use(middleware.StaticWithConfig(middleware.StaticConfig{
		Root:       "/",
		Filesystem: http.FS(frontendSubFS),
		HTML5:      true,
	}))

	// API routes
	api := e.Group("/api/v1")

	// Public routes
	auth := api.Group("/auth")
	auth.POST("/login", authHandler.Login)
	auth.POST("/register", authHandler.Register)
	auth.POST("/refresh", authHandler.RefreshToken)

	// Protected routes
	protected := api.Group("")
	protected.Use(authMW.JWTAuth())

	// Auth protected routes
	protected.GET("/auth/profile", authHandler.GetProfile)
	protected.POST("/auth/logout", authHandler.Logout)

	// User management routes (require admin permissions)
	users := protected.Group("/users")
	users.Use(authMW.RequirePermission("users", "read"))
	users.GET("", userHandler.GetUsers)
	users.GET("/:id", userHandler.GetUser)

	// User creation/modification (require admin permissions)
	users.POST("", userHandler.CreateUser, authMW.RequirePermission("users", "create"))
	users.PUT("/:id", userHandler.UpdateUser, authMW.RequirePermission("users", "update"))
	users.DELETE("/:id", userHandler.DeleteUser, authMW.RequirePermission("users", "delete"))

	// Role assignment (require admin permissions)
	users.POST("/:id/roles", userHandler.AssignRole, authMW.RequirePermission("roles", "update"))
	users.DELETE("/:id/roles/:roleId", userHandler.RemoveRole, authMW.RequirePermission("roles", "update"))

	// Role management routes (require admin permissions)
	roles := protected.Group("/roles")
	roles.Use(authMW.RequirePermission("roles", "read"))
	roles.GET("", roleHandler.GetRoles)
	roles.GET("/:id", roleHandler.GetRole)
	roles.POST("", roleHandler.CreateRole, authMW.RequirePermission("roles", "create"))
	roles.PUT("/:id", roleHandler.UpdateRole, authMW.RequirePermission("roles", "update"))
	roles.DELETE("/:id", roleHandler.DeleteRole, authMW.RequirePermission("roles", "delete"))
	roles.POST("/:id/permissions", roleHandler.AssignPermission, authMW.RequirePermission("roles", "update"))
	roles.DELETE("/:id/permissions/:permissionId", roleHandler.RemovePermission, authMW.RequirePermission("roles", "update"))
	roles.GET("/:id/permissions", roleHandler.GetRolePermissions, authMW.RequirePermission("roles", "read"))

	// Permission management routes (require admin permissions)
	permissions := protected.Group("/permissions")
	permissions.Use(authMW.RequirePermission("permissions", "read"))
	permissions.GET("", permissionHandler.GetPermissions)
	permissions.GET("/:id", permissionHandler.GetPermission)
	permissions.POST("", permissionHandler.CreatePermission, authMW.RequirePermission("permissions", "create"))
	permissions.PUT("/:id", permissionHandler.UpdatePermission, authMW.RequirePermission("permissions", "update"))
	permissions.DELETE("/:id", permissionHandler.DeletePermission, authMW.RequirePermission("permissions", "delete"))
	permissions.GET("/resources", permissionHandler.GetResourceList)
	permissions.GET("/resources/:resource", permissionHandler.GetPermissionsByResource)
	permissions.GET("/grouped", permissionHandler.GetPermissionsGrouped)

	// Audit log routes (require admin permissions)
	audit := protected.Group("/audit")
	audit.Use(authMW.RequirePermission("system", "admin"))
	audit.GET("/logs", auditHandler.GetAuditLogs)
	audit.GET("/logs/users/:userId", auditHandler.GetUserAuditLogs)
	audit.GET("/stats", auditHandler.GetAuditStats)
	audit.DELETE("/cleanup", auditHandler.CleanupOldLogs)
	audit.GET("/actions", auditHandler.GetAuditActions)
	audit.GET("/resources", auditHandler.GetAuditResources)

	// Personal audit logs (users can view their own)
	protected.GET("/audit/my-logs", auditHandler.GetMyAuditLogs)

	// System settings routes (require admin permissions)
	settings := protected.Group("/settings")
	settings.Use(authMW.RequirePermission("system", "admin"))
	settings.GET("", settingHandler.GetSettings)
	settings.GET("/:key", settingHandler.GetSetting)
	settings.POST("", settingHandler.CreateSetting)
	settings.PUT("/:key", settingHandler.UpdateSetting)
	settings.DELETE("/:key", settingHandler.DeleteSetting)
	settings.GET("/categories", settingHandler.GetCategories)
	settings.POST("/bulk", settingHandler.BulkUpdateSettings)
	settings.POST("/initialize", settingHandler.InitializeDefaults)

	// Public settings (no authentication required)
	e.GET("/api/v1/settings/public", settingHandler.GetPublicSettings)

	// Menu management routes (require admin permissions)
	menus := protected.Group("/menus")
	menus.Use(authMW.RequirePermission("system", "admin"))
	menus.GET("", menuHandler.GetMenus)
	menus.GET("/:id", menuHandler.GetMenu)
	menus.POST("", menuHandler.CreateMenu)
	menus.PUT("/:id", menuHandler.UpdateMenu)
	menus.DELETE("/:id", menuHandler.DeleteMenu)
	menus.POST("/initialize", menuHandler.InitializeDefaults)

	// User menus (accessible to authenticated users)
	protected.GET("/menus/my-menus", menuHandler.GetUserMenus)

	// Notification routes
	notifications := protected.Group("/notifications")
	notifications.GET("", notificationHandler.GetMyNotifications)
	notifications.GET("/:id", notificationHandler.GetNotification)
	notifications.PUT("/:id/read", notificationHandler.MarkAsRead)
	notifications.PUT("/read-all", notificationHandler.MarkAllAsRead)
	notifications.DELETE("/:id", notificationHandler.DeleteNotification)
	notifications.GET("/stats", notificationHandler.GetMyNotificationStats)

	// Admin notification routes
	adminNotifications := protected.Group("/admin/notifications")
	adminNotifications.Use(authMW.RequirePermission("system", "admin"))
	adminNotifications.POST("", notificationHandler.CreateNotification)
	adminNotifications.POST("/broadcast", notificationHandler.BroadcastNotification)
	adminNotifications.DELETE("/cleanup", notificationHandler.CleanupExpired)

	// Swagger documentation
	e.GET("/swagger/*", echoSwagger.WrapHandler)

	// Health check
	e.GET("/health", func(c echo.Context) error {
		return c.JSON(http.StatusOK, map[string]any{
			"status":    "healthy",
			"timestamp": time.Now().Format(time.RFC3339),
			"version":   "1.0.0",
			"message":   "Admin system is running 🔥",
		})
	})

	// Start server with graceful shutdown
	address := cfg.Server.Host + ":" + cfg.Server.Port
	log.Printf("Server starting on %s", address)

	// Start server in a goroutine
	go func() {
		if err := e.Start(address); err != nil && err != http.ErrServerClosed {
			log.Fatal("Failed to start server:", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server with a timeout of 10 seconds
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt)
	<-quit

	log.Println("Shutting down server...")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := e.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("Server exited gracefully")
}
