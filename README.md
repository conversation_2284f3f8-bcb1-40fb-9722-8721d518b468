# Internal Company Admin Management System

A comprehensive internal admin management system built with Go + Vue.js, featuring RBAC (Role-Based Access Control) and ABAC (Attribute-Based Access Control) authorization systems.

## Features

- **Backend**: Go with Echo framework
- **Frontend**: Vue.js 3 with Tailwind CSS
- **Database**: MySQL with GORM ORM
- **Authentication**: JWT-based authentication
- **Authorization**: RBAC and ABAC systems
- **Deployment**: Docker Compose for containerized deployment
- **Embedded Frontend**: Vue frontend embedded into Go binary

## Architecture

### Backend (Go + Echo)
- RESTful API with Echo framework
- JWT authentication and authorization
- RBAC and ABAC permission systems
- MySQL database with GORM
- Embedded frontend assets

### Frontend (Vue.js + TypeScript)
- Vue.js 3 with Composition API and TypeScript
- Vite 7.x for fast development and building
- pnpm for package management
- Tailwind CSS 3.x for styling
- Pinia for state management
- Vue Router 4 for navigation
- Axios for API communication

### Authorization Systems
- **RBAC**: Role-based access control with roles and permissions
- **ABAC**: Attribute-based access control with policies and conditions
- **User Management**: Create, update, delete users and assign roles
- **Permission Groups**: Organize permissions into logical groups

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for development)
- pnpm (for frontend package management)
- Go 1.18+ (for development)

### Development Setup

1. **Clone and setup the project:**
```bash
git clone <repository-url>
cd admin-system
```

2. **Install dependencies:**
```bash
make install-deps
# Or manually:
# go mod tidy
# cd web && npm ci
```

3. **Start MySQL database:**
```bash
docker-compose up mysql -d
```

4. **Start development server with hot reloading:**
```bash
make dev
# Or directly:
# ./scripts/dev.sh
```

This will:
- Install Air (Go hot reloading tool) if not present
- Build the frontend
- Start the backend with hot reloading enabled
- Watch for Go file changes and automatically restart the server

### 📚 API Documentation

The application includes comprehensive API documentation powered by Swagger:

- **Swagger UI**: `http://localhost:8080/swagger/index.html`
- **OpenAPI JSON**: `http://localhost:8080/swagger/doc.json`

To regenerate API documentation after making changes:
```bash
make swagger
```

### 🛡️ Graceful Shutdown

The application supports graceful shutdown to ensure:
- All ongoing requests are completed
- Database connections are properly closed
- Resources are cleaned up properly

To test graceful shutdown:
```bash
# Start the server
make run

# In another terminal, send interrupt signal
kill -INT <process_id>
# Or simply press Ctrl+C
```

The server will:
1. Stop accepting new requests
2. Wait for ongoing requests to complete (up to 10 seconds)
3. Close database connections
4. Exit cleanly

5. **Alternative: Manual development setup:**
```bash
# Run backend with hot reloading
air -c .air.toml

# Or run frontend separately (in another terminal)
cd web
npm run dev
```

### Production Deployment

1. **Deploy with Docker Compose:**
```bash
docker-compose up -d
```

This will:
- Build the frontend and embed it into the Go binary
- Create a production-ready Docker image
- Start MySQL database
- Start the application on port 8080

2. **Access the application:**
- Open http://localhost:8080 in your browser
- Default admin credentials will be created during first run

## Configuration

The application uses [Viper](https://github.com/spf13/viper) for configuration management, supporting multiple configuration sources:

1. **Configuration file** (config.yaml)
2. **Environment variables** (with ADMIN_ prefix)
3. **Default values**

### Configuration Methods

#### 1. Using Configuration File

Copy `config.example.yaml` to `config.yaml` and modify:

```yaml
server:
  host: "0.0.0.0"
  port: "8080"

database:
  host: "localhost"
  port: "3306"
  user: "root"
  password: "password"
  dbname: "admin_system"
  charset: "utf8mb4"

jwt:
  secret: "your-super-secret-jwt-key-change-in-production"
  expire_hours: 24
```

#### 2. Using Environment Variables

Set environment variables with `ADMIN_` prefix:

```env
# Server Configuration
ADMIN_SERVER_HOST=0.0.0.0
ADMIN_SERVER_PORT=8080

# Database Configuration
ADMIN_DATABASE_HOST=localhost
ADMIN_DATABASE_PORT=3306
ADMIN_DATABASE_USER=root
ADMIN_DATABASE_PASSWORD=password
ADMIN_DATABASE_DBNAME=admin_system
ADMIN_DATABASE_CHARSET=utf8mb4

# JWT Configuration
ADMIN_JWT_SECRET=your-super-secret-jwt-key-change-in-production
ADMIN_JWT_EXPIRE_HOURS=24
```

**Note**: Environment variables take precedence over configuration file values.

#### 3. Configuration Priority

Viper loads configuration in the following order (highest to lowest priority):

1. Environment variables (with `ADMIN_` prefix)
2. Configuration file (`config.yaml`)
3. Default values

#### 4. Quick Setup

For development, copy the example files:

```bash
# Copy configuration examples
cp config.example.yaml config.yaml
cp .env.example .env

# Edit the files with your database credentials
nano .env  # or your preferred editor
```

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `GET /api/v1/auth/profile` - Get user profile
- `POST /api/v1/auth/refresh` - Refresh JWT token
- `POST /api/v1/auth/logout` - User logout

### User Management
- `GET /api/v1/users` - List users (requires `users.read`)
- `POST /api/v1/users` - Create user (requires `users.create`)
- `GET /api/v1/users/:id` - Get user details (requires `users.read`)
- `PUT /api/v1/users/:id` - Update user (requires `users.update`)
- `DELETE /api/v1/users/:id` - Delete user (requires `users.delete`)

### Role Management
- `POST /api/v1/users/:id/roles` - Assign role to user (requires `roles.update`)
- `DELETE /api/v1/users/:id/roles/:roleId` - Remove role from user (requires `roles.update`)

## Default Roles and Permissions

### Roles
- **admin**: Full system access
- **manager**: User management access
- **user**: Limited access

### Permissions
- `users.create`, `users.read`, `users.update`, `users.delete`
- `roles.create`, `roles.read`, `roles.update`, `roles.delete`
- `permissions.read`
- `system.admin`

## Security Features

- **Password Security**: Bcrypt hashing with strength validation
- **JWT Security**: Secure token generation and validation
- **RBAC**: Role-based permission checking
- **ABAC**: Attribute-based policy evaluation
- **Input Validation**: Request validation and sanitization
- **CORS**: Cross-origin resource sharing configuration

## Development

### Development Tools

This project includes several tools to improve the development experience:

#### Hot Reloading with Air
- **Air**: Automatically restarts the Go server when files change
- **Configuration**: `.air.toml` - customized for this project
- **Usage**: `make dev` or `./scripts/dev.sh`

#### Available Make Commands
```bash
make dev           # Start development server with hot reloading
make build         # Build the application for production
make run           # Run the built application
make install-deps  # Install all dependencies (Go + frontend)
make build-frontend# Build frontend only
make clean         # Clean build artifacts
make test          # Run Go tests
make docker-up     # Start production Docker containers
make docker-down   # Stop Docker containers
make help          # Show all available commands
```

#### Development Workflow
1. Start database: `docker-compose up mysql -d`
2. Start development server: `make dev`
3. Edit Go files - server automatically restarts
4. Edit frontend files - rebuild with `make build-frontend`

### Project Structure
```
admin-system/
├── main.go                      # Application entry point
├── internal/
│   ├── auth/                    # Authentication services
│   ├── config/                  # Configuration management
│   ├── database/                # Database connection and migrations
│   ├── handlers/                # HTTP handlers
│   ├── middleware/              # HTTP middleware
│   ├── models/                  # Database models
│   └── services/                # Business logic services
├── pkg/                         # Shared packages
├── web/
│   ├── src/
│   │   ├── components/          # Vue components
│   │   ├── views/               # Vue pages
│   │   ├── stores/              # Pinia stores
│   │   └── router/              # Vue Router configuration
│   └── package.json
├── docker-compose.yml           # Docker Compose configuration
├── Dockerfile                   # Multi-stage Docker build
└── README.md
```

### Adding New Features

1. **Backend**: Add new handlers, services, and models in respective directories
2. **Frontend**: Add new components and views in the frontend structure
3. **Permissions**: Define new permissions in the database seeder
4. **Routes**: Add new API routes in main.go and frontend routes in router/index.js

## Troubleshooting

### Common Issues

1. **Database Connection**: Ensure MySQL is running and credentials are correct
2. **Port Conflicts**: Change ports in docker-compose.yml if needed
3. **Permission Denied**: Check user roles and permissions in the database
4. **Build Errors**: Ensure all dependencies are installed correctly

### Logs

- **Application Logs**: Check Docker container logs with `docker-compose logs app`
- **Database Logs**: Check MySQL logs with `docker-compose logs mysql`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
