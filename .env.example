# Admin System Environment Variables Example
# Copy this file to .env and modify the values as needed
# These variables will be automatically loaded by Viper with ADMIN_ prefix

# Server Configuration
ADMIN_SERVER_HOST=0.0.0.0
ADMIN_SERVER_PORT=8080

# Database Configuration
ADMIN_DATABASE_HOST=localhost
ADMIN_DATABASE_PORT=3306
ADMIN_DATABASE_USER=root
ADMIN_DATABASE_PASSWORD=your_mysql_password_here
ADMIN_DATABASE_DBNAME=admin_system
ADMIN_DATABASE_CHARSET=utf8mb4

# JWT Configuration
ADMIN_JWT_SECRET=your-super-secret-jwt-key-change-in-production-min-32-chars
ADMIN_JWT_EXPIRE_HOURS=24
