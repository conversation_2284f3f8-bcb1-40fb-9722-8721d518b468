.PHONY: dev build run clean install-deps build-frontend test docker-up docker-down

# Development with hot reloading
dev:
	@echo "🚀 Starting development with hot reloading..."
	@./scripts/dev.sh

# Install Air if not present
install-air:
	@if ! command -v air >/dev/null 2>&1; then \
		echo "📦 Installing Air..."; \
		go install github.com/air-verse/air@latest; \
	else \
		echo "✅ Air is already installed"; \
	fi

# Install all dependencies
install-deps:
	@echo "📦 Installing Go dependencies..."
	@go mod download
	@echo "📦 Installing frontend dependencies..."
	@cd web && npm ci

# Build frontend
build-frontend:
	@echo "🏗️  Building frontend..."
	@cd web && npm run build

# Build the application
build: build-frontend
	@echo "🏗️  Building application..."
	@go build -o bin/main .

# Run the application (production mode)
run: build
	@echo "🚀 Starting application..."
	@./bin/main

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	@rm -rf bin/
	@rm -rf tmp/
	@rm -rf web/dist/

# Run tests
test:
	@echo "🧪 Running tests..."
	@go test ./...

# Docker commands for production
docker-up:
	@echo "🐳 Starting production containers..."
	@docker-compose up -d

docker-down:
	@echo "🛑 Stopping containers..."
	@docker-compose down

# Swagger documentation
swagger:
	@echo "📚 Generating Swagger documentation..."
	@~/go/bin/swag init
	@echo "✅ Swagger docs generated in docs/"

# Install development tools
install-tools:
	@echo "🔧 Installing development tools..."
	@go install github.com/swaggo/swag/cmd/swag@latest
	@go install github.com/cosmtrek/air@latest
	@echo "✅ Development tools installed"

# Test with coverage
test-coverage:
	@echo "🧪 Running tests with coverage..."
	@go test -v -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html
	@echo "✅ Coverage report generated: coverage.html"

# Format code
format:
	@echo "🎨 Formatting code..."
	@go fmt ./...
	@echo "✅ Code formatted"

# Setup development environment
setup: install-deps install-tools swagger
	@echo "🎉 Development environment setup complete!"
	@echo "Run 'make dev' to start development server"

# Help
help:
	@echo "Available commands:"
	@echo "  dev           - Start development server with hot reloading"
	@echo "  build         - Build the application"
	@echo "  run           - Run the built application"
	@echo "  install-deps  - Install all dependencies"
	@echo "  install-tools - Install development tools (swag, air)"
	@echo "  build-frontend- Build frontend only"
	@echo "  swagger       - Generate Swagger documentation"
	@echo "  clean         - Clean build artifacts"
	@echo "  test          - Run tests"
	@echo "  test-coverage - Run tests with coverage report"
	@echo "  format        - Format Go code"
	@echo "  setup         - Setup complete development environment"
	@echo "  docker-up     - Start production Docker containers"
	@echo "  docker-down   - Stop Docker containers"
